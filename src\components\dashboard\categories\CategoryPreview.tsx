import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Folder,
  Briefcase,
  Book,
  Brain,
  Edit3,
  Target,
  BarChart3,
  Home,
  Heart,
  Star,
  Lightbulb,
  Camera,
  Music,
  Plane,
  ShoppingCart,
  Coffee,
  Gamepad2,
  Dumbbell,
  Car,
  Smartphone,
  Laptop,
  Palette,
  Clock,
  Calendar,
  FileText,
  Calendar as CalendarIcon
} from "lucide-react"

// Icon mapping
const iconMap: { [key: string]: React.ElementType } = {
  'folder': Folder,
  'briefcase': Briefcase,
  'book': Book,
  'brain': Brain,
  'edit': Edit3,
  'target': Target,
  'bar-chart': BarChart3,
  'home': Home,
  'heart': Heart,
  'star': Star,
  'lightbulb': Lightbulb,
  'camera': Camera,
  'music': Music,
  'plane': Plane,
  'shopping-cart': ShoppingCart,
  'coffee': Coffee,
  'gamepad': Gamepad2,
  'dumbbell': Dumbbell,
  'car': Car,
  'smartphone': Smartphone,
  'laptop': Lapt<PERSON>,
  'palette': Palette,
  'clock': Clock,
  'calendar': Calendar,
}

interface CategoryPreviewProps {
  name: string
  description?: string
  color: string
  icon: string
}

export function CategoryPreview({ name, description, color, icon }: CategoryPreviewProps) {
  const IconComponent = iconMap[icon] || Folder

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95, y: 20 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="sticky top-4"
    >
      {/* Preview Label */}
      <div className="text-center mb-4">
        <motion.span
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="text-sm font-semibold text-foreground bg-gradient-to-r from-primary/10 to-secondary/10 px-4 py-2 rounded-full border border-border/30"
        >
          ✨ معاينة مباشرة
        </motion.span>
      </div>

      <Card className="relative overflow-hidden border-border/50 bg-gradient-to-br from-background via-muted/10 to-accent/5 shadow-xl">
        {/* Color Indicator - Enhanced */}
        <div
          className="absolute top-0 right-0 w-2 h-full opacity-80"
          style={{
            background: `linear-gradient(to bottom, ${color}, ${color}80)`
          }}
        />

        {/* Background Gradient - Enhanced */}
        <div
          className="absolute inset-0 opacity-[0.03]"
          style={{
            background: `radial-gradient(circle at top right, ${color}, transparent 70%)`
          }}
        />

        {/* Multiple Animated Background Shapes */}
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 10, 0],
            opacity: [0.1, 0.15, 0.1]
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
          }}
          className="absolute -top-6 -left-6 w-20 h-20 rounded-full blur-xl"
          style={{ backgroundColor: color }}
        />

        <motion.div
          animate={{
            scale: [1, 1.1, 1],
            rotate: [0, -5, 0],
            opacity: [0.05, 0.1, 0.05]
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
          className="absolute -bottom-4 -right-4 w-16 h-16 rounded-full blur-xl"
          style={{ backgroundColor: color }}
        />

        <CardContent className="p-6 relative z-10">
          {/* Header - Enhanced */}
          <div className="flex items-center space-x-4 space-x-reverse mb-6">
            <motion.div
              className="relative"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              <div
                className="w-16 h-16 rounded-2xl flex items-center justify-center shadow-lg relative overflow-hidden"
                style={{ backgroundColor: `${color}15` }}
              >
                {/* Icon background glow */}
                <div
                  className="absolute inset-0 rounded-2xl opacity-20 blur-sm"
                  style={{ backgroundColor: color }}
                />
                <IconComponent
                  className="w-8 h-8 relative z-10"
                  style={{ color: color }}
                />
              </div>

              {/* Floating particles */}
              <motion.div
                animate={{
                  y: [-2, -8, -2],
                  opacity: [0.5, 1, 0.5]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="absolute -top-1 -right-1 w-2 h-2 rounded-full"
                style={{ backgroundColor: color }}
              />
            </motion.div>

            <div className="flex-1">
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2 }}
              >
                <div className="flex items-center space-x-2 space-x-reverse mb-1">
                  <Badge
                    variant="outline"
                    className="text-xs px-2 py-0.5"
                    style={{
                      borderColor: `${color}40`,
                      color: color,
                      backgroundColor: `${color}10`
                    }}
                  >
                    جديدة
                  </Badge>
                </div>
              </motion.div>
            </div>
          </div>

          {/* Content - Enhanced */}
          <motion.div
            className="space-y-4"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <h3 className="text-xl font-bold text-foreground line-clamp-1 leading-tight">
              {name || "اسم الفئة"}
            </h3>

            {description && (
              <p className="text-sm text-muted-foreground line-clamp-3 leading-relaxed">
                {description}
              </p>
            )}

            {!description && (
              <p className="text-sm text-muted-foreground/60 italic">
                لم يتم إضافة وصف بعد...
              </p>
            )}
          </motion.div>

          {/* Footer - Enhanced */}
          <motion.div
            className="space-y-4 pt-6 border-t border-border/30 mt-6"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
          >
            {/* Stats Row */}
            <div className="flex items-center justify-between">
              <Badge
                variant="secondary"
                className="flex items-center space-x-1 space-x-reverse bg-muted/50"
              >
                <FileText className="w-3 h-3" />
                <span>0 ملاحظة</span>
              </Badge>

              <div className="flex items-center space-x-1 space-x-reverse text-xs text-muted-foreground">
                <CalendarIcon className="w-3 h-3" />
                <span>الآن</span>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="space-y-2">
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>مستوى الاستخدام</span>
                <span>جديدة</span>
              </div>
              <div className="w-full bg-muted/30 rounded-full h-1.5">
                <motion.div
                  className="h-1.5 rounded-full"
                  style={{ backgroundColor: color }}
                  initial={{ width: 0 }}
                  animate={{ width: "5%" }}
                  transition={{ duration: 1, delay: 0.5 }}
                />
              </div>
            </div>
          </motion.div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
