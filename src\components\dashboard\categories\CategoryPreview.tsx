import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Folder,
  Briefcase,
  Book,
  Brain,
  Edit,
  Target,
  BarChart3,
  Home,
  Heart,
  Star,
  Lightbulb,
  Camera,
  Music,
  Plane,
  ShoppingCart,
  Coffee,
  Gamepad2,
  Dumbbell,
  Car,
  Smartphone,
  Laptop,
  Palette,
  Clock,
  Calendar,
  FileText,
  Calendar as CalendarIcon
} from "lucide-react"

// Icon mapping
const iconMap: { [key: string]: React.ElementType } = {
  'folder': Folder,
  'briefcase': Briefcase,
  'book': Book,
  'brain': Brain,
  'edit': Edit,
  'target': Target,
  'bar-chart': BarChart3,
  'home': Home,
  'heart': Heart,
  'star': Star,
  'lightbulb': Lightbulb,
  'camera': Camera,
  'music': Music,
  'plane': Plane,
  'shopping-cart': ShoppingCart,
  'coffee': Coffee,
  'gamepad': Gamepad2,
  'dumbbell': Dumbbell,
  'car': Car,
  'smartphone': Smartphone,
  'laptop': Laptop,
  'palette': Palette,
  'clock': Clock,
  'calendar': Calendar,
}

interface CategoryPreviewProps {
  name: string
  description?: string
  color: string
  icon: string
}

export function CategoryPreview({ name, description, color, icon }: CategoryPreviewProps) {
  const IconComponent = iconMap[icon] || Folder

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
      className="sticky top-4"
    >
      <Card className="relative overflow-hidden border-border/50 bg-gradient-to-br from-background to-muted/20">
        {/* Color Indicator */}
        <div 
          className="absolute top-0 right-0 w-1 h-full"
          style={{ backgroundColor: color }}
        />
        
        {/* Background Gradient */}
        <div 
          className="absolute inset-0 opacity-5"
          style={{ backgroundColor: color }}
        />
        
        {/* Animated Background Shape */}
        <motion.div
          animate={{
            scale: [1, 1.1, 1],
            rotate: [0, 5, 0],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut",
          }}
          className="absolute -top-4 -left-4 w-16 h-16 opacity-10 rounded-full blur-xl"
          style={{ backgroundColor: color }}
        />

        <CardContent className="p-6 relative z-10">
          {/* Header */}
          <div className="flex items-center space-x-3 space-x-reverse mb-4">
            <div 
              className="w-12 h-12 rounded-xl flex items-center justify-center shadow-sm"
              style={{ backgroundColor: `${color}20` }}
            >
              <IconComponent 
                className="w-6 h-6" 
                style={{ color: color }}
              />
            </div>
          </div>

          {/* Content */}
          <div className="space-y-3">
            <h3 className="text-lg font-bold text-foreground line-clamp-1">
              {name}
            </h3>
            
            {description && (
              <p className="text-sm text-muted-foreground line-clamp-2 leading-relaxed">
                {description}
              </p>
            )}
          </div>

          {/* Footer */}
          <div className="space-y-3 pt-4 border-t border-border/30 mt-4">
            {/* Notes Count */}
            <div className="flex items-center justify-between">
              <Badge 
                variant="secondary" 
                className="flex items-center space-x-1 space-x-reverse"
              >
                <FileText className="w-3 h-3" />
                <span>0 ملاحظة</span>
              </Badge>
              
              <Badge variant="outline" className="text-green-600 border-green-200">
                جديدة
              </Badge>
            </div>

            {/* Creation Date */}
            <div className="flex items-center space-x-1 space-x-reverse text-xs text-muted-foreground">
              <CalendarIcon className="w-3 h-3" />
              <span>الآن</span>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Preview Label */}
      <div className="text-center mt-3">
        <span className="text-xs text-muted-foreground bg-muted/30 px-2 py-1 rounded-full">
          معاينة الفئة
        </span>
      </div>
    </motion.div>
  )
}
