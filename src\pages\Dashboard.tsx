import { useEffect } from "react"
import { useNavigate } from "react-router-dom"
import { motion } from "framer-motion"
import { ThemeProvider } from "@/components/theme/ThemeProvider"
import { Navigation } from "@/components/layout/Navigation"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useAuthStore } from "@/stores/authStore"
import { 
  LayoutDashboard, 
  FileText, 
  FolderPlus, 
  Settings, 
  BarChart3,
  Plus,
  Search,
  Filter
} from "lucide-react"

const Dashboard = () => {
  const navigate = useNavigate()
  const { isAuthenticated, user } = useAuthStore()

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/auth/login')
    }
  }, [isAuthenticated, navigate])

  if (!isAuthenticated) {
    return null // or a loading spinner
  }

  const stats = [
    {
      title: "إجمالي الملاحظات",
      value: user?.profile?.notes_count || 0,
      icon: FileText,
      color: "from-blue-500 to-blue-600"
    },
    {
      title: "المجلدات",
      value: user?.profile?.folders_count || 0,
      icon: FolderPlus,
      color: "from-green-500 to-green-600"
    },
    {
      title: "الخطة الحالية",
      value: user?.plan === 'free' ? 'مجانية' : 'مميزة',
      icon: BarChart3,
      color: "from-purple-500 to-purple-600"
    }
  ]

  return (
    <ThemeProvider defaultTheme="system" storageKey="nots-dashboard-theme">
      <div className="min-h-screen bg-background font-tajawal" dir="rtl">
        <Navigation />
        
        <main className="pt-20 pb-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Welcome Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="mb-8"
            >
              <h1 className="text-3xl font-bold text-foreground mb-2">
                مرحباً، {user?.fullName || 'مستخدم'}! 👋
              </h1>
              <p className="text-muted-foreground text-lg">
                إليك نظرة عامة على ملاحظاتك ونشاطك
              </p>
            </motion.div>

            {/* Stats Cards */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"
            >
              {stats.map((stat, index) => {
                const Icon = stat.icon
                return (
                  <Card key={stat.title} className="relative overflow-hidden">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium text-muted-foreground">
                        {stat.title}
                      </CardTitle>
                      <div className={`w-8 h-8 rounded-lg bg-gradient-to-r ${stat.color} flex items-center justify-center`}>
                        <Icon className="w-4 h-4 text-white" />
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-foreground">{stat.value}</div>
                    </CardContent>
                  </Card>
                )
              })}
            </motion.div>

            {/* Quick Actions */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="grid grid-cols-1 lg:grid-cols-2 gap-8"
            >
              {/* Recent Notes */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center space-x-2 space-x-reverse">
                        <FileText className="w-5 h-5" />
                        <span>الملاحظات الأخيرة</span>
                      </CardTitle>
                      <CardDescription>آخر الملاحظات التي تم إنشاؤها</CardDescription>
                    </div>
                    <Button size="sm" className="flex items-center space-x-1 space-x-reverse">
                      <Plus className="w-4 h-4" />
                      <span>ملاحظة جديدة</span>
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="text-center py-8 text-muted-foreground">
                      <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
                      <p>لا توجد ملاحظات بعد</p>
                      <p className="text-sm">ابدأ بإنشاء ملاحظتك الأولى!</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Tools */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2 space-x-reverse">
                    <LayoutDashboard className="w-5 h-5" />
                    <span>أدوات سريعة</span>
                  </CardTitle>
                  <CardDescription>الوصول السريع للميزات الأساسية</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <Button variant="outline" className="h-20 flex flex-col items-center space-y-2">
                      <Plus className="w-6 h-6" />
                      <span className="text-sm">ملاحظة جديدة</span>
                    </Button>
                    
                    <Button variant="outline" className="h-20 flex flex-col items-center space-y-2">
                      <FolderPlus className="w-6 h-6" />
                      <span className="text-sm">مجلد جديد</span>
                    </Button>
                    
                    <Button variant="outline" className="h-20 flex flex-col items-center space-y-2">
                      <Search className="w-6 h-6" />
                      <span className="text-sm">البحث</span>
                    </Button>
                    
                    <Button variant="outline" className="h-20 flex flex-col items-center space-y-2">
                      <Settings className="w-6 h-6" />
                      <span className="text-sm">الإعدادات</span>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Welcome Message for New Users */}
            {user?.profile?.notes_count === 0 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="mt-8"
              >
                <Card className="bg-gradient-to-r from-primary/10 to-secondary/10 border-primary/20">
                  <CardContent className="p-6">
                    <div className="text-center">
                      <h3 className="text-xl font-semibold text-foreground mb-2">
                        🎉 مرحباً بك في Nots!
                      </h3>
                      <p className="text-muted-foreground mb-4">
                        أنت الآن جاهز لبدء رحلتك مع الملاحظات الذكية. ابدأ بإنشاء ملاحظتك الأولى!
                      </p>
                      <div className="flex flex-col sm:flex-row gap-3 justify-center">
                        <Button className="bg-gradient-to-r from-primary to-secondary text-white">
                          إنشاء ملاحظة جديدة
                        </Button>
                        <Button variant="outline">
                          استكشاف الميزات
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </div>
        </main>
      </div>
    </ThemeProvider>
  )
}

export default Dashboard
