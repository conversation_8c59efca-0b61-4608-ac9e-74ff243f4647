import { useEffect } from "react"
import { useNavigate } from "react-router-dom"
import { motion } from "framer-motion"
import { ThemeProvider } from "@/components/theme/ThemeProvider"
import { DashboardLayout } from "@/components/dashboard/DashboardLayout"
import { StatsCards } from "@/components/dashboard/StatsCards"
import { RecentActivity } from "@/components/dashboard/RecentActivity"
import { AIAnalytics } from "@/components/dashboard/AIAnalytics"
import { CalendarPreview } from "@/components/dashboard/CalendarPreview"
import { AccountUpgrade } from "@/components/dashboard/AccountUpgrade"
import { useAuthStore } from "@/stores/authStore"
import {
  Home,
  Sparkles,
  TrendingUp
} from "lucide-react"

const Dashboard = () => {
  const navigate = useNavigate()
  const { isAuthenticated, user } = useAuthStore()

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/auth/login')
    }
  }, [isAuthenticated, navigate])

  if (!isAuthenticated) {
    return null // or a loading spinner
  }

  return (
    <ThemeProvider defaultTheme="system" storageKey="nots-dashboard-theme">
      <DashboardLayout>
        {/* Welcome Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="flex items-center space-x-3 space-x-reverse mb-4">
            <div className="w-12 h-12 bg-gradient-to-r from-primary to-secondary rounded-xl flex items-center justify-center shadow-lg">
              <Home className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-foreground">
                مرحباً، {user?.fullName || 'مستخدم'}! 👋
              </h1>
              <p className="text-muted-foreground">
                إليك نظرة عامة على ملاحظاتك ونشاطك
              </p>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="flex items-center space-x-6 space-x-reverse text-sm text-muted-foreground">
            <div className="flex items-center space-x-1 space-x-reverse">
              <TrendingUp className="w-4 h-4 text-green-500" />
              <span>نشاط متزايد هذا الأسبوع</span>
            </div>
            <div className="flex items-center space-x-1 space-x-reverse">
              <Sparkles className="w-4 h-4 text-primary" />
              <span>مدعوم بالذكاء الاصطناعي</span>
            </div>
          </div>
        </motion.div>

        {/* Stats Cards */}
        <div className="mb-8">
          <StatsCards />
        </div>

        {/* Recent Activity */}
        <div className="mb-8">
          <RecentActivity />
        </div>

        {/* AI Analytics */}
        <div className="mb-8">
          <AIAnalytics />
        </div>

        {/* Calendar Preview */}
        <div className="mb-8">
          <CalendarPreview />
        </div>

        {/* Account Upgrade */}
        <div className="mb-8">
          <AccountUpgrade />
        </div>
      </DashboardLayout>
    </ThemeProvider>
  )
}

export default Dashboard
