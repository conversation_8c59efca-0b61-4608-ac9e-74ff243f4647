import { useState } from "react"
import { motion } from "framer-motion"
import { Briefcase, GraduationCap, Lightbulb, User, Plus, X } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { useAuthStore } from "@/stores/authStore"
import { toast } from "sonner"

interface InitialSetupStepProps {
  onComplete: () => void
  onBack: () => void
}

const suggestedFolders = [
  {
    id: 'work',
    name: 'العمل',
    description: 'مهام ومشاريع العمل',
    icon: Briefcase,
    color: 'from-blue-500 to-blue-600'
  },
  {
    id: 'study',
    name: 'الدراسة',
    description: 'محاضرات ومذاكرة',
    icon: GraduationCap,
    color: 'from-green-500 to-green-600'
  },
  {
    id: 'ideas',
    name: 'أفكار عامة',
    description: 'أفكار وإلهامات',
    icon: Lightbulb,
    color: 'from-yellow-500 to-yellow-600'
  },
  {
    id: 'personal',
    name: 'الشخصي',
    description: 'أمور شخصية ويومية',
    icon: User,
    color: 'from-purple-500 to-purple-600'
  }
]

export function InitialSetupStep({ onComplete, onBack }: InitialSetupStepProps) {
  const { signUpData, updateSignUpData, completeSignUp, isLoading } = useAuthStore()
  const [selectedFolders, setSelectedFolders] = useState<string[]>(signUpData.selectedFolders)
  const [customFolders, setCustomFolders] = useState<string[]>(signUpData.customFolders)
  const [newFolderName, setNewFolderName] = useState('')

  const handleFolderToggle = (folderId: string) => {
    setSelectedFolders(prev => 
      prev.includes(folderId) 
        ? prev.filter(id => id !== folderId)
        : [...prev, folderId]
    )
  }

  const handleAddCustomFolder = () => {
    if (newFolderName.trim() && !customFolders.includes(newFolderName.trim())) {
      setCustomFolders(prev => [...prev, newFolderName.trim()])
      setNewFolderName('')
    }
  }

  const handleRemoveCustomFolder = (folderName: string) => {
    setCustomFolders(prev => prev.filter(name => name !== folderName))
  }

  const handleComplete = async () => {
    updateSignUpData({
      selectedFolders,
      customFolders
    })

    const result = await completeSignUp()

    if (result.success) {
      toast.success("🎉 تم إنشاء حسابك بنجاح!", {
        description: "مرحباً بك في Nots! ابدأ في تنظيم ملاحظاتك الآن"
      })
      onComplete()
    } else {
      toast.error("حدث خطأ أثناء إنشاء الحساب", {
        description: result.error || "يرجى المحاولة مرة أخرى"
      })
    }
  }

  return (
    <div className="space-y-6">
      {/* Suggested Folders */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-foreground">المجلدات المقترحة</h3>
        
        <div className="grid grid-cols-1 gap-3">
          {suggestedFolders.map((folder, index) => {
            const Icon = folder.icon
            const isSelected = selectedFolders.includes(folder.id)
            
            return (
              <motion.button
                key={folder.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                onClick={() => handleFolderToggle(folder.id)}
                className={`
                  p-4 rounded-xl border-2 text-right transition-all duration-300
                  ${isSelected 
                    ? 'border-primary bg-primary/10' 
                    : 'border-border hover:border-primary/50 hover:bg-muted/50'
                  }
                `}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className="flex items-center space-x-4 space-x-reverse">
                  <Checkbox
                    checked={isSelected}
                    onCheckedChange={() => handleFolderToggle(folder.id)}
                    className="data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                  />
                  
                  <div className={`
                    w-10 h-10 rounded-lg bg-gradient-to-r ${folder.color} 
                    flex items-center justify-center text-white shadow-md
                  `}>
                    <Icon className="w-5 h-5" />
                  </div>
                  
                  <div className="flex-1">
                    <h4 className="font-medium text-foreground">{folder.name}</h4>
                    <p className="text-sm text-muted-foreground">{folder.description}</p>
                  </div>
                </div>
              </motion.button>
            )
          })}
        </div>
      </div>

      {/* Custom Folders */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-foreground">مجلدات مخصصة</h3>
        
        {/* Add Custom Folder */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
          className="flex space-x-2 space-x-reverse"
        >
          <Input
            value={newFolderName}
            onChange={(e) => setNewFolderName(e.target.value)}
            placeholder="اسم المجلد الجديد"
            className="flex-1"
            onKeyPress={(e) => e.key === 'Enter' && handleAddCustomFolder()}
          />
          <Button
            onClick={handleAddCustomFolder}
            disabled={!newFolderName.trim()}
            size="sm"
            className="px-4"
          >
            <Plus className="w-4 h-4" />
          </Button>
        </motion.div>
        
        {/* Custom Folders List */}
        {customFolders.length > 0 && (
          <div className="space-y-2">
            {customFolders.map((folderName, index) => (
              <motion.div
                key={folderName}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="flex items-center justify-between p-3 bg-muted/50 rounded-lg"
              >
                <div className="flex items-center space-x-3 space-x-reverse">
                  <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-gray-500 to-gray-600 flex items-center justify-center text-white">
                    <User className="w-4 h-4" />
                  </div>
                  <span className="font-medium text-foreground">{folderName}</span>
                </div>
                
                <Button
                  onClick={() => handleRemoveCustomFolder(folderName)}
                  variant="ghost"
                  size="sm"
                  className="text-muted-foreground hover:text-red-500 p-1"
                >
                  <X className="w-4 h-4" />
                </Button>
              </motion.div>
            ))}
          </div>
        )}
      </div>

      {/* Summary */}
      {(selectedFolders.length > 0 || customFolders.length > 0) && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.7 }}
          className="p-4 bg-primary/10 border border-primary/20 rounded-xl"
        >
          <h4 className="font-medium text-primary mb-2">ملخص المجلدات المختارة:</h4>
          <p className="text-sm text-muted-foreground">
            سيتم إنشاء {selectedFolders.length + customFolders.length} مجلد لتنظيم ملاحظاتك
          </p>
        </motion.div>
      )}

      {/* Action Buttons */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.8 }}
        className="flex space-x-3 space-x-reverse"
      >
        <Button
          variant="outline"
          onClick={onBack}
          disabled={isLoading}
          className="flex-1 border-2 border-border text-foreground hover:bg-muted py-3 rounded-xl transition-all duration-300"
        >
          رجوع
        </Button>
        
        <Button
          onClick={handleComplete}
          disabled={isLoading}
          className="flex-1 bg-gradient-to-r from-primary to-secondary hover:opacity-90 text-primary-foreground py-3 text-lg font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50"
        >
          {isLoading ? (
            <div className="flex items-center space-x-2 space-x-reverse">
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              <span>جاري الإنشاء...</span>
            </div>
          ) : (
            'إنشاء الحساب'
          )}
        </Button>
      </motion.div>

      {/* Help Text */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.9 }}
        className="text-center"
      >
        <p className="text-sm text-muted-foreground">
          يمكنك إضافة أو تعديل المجلدات لاحقاً من إعدادات التطبيق
        </p>
      </motion.div>
    </div>
  )
}
