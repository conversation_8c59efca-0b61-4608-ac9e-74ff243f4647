
import { useState, useRef, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Link } from "react-router-dom"
import { Button } from "@/components/ui/button"
import { ThemeToggle } from "@/components/theme/ThemeToggle"
import {
  Menu,
  X,
  <PERSON>rkles,
  Brain,
  Wrench,
  HelpCircle,
  MessageSquare,
  Globe,
  ChevronDown,
  DollarSign,
  Zap,
  Shield,
  Users,
  Star,
  LogIn,
  UserPlus
} from "lucide-react"

const navItems = [
  {
    name: "الميزات",
    href: "#features",
    icon: Brain,
    dropdown: [
      { name: "الميزات الذكية", href: "#ai-features", icon: Brain, description: "ذكاء اصطناعي متقدم" },
      { name: "الأدوات", href: "#tools", icon: Wrench, description: "أدوات قوية ومتنوعة" },
      { name: "الحماية", href: "#security", icon: Shield, description: "أمان وخصوصية عالية" }
    ]
  },
  { name: "كيف يعمل؟", href: "#how-it-works", icon: HelpCircle },
  { name: "الأسعار", href: "#pricing", icon: DollarSign },
  {
    name: "المجتمع",
    href: "#community",
    icon: Users,
    dropdown: [
      { name: "آراء المستخدمين", href: "#reviews", icon: MessageSquare, description: "تجارب حقيقية" },
      { name: "قصص النجاح", href: "#success-stories", icon: Star, description: "إنجازات ملهمة" },
      { name: "المطورين", href: "#developers", icon: Zap, description: "موارد للمطورين" }
    ]
  },
  { name: "الأسئلة الشائعة", href: "#faq", icon: HelpCircle },
]

export function Navigation() {
  const [isOpen, setIsOpen] = useState(false)
  const [language, setLanguage] = useState<'ar' | 'en'>('ar')
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)
  const dropdownRef = useRef<HTMLDivElement>(null)

  const toggleLanguage = () => {
    setLanguage(prev => prev === 'ar' ? 'en' : 'ar')
  }

  const handleDropdownToggle = (itemName: string) => {
    setActiveDropdown(activeDropdown === itemName ? null : itemName)
  }

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setActiveDropdown(null)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  return (
    <motion.nav
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      className="fixed top-0 left-0 right-0 z-50 bg-background/95 backdrop-blur-md border-b border-border/50 nots-shadow-lg"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16 md:h-18">
          {/* Logo */}
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="flex items-center space-x-2 space-x-reverse cursor-pointer"
          >
            <div className="w-9 h-9 bg-gradient-to-r from-primary to-secondary rounded-xl flex items-center justify-center shadow-lg">
              <Sparkles className="w-5 h-5 text-primary-foreground" />
            </div>
            <h1 className="text-2xl md:text-3xl font-bold text-primary nots-heading">Nots</h1>
          </motion.div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-8 space-x-reverse">
            {navItems.map((item, index) => {
              const Icon = item.icon
              return (
                <motion.a
                  key={item.name}
                  href={item.href}
                  className="text-foreground hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 flex items-center space-x-1 space-x-reverse hover:bg-muted/50"
                  whileHover={{ scale: 1.05 }}
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Icon className="w-4 h-4" />
                  <span>{item.name}</span>
                </motion.a>
              )
            })}
          </div>

          {/* Language Toggle, Theme Toggle & CTA */}
          <div className="hidden md:flex items-center space-x-4 space-x-reverse">
            <button
              onClick={toggleLanguage}
              className="flex items-center space-x-1 space-x-reverse px-3 py-2 rounded-md text-sm font-medium text-foreground hover:text-primary hover:bg-muted/50 transition-all duration-200"
            >
              <Globe className="w-4 h-4" />
              <span>{language === 'ar' ? 'English' : 'العربية'}</span>
            </button>
            <ThemeToggle />
            <Button className="bg-gradient-purple hover:opacity-90 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 flex items-center space-x-1 space-x-reverse">
              <Sparkles className="w-4 h-4" />
              <span>✨ ابدأ مجاناً</span>
            </Button>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center space-x-2 space-x-reverse">
            <ThemeToggle />
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsOpen(!isOpen)}
            >
              {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="md:hidden border-t border-border/50 bg-background/95 backdrop-blur-md"
            >
              <div className="py-4 space-y-1 max-h-96 overflow-y-auto">
                {navItems.map((item, index) => {
                  const Icon = item.icon
                  const hasDropdown = item.dropdown && item.dropdown.length > 0

                  return (
                    <div key={item.name}>
                      {hasDropdown ? (
                        <div>
                          <motion.button
                            onClick={() => handleDropdownToggle(`mobile-${item.name}`)}
                            className="w-full flex items-center justify-between space-x-2 space-x-reverse px-4 py-3 text-foreground hover:text-primary hover:bg-muted/50 rounded-lg transition-colors"
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.3, delay: index * 0.1 }}
                          >
                            <div className="flex items-center space-x-2 space-x-reverse">
                              <Icon className="w-5 h-5" />
                              <span className="font-medium">{item.name}</span>
                            </div>
                            <ChevronDown className={`w-4 h-4 transition-transform duration-200 ${
                              activeDropdown === `mobile-${item.name}` ? 'rotate-180' : ''
                            }`} />
                          </motion.button>

                          <AnimatePresence>
                            {activeDropdown === `mobile-${item.name}` && (
                              <motion.div
                                initial={{ opacity: 0, height: 0 }}
                                animate={{ opacity: 1, height: "auto" }}
                                exit={{ opacity: 0, height: 0 }}
                                transition={{ duration: 0.2 }}
                                className="mr-6 mt-1 space-y-1"
                              >
                                {item.dropdown?.map((dropdownItem) => {
                                  const DropdownIcon = dropdownItem.icon
                                  return (
                                    <motion.a
                                      key={dropdownItem.name}
                                      href={dropdownItem.href}
                                      className="flex items-start space-x-2 space-x-reverse px-4 py-2 text-sm text-muted-foreground hover:text-primary hover:bg-muted/30 rounded-lg transition-colors"
                                      onClick={() => {
                                        setIsOpen(false)
                                        setActiveDropdown(null)
                                      }}
                                      whileHover={{ x: -4 }}
                                    >
                                      <DropdownIcon className="w-4 h-4 mt-0.5 text-primary" />
                                      <div>
                                        <div className="font-medium">{dropdownItem.name}</div>
                                        <div className="text-xs text-muted-foreground">{dropdownItem.description}</div>
                                      </div>
                                    </motion.a>
                                  )
                                })}
                              </motion.div>
                            )}
                          </AnimatePresence>
                        </div>
                      ) : (
                        <motion.a
                          href={item.href}
                          className="flex items-center space-x-2 space-x-reverse px-4 py-3 text-foreground hover:text-primary hover:bg-muted/50 rounded-lg transition-colors"
                          onClick={() => setIsOpen(false)}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.3, delay: index * 0.1 }}
                          whileHover={{ x: -4 }}
                        >
                          <Icon className="w-5 h-5" />
                          <span className="font-medium">{item.name}</span>
                        </motion.a>
                      )}
                    </div>
                  )
                })}

                {/* Mobile Auth Section */}
                <div className="pt-4 border-t border-border/30 space-y-3">
                  <motion.button
                    onClick={toggleLanguage}
                    className="w-full flex items-center justify-center space-x-1 space-x-reverse px-3 py-2 rounded-lg text-sm font-medium text-foreground hover:text-primary hover:bg-muted/50 transition-all duration-200"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Globe className="w-4 h-4" />
                    <span>{language === 'ar' ? 'English' : 'العربية'}</span>
                  </motion.button>

                  <div className="space-y-2">
                    <Link to="/auth/login">
                      <Button
                        variant="ghost"
                        className="w-full text-foreground hover:text-primary hover:bg-muted/50 rounded-lg transition-all duration-200 flex items-center justify-center space-x-1 space-x-reverse"
                        onClick={() => setIsOpen(false)}
                      >
                        <LogIn className="w-4 h-4" />
                        <span>تسجيل الدخول</span>
                      </Button>
                    </Link>

                    <Link to="/auth/signup">
                      <Button
                        className="w-full bg-gradient-to-r from-primary to-secondary hover:opacity-90 text-primary-foreground rounded-xl shadow-lg flex items-center justify-center space-x-1 space-x-reverse"
                        onClick={() => setIsOpen(false)}
                      >
                        <UserPlus className="w-4 h-4" />
                        <span>✨ ابدأ مجاناً</span>
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.nav>
  )
}
