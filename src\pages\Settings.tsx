import { useEffect } from "react"
import { useNavigate } from "react-router-dom"
import { motion } from "framer-motion"
import { ThemeProvider } from "@/components/theme/ThemeProvider"
import { Navigation } from "@/components/layout/Navigation"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { useAuthStore } from "@/stores/authStore"
import { 
  Settings as SettingsIcon, 
  User, 
  Bell, 
  Shield, 
  Palette,
  Globe,
  Crown
} from "lucide-react"

const Settings = () => {
  const navigate = useNavigate()
  const { isAuthenticated, user } = useAuthStore()

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/auth/login')
    }
  }, [isAuthenticated, navigate])

  if (!isAuthenticated) {
    return null
  }

  return (
    <ThemeProvider defaultTheme="system" storageKey="nots-settings-theme">
      <div className="min-h-screen bg-background font-tajawal" dir="rtl">
        <Navigation />
        
        <main className="pt-20 pb-12">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Header */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="mb-8"
            >
              <h1 className="text-3xl font-bold text-foreground mb-2 flex items-center space-x-2 space-x-reverse">
                <SettingsIcon className="w-8 h-8" />
                <span>الإعدادات</span>
              </h1>
              <p className="text-muted-foreground text-lg">
                إدارة حسابك وتخصيص تجربتك مع Nots
              </p>
            </motion.div>

            <div className="space-y-6">
              {/* Profile Settings */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
              >
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2 space-x-reverse">
                      <User className="w-5 h-5" />
                      <span>معلومات الحساب</span>
                    </CardTitle>
                    <CardDescription>إدارة معلوماتك الشخصية</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label className="text-sm font-medium">الاسم الكامل</Label>
                        <p className="text-foreground mt-1">{user?.fullName}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">البريد الإلكتروني</Label>
                        <p className="text-foreground mt-1">{user?.email}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <Crown className="w-5 h-5 text-primary" />
                      <span className="text-sm">
                        الخطة الحالية: <strong>{user?.plan === 'free' ? 'مجانية' : 'مميزة'}</strong>
                      </span>
                    </div>
                    <Button variant="outline" className="w-full md:w-auto">
                      تعديل المعلومات
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Notification Settings */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2 space-x-reverse">
                      <Bell className="w-5 h-5" />
                      <span>الإشعارات</span>
                    </CardTitle>
                    <CardDescription>إدارة تفضيلات الإشعارات</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-sm font-medium">إشعارات البريد الإلكتروني</Label>
                        <p className="text-sm text-muted-foreground">تلقي تحديثات عبر البريد الإلكتروني</p>
                      </div>
                      <Switch defaultChecked={user?.profile?.notifications_enabled} />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-sm font-medium">إشعارات المتصفح</Label>
                        <p className="text-sm text-muted-foreground">إشعارات فورية في المتصفح</p>
                      </div>
                      <Switch />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-sm font-medium">تذكيرات الملاحظات</Label>
                        <p className="text-sm text-muted-foreground">تذكيرات لمراجعة الملاحظات</p>
                      </div>
                      <Switch />
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Appearance Settings */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
              >
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2 space-x-reverse">
                      <Palette className="w-5 h-5" />
                      <span>المظهر</span>
                    </CardTitle>
                    <CardDescription>تخصيص مظهر التطبيق</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-sm font-medium">الوضع المظلم</Label>
                        <p className="text-sm text-muted-foreground">تبديل بين الوضع الفاتح والمظلم</p>
                      </div>
                      <Switch />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-sm font-medium">اللغة</Label>
                        <p className="text-sm text-muted-foreground">العربية (افتراضي)</p>
                      </div>
                      <Button variant="outline" size="sm">
                        <Globe className="w-4 h-4 ml-2" />
                        تغيير
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Privacy & Security */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2 space-x-reverse">
                      <Shield className="w-5 h-5" />
                      <span>الخصوصية والأمان</span>
                    </CardTitle>
                    <CardDescription>إدارة إعدادات الأمان والخصوصية</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-sm font-medium">المصادقة الثنائية</Label>
                        <p className="text-sm text-muted-foreground">حماية إضافية لحسابك</p>
                      </div>
                      <Button variant="outline" size="sm">
                        تفعيل
                      </Button>
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-sm font-medium">تغيير كلمة المرور</Label>
                        <p className="text-sm text-muted-foreground">تحديث كلمة مرور حسابك</p>
                      </div>
                      <Button variant="outline" size="sm">
                        تغيير
                      </Button>
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-sm font-medium">تصدير البيانات</Label>
                        <p className="text-sm text-muted-foreground">تحميل نسخة من بياناتك</p>
                      </div>
                      <Button variant="outline" size="sm">
                        تصدير
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Danger Zone */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
              >
                <Card className="border-red-200 dark:border-red-800">
                  <CardHeader>
                    <CardTitle className="text-red-600 dark:text-red-400">منطقة الخطر</CardTitle>
                    <CardDescription>إجراءات لا يمكن التراجع عنها</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-sm font-medium text-red-600 dark:text-red-400">حذف الحساب</Label>
                        <p className="text-sm text-muted-foreground">حذف حسابك وجميع بياناتك نهائياً</p>
                      </div>
                      <Button variant="destructive" size="sm">
                        حذف الحساب
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>
          </div>
        </main>
      </div>
    </ThemeProvider>
  )
}

export default Settings
