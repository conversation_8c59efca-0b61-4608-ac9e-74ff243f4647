import { motion } from "framer-motion"
import { <PERSON> } from "react-router-dom"
import {
  FileText,
  Home,
  ChevronLeft
} from "lucide-react"
import { ViewToggle, ViewType } from "@/components/dashboard/shared/ViewToggle"

interface NotesHeaderProps {
  currentView: ViewType
  onViewChange: (view: ViewType) => void
}

export function NotesHeader({ currentView, onViewChange }: NotesHeaderProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="space-y-4"
    >
      {/* Breadcrumb */}
      <nav className="flex items-center space-x-2 space-x-reverse text-sm text-muted-foreground">
        <Link 
          to="/dashboard" 
          className="flex items-center space-x-1 space-x-reverse hover:text-primary transition-colors"
        >
          <Home className="w-4 h-4" />
          <span>لوحة التحكم</span>
        </Link>
        <ChevronLeft className="w-4 h-4" />
        <span className="text-foreground font-medium">الملاحظات</span>
      </nav>

      {/* Header Content */}
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-4 space-x-reverse">
          <div className="w-16 h-16 bg-gradient-to-r from-primary to-secondary rounded-2xl flex items-center justify-center shadow-lg">
            <FileText className="w-8 h-8 text-white" />
          </div>

          <div className="flex-1">
            <h1 className="text-3xl font-bold text-foreground mb-2 flex items-center space-x-2 space-x-reverse">
              <span>📝 ملاحظاتي</span>
            </h1>

            <p className="text-muted-foreground text-lg leading-relaxed max-w-4xl">
              إدارة وتنظيم جميع ملاحظاتك في مكان واحد. اكتب أفكارك، نظم مهامك، واحتفظ بكل ما يهمك بطريقة ذكية وجميلة.
            </p>
          </div>
        </div>

        {/* View Toggle */}
        <div className="flex items-center space-x-3 space-x-reverse">
          <ViewToggle
            currentView={currentView}
            onViewChange={onViewChange}
            storageKey="notes-view-preference"
          />
        </div>
      </div>
    </motion.div>
  )
}
