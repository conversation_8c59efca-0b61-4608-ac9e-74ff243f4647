'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Zap, 
  FileText, 
  Lightbulb, 
  Mic, 
  Eye, 
  Play, 
  ArrowRight,
  Sparkles,
  Brain,
  Clock,
  CheckCircle2
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

interface AINotification {
  id: string
  type: 'summary' | 'suggestion' | 'transcription' | 'analysis' | 'reminder'
  title: string
  description: string
  timestamp: Date
  isRead: boolean
  priority: 'low' | 'medium' | 'high'
  noteId?: string
  noteTitle?: string
  actions?: AIAction[]
  progress?: number
  status: 'completed' | 'processing' | 'pending'
}

interface AIAction {
  id: string
  label: string
  type: 'primary' | 'secondary'
  icon?: React.ReactNode
  onClick: () => void
}

interface AINotificationsProps {
  onRead: (notificationId: string) => void
  isLoading: boolean
}

interface AINotificationCardProps {
  notification: AINotification
  onRead: (id: string) => void
  index: number
}

function AINotificationCard({ notification, onRead, index }: AINotificationCardProps) {
  const getIcon = (type: string) => {
    switch (type) {
      case 'summary':
        return <FileText className="w-5 h-5" />
      case 'suggestion':
        return <Lightbulb className="w-5 h-5" />
      case 'transcription':
        return <Mic className="w-5 h-5" />
      case 'analysis':
        return <Brain className="w-5 h-5" />
      case 'reminder':
        return <Clock className="w-5 h-5" />
      default:
        return <Zap className="w-5 h-5" />
    }
  }

  const getIconColor = (type: string) => {
    switch (type) {
      case 'summary':
        return 'text-blue-600 bg-gradient-to-r from-blue-500 to-cyan-500'
      case 'suggestion':
        return 'text-yellow-600 bg-gradient-to-r from-yellow-500 to-orange-500'
      case 'transcription':
        return 'text-purple-600 bg-gradient-to-r from-purple-500 to-pink-500'
      case 'analysis':
        return 'text-green-600 bg-gradient-to-r from-green-500 to-emerald-500'
      case 'reminder':
        return 'text-indigo-600 bg-gradient-to-r from-indigo-500 to-blue-500'
      default:
        return 'text-primary bg-gradient-to-r from-primary to-secondary'
    }
  }

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'summary':
        return 'ملخص ذكي'
      case 'suggestion':
        return 'اقتراح ذكي'
      case 'transcription':
        return 'تحويل صوتي'
      case 'analysis':
        return 'تحليل ذكي'
      case 'reminder':
        return 'تذكير ذكي'
      default:
        return 'ذكاء اصطناعي'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle2 className="w-4 h-4 text-green-500" />
      case 'processing':
        return <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />
      default:
        return null
    }
  }

  const formatTimeAgo = (date: Date) => {
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMinutes = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))

    if (diffMinutes < 1) return 'الآن'
    if (diffMinutes < 60) return `منذ ${diffMinutes} دقيقة`
    return `منذ ${diffHours} ساعة`
  }

  const handleMarkAsRead = () => {
    if (!notification.isRead) {
      onRead(notification.id)
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className={`
        bg-background rounded-xl border border-border/50 shadow-sm hover:shadow-md transition-all duration-300 p-6 group relative overflow-hidden
        ${!notification.isRead ? 'ring-2 ring-primary/20' : ''}
      `}
    >
      {/* Gradient Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5 opacity-50" />
      
      <div className="relative space-y-4">
        {/* Header */}
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-4 flex-1">
            <div className={`w-10 h-10 rounded-lg flex items-center justify-center text-white ${getIconColor(notification.type)}`}>
              {getIcon(notification.type)}
            </div>
            
            <div className="flex-1 space-y-2">
              <div className="flex items-center gap-3">
                <h3 className="font-semibold text-foreground group-hover:text-primary transition-colors">
                  {notification.title}
                </h3>
                {!notification.isRead && (
                  <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
                )}
                {getStatusIcon(notification.status)}
              </div>
              
              <p className="text-sm text-muted-foreground line-clamp-2">
                {notification.description}
              </p>

              {notification.noteTitle && (
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <FileText className="w-3 h-3" />
                  <span>من ملاحظة: {notification.noteTitle}</span>
                </div>
              )}
            </div>
          </div>

          <div className="flex flex-col items-end gap-2">
            <Badge 
              variant="outline" 
              className="text-xs bg-gradient-to-r from-primary/10 to-secondary/10 border-primary/20"
            >
              <Sparkles className="w-3 h-3 mr-1" />
              {getTypeLabel(notification.type)}
            </Badge>
          </div>
        </div>

        {/* Progress Bar (for processing notifications) */}
        {notification.status === 'processing' && notification.progress !== undefined && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>جاري المعالجة...</span>
              <span>{notification.progress}%</span>
            </div>
            <div className="w-full bg-muted/30 rounded-full h-2">
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: `${notification.progress}%` }}
                transition={{ duration: 0.5 }}
                className="bg-gradient-to-r from-primary to-secondary h-2 rounded-full"
              />
            </div>
          </div>
        )}

        {/* Timestamp */}
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2 text-muted-foreground">
            <Clock className="w-4 h-4" />
            <span>{formatTimeAgo(notification.timestamp)}</span>
          </div>
          
          {!notification.isRead && (
            <Button
              size="sm"
              variant="ghost"
              onClick={handleMarkAsRead}
              className="text-xs"
            >
              تم القراءة
            </Button>
          )}
        </div>

        {/* Actions */}
        {notification.actions && notification.actions.length > 0 && (
          <div className="flex items-center gap-2 pt-2 border-t border-border/30">
            {notification.actions.map((action) => (
              <Button
                key={action.id}
                size="sm"
                variant={action.type === 'primary' ? 'default' : 'outline'}
                onClick={action.onClick}
                className={`flex items-center gap-2 ${
                  action.type === 'primary' 
                    ? 'bg-gradient-to-r from-primary to-secondary hover:from-primary/90 hover:to-secondary/90' 
                    : ''
                }`}
              >
                {action.icon}
                <span>{action.label}</span>
              </Button>
            ))}
          </div>
        )}
      </div>
    </motion.div>
  )
}

function EmptyState() {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
      className="text-center py-12"
    >
      <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full flex items-center justify-center">
        <Zap className="w-12 h-12 text-primary" />
      </div>
      <h3 className="text-lg font-semibold text-foreground mb-2">
        لا توجد تنبيهات ذكية
      </h3>
      <p className="text-muted-foreground max-w-md mx-auto">
        سيقوم الذكاء الاصطناعي بإنشاء تنبيهات ذكية بناءً على ملاحظاتك ونشاطك.
      </p>
    </motion.div>
  )
}

function LoadingSkeleton() {
  return (
    <div className="space-y-4">
      {Array.from({ length: 2 }).map((_, i) => (
        <motion.div
          key={i}
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: i * 0.1 }}
          className="bg-background rounded-xl border border-border/50 shadow-sm p-6"
        >
          <div className="flex items-start gap-4">
            <div className="w-10 h-10 bg-gradient-to-r from-muted/50 to-muted/30 rounded-lg animate-pulse"></div>
            <div className="flex-1 space-y-2">
              <div className="h-5 bg-muted/50 rounded animate-pulse w-3/4"></div>
              <div className="h-4 bg-muted/50 rounded animate-pulse w-1/2"></div>
              <div className="h-4 bg-muted/50 rounded animate-pulse w-1/4"></div>
            </div>
            <div className="h-6 bg-muted/50 rounded animate-pulse w-20"></div>
          </div>
        </motion.div>
      ))}
    </div>
  )
}

export function AINotifications({ onRead, isLoading }: AINotificationsProps) {
  // Mock AI notifications
  const [notifications] = useState<AINotification[]>([
    {
      id: '1',
      type: 'summary',
      title: 'تم إنشاء ملخص ذكي لملاحظتك',
      description: 'قام الذكاء الاصطناعي بإنشاء ملخص شامل لملاحظة "خطة المشروع الجديد" مع النقاط الرئيسية.',
      timestamp: new Date(Date.now() - 10 * 60 * 1000), // 10 minutes ago
      isRead: false,
      priority: 'medium',
      noteId: 'note-1',
      noteTitle: 'خطة المشروع الجديد',
      status: 'completed',
      actions: [
        {
          id: 'view_summary',
          label: 'عرض الملخص',
          type: 'primary',
          icon: <Eye className="w-4 h-4" />,
          onClick: () => console.log('View summary')
        },
        {
          id: 'view_note',
          label: 'فتح الملاحظة',
          type: 'secondary',
          icon: <ArrowRight className="w-4 h-4" />,
          onClick: () => console.log('Open note')
        }
      ]
    },
    {
      id: '2',
      type: 'suggestion',
      title: 'اقتراح لتوسيع فكرة المشروع',
      description: 'بناءً على ملاحظاتك الأخيرة، يقترح الذكاء الاصطناعي إضافة قسم جديد حول استراتيجية التسويق.',
      timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
      isRead: false,
      priority: 'high',
      noteId: 'note-2',
      noteTitle: 'أفكار المشروع',
      status: 'completed',
      actions: [
        {
          id: 'expand_idea',
          label: 'توسيع الفكرة',
          type: 'primary',
          icon: <Lightbulb className="w-4 h-4" />,
          onClick: () => console.log('Expand idea')
        },
        {
          id: 'view_suggestions',
          label: 'عرض الاقتراحات',
          type: 'secondary',
          icon: <Eye className="w-4 h-4" />,
          onClick: () => console.log('View suggestions')
        }
      ]
    },
    {
      id: '3',
      type: 'transcription',
      title: 'تم تحويل التسجيل الصوتي بنجاح',
      description: 'تم تحويل التسجيل الصوتي الخاص بك إلى نص مكتوب وإضافته إلى ملاحظتك.',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      isRead: true,
      priority: 'medium',
      noteId: 'note-3',
      noteTitle: 'اجتماع الفريق',
      status: 'completed',
      actions: [
        {
          id: 'view_transcription',
          label: 'عرض النص',
          type: 'primary',
          icon: <FileText className="w-4 h-4" />,
          onClick: () => console.log('View transcription')
        },
        {
          id: 'play_audio',
          label: 'تشغيل الصوت',
          type: 'secondary',
          icon: <Play className="w-4 h-4" />,
          onClick: () => console.log('Play audio')
        }
      ]
    }
  ])

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.5 }}
      className="space-y-6"
    >
      {/* Section Header */}
      <div className="flex items-center gap-3">
        <div className="w-8 h-8 bg-gradient-to-r from-primary to-secondary rounded-lg flex items-center justify-center">
          <Zap className="w-4 h-4 text-white" />
        </div>
        <div>
          <h2 className="text-xl font-bold text-foreground">تنبيهات الذكاء الاصطناعي</h2>
          <p className="text-sm text-muted-foreground">
            اقتراحات وتحليلات ذكية لملاحظاتك
          </p>
        </div>
      </div>

      {/* Content */}
      {isLoading ? (
        <LoadingSkeleton />
      ) : notifications.length === 0 ? (
        <EmptyState />
      ) : (
        <div className="space-y-4">
          {notifications.map((notification, index) => (
            <AINotificationCard
              key={notification.id}
              notification={notification}
              onRead={onRead}
              index={index}
            />
          ))}
        </div>
      )}
    </motion.div>
  )
}
