import { motion } from "framer-motion"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { 
  Search, 
  Plus, 
  Filter,
  ArrowUpDown
} from "lucide-react"

export type FilterType = 'all' | 'pinned' | 'favorite' | 'recent'
export type SortType = 'newest' | 'oldest' | 'most_edited' | 'alphabetical'

interface NotesFiltersProps {
  searchQuery: string
  onSearchChange: (query: string) => void
  filterType: FilterType
  onFilterChange: (filter: FilterType) => void
  sortType: SortType
  onSortChange: (sort: SortType) => void
  onCreateNote: () => void
}

export function NotesFilters({
  searchQuery,
  onSearchChange,
  filterType,
  onFilterChange,
  sortType,
  onSortChange,
  onCreateNote
}: NotesFiltersProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.1 }}
      className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between p-6 bg-gradient-to-r from-muted/30 to-accent/10 rounded-xl border border-border/50"
    >
      {/* Left Section - Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4 flex-1 w-full lg:w-auto">
        {/* Search */}
        <div className="relative flex-1 max-w-md">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          <Input
            placeholder="البحث في الملاحظات..."
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pr-10 bg-background/50 border-border/50 focus:border-primary/50 rounded-xl"
          />
        </div>

        {/* Filter Dropdown */}
        <Select value={filterType} onValueChange={(value: FilterType) => onFilterChange(value)}>
          <SelectTrigger className="w-full sm:w-48 bg-background/50 border-border/50 rounded-xl">
            <div className="flex items-center space-x-2 space-x-reverse">
              <Filter className="w-4 h-4" />
              <SelectValue placeholder="تصفية" />
            </div>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">الكل</SelectItem>
            <SelectItem value="pinned">مثبتة</SelectItem>
            <SelectItem value="favorite">مفضلة</SelectItem>
            <SelectItem value="recent">حديثة</SelectItem>
          </SelectContent>
        </Select>

        {/* Sort Dropdown */}
        <Select value={sortType} onValueChange={(value: SortType) => onSortChange(value)}>
          <SelectTrigger className="w-full sm:w-48 bg-background/50 border-border/50 rounded-xl">
            <div className="flex items-center space-x-2 space-x-reverse">
              <ArrowUpDown className="w-4 h-4" />
              <SelectValue placeholder="ترتيب" />
            </div>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="newest">الأحدث</SelectItem>
            <SelectItem value="oldest">الأقدم</SelectItem>
            <SelectItem value="most_edited">الأكثر تعديلاً</SelectItem>
            <SelectItem value="alphabetical">أبجدياً</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Right Section - Create Button */}
      <motion.div
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <Button
          onClick={onCreateNote}
          className="bg-gradient-to-r from-primary to-secondary hover:opacity-90 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto"
        >
          <Plus className="w-4 h-4 mr-2" />
          ➕ ملاحظة جديدة
        </Button>
      </motion.div>
    </motion.div>
  )
}
