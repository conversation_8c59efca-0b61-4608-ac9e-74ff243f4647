import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { 
  Grid3X3, 
  List, 
  LayoutGrid, 
  Columns,
  ChevronDown
} from "lucide-react"

export type ViewType = 'grid' | 'list' | 'compact' | 'masonry'

interface ViewToggleProps {
  currentView: ViewType
  onViewChange: (view: ViewType) => void
  storageKey: string
}

const viewOptions = [
  {
    value: 'grid' as ViewType,
    label: 'شبكة',
    icon: Grid3X3,
    description: 'عرض البطاقات في شبكة مرنة'
  },
  {
    value: 'list' as ViewType,
    label: 'قائمة',
    icon: List,
    description: 'عرض البطاقات في قائمة أفقية'
  },
  {
    value: 'compact' as ViewType,
    label: 'مضغوط',
    icon: LayoutGrid,
    description: 'بطاقات صغيرة مع مساحات أقل'
  },
  {
    value: 'masonry' as ViewType,
    label: 'متدرج',
    icon: Columns,
    description: 'تخطيط متدرج مثل Pinterest'
  }
]

export function ViewToggle({ currentView, onViewChange, storageKey }: ViewToggleProps) {
  const [isOpen, setIsOpen] = useState(false)

  // Load saved view preference
  useEffect(() => {
    const savedView = localStorage.getItem(storageKey) as ViewType
    if (savedView && viewOptions.find(option => option.value === savedView)) {
      onViewChange(savedView)
    }
  }, [storageKey, onViewChange])

  // Save view preference
  const handleViewChange = (view: ViewType) => {
    onViewChange(view)
    localStorage.setItem(storageKey, view)
    setIsOpen(false)
  }

  const currentOption = viewOptions.find(option => option.value === currentView) || viewOptions[0]

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="h-9 px-3 rounded-xl border-border/50 hover:bg-muted/50 transition-colors"
        >
          <currentOption.icon className="w-4 h-4 ml-2" />
          <span className="hidden sm:inline">{currentOption.label}</span>
          <ChevronDown className="w-3 h-3 mr-2 transition-transform duration-200" 
            style={{ transform: isOpen ? 'rotate(180deg)' : 'rotate(0deg)' }} 
          />
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent 
        align="end" 
        className="w-64 p-2"
        sideOffset={8}
      >
        <div className="space-y-1">
          {viewOptions.map((option) => (
            <DropdownMenuItem
              key={option.value}
              onClick={() => handleViewChange(option.value)}
              className={`
                flex items-start gap-3 p-3 rounded-lg cursor-pointer transition-colors
                ${currentView === option.value 
                  ? 'bg-primary/10 text-primary' 
                  : 'hover:bg-muted/50'
                }
              `}
            >
              <div className={`
                w-8 h-8 rounded-lg flex items-center justify-center transition-colors
                ${currentView === option.value 
                  ? 'bg-primary/20 text-primary' 
                  : 'bg-muted/30 text-muted-foreground'
                }
              `}>
                <option.icon className="w-4 h-4" />
              </div>
              
              <div className="flex-1 text-right">
                <div className={`
                  font-medium text-sm
                  ${currentView === option.value ? 'text-primary' : 'text-foreground'}
                `}>
                  {option.label}
                </div>
                <div className="text-xs text-muted-foreground mt-0.5">
                  {option.description}
                </div>
              </div>

              {currentView === option.value && (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="w-2 h-2 bg-primary rounded-full"
                />
              )}
            </DropdownMenuItem>
          ))}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

// Hook for managing view state
export function useViewToggle(storageKey: string, defaultView: ViewType = 'grid') {
  const [currentView, setCurrentView] = useState<ViewType>(defaultView)

  useEffect(() => {
    const savedView = localStorage.getItem(storageKey) as ViewType
    if (savedView && viewOptions.find(option => option.value === savedView)) {
      setCurrentView(savedView)
    }
  }, [storageKey])

  const handleViewChange = (view: ViewType) => {
    setCurrentView(view)
    localStorage.setItem(storageKey, view)
  }

  return {
    currentView,
    setCurrentView: handleViewChange
  }
}
