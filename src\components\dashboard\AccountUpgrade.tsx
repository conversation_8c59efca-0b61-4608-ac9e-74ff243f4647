import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useAuthStore } from "@/stores/authStore"
import { 
  Crown, 
  Sparkles, 
  Check, 
  ArrowLeft,
  Zap,
  Shield,
  Infinity
} from "lucide-react"

const proFeatures = [
  {
    icon: Infinity,
    title: "ملاحظات غير محدودة",
    description: "أنشئ عدد لا محدود من الملاحظات والمجلدات"
  },
  {
    icon: Zap,
    title: "ذكاء اصطناعي متقدم",
    description: "وصول كامل لجميع ميزات الذكاء الاصطناعي"
  },
  {
    icon: Shield,
    title: "نسخ احتياطي تلقائي",
    description: "حماية بياناتك مع النسخ الاحتياطي السحابي"
  }
]

export function AccountUpgrade() {
  const { user } = useAuthStore()
  
  // Don't show upgrade card for Pro users
  if (user?.plan !== 'free') {
    return null
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.7 }}
    >
      <Card className="relative overflow-hidden border-border/50 bg-gradient-to-br from-primary/5 via-secondary/5 to-background">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-secondary/10 to-transparent" />
        
        {/* Animated Background Shapes */}
        <motion.div
          animate={{
            rotate: 360,
            scale: [1, 1.1, 1],
          }}
          transition={{
            rotate: { duration: 20, repeat: Infinity, ease: "linear" },
            scale: { duration: 8, repeat: Infinity, ease: "easeInOut" },
          }}
          className="absolute top-4 right-4 w-16 h-16 rounded-full bg-primary/10 blur-xl"
        />
        
        <motion.div
          animate={{
            rotate: -360,
            scale: [1, 1.2, 1],
          }}
          transition={{
            rotate: { duration: 25, repeat: Infinity, ease: "linear" },
            scale: { duration: 12, repeat: Infinity, ease: "easeInOut" },
          }}
          className="absolute bottom-4 left-4 w-20 h-20 rounded-full bg-secondary/10 blur-xl"
        />

        <CardContent className="relative z-10 p-8">
          <div className="text-center mb-6">
            {/* Crown Icon */}
            <motion.div
              animate={{ 
                rotate: [0, 5, -5, 0],
                scale: [1, 1.05, 1]
              }}
              transition={{ 
                duration: 3, 
                repeat: Infinity, 
                ease: "easeInOut" 
              }}
              className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full mb-4 shadow-lg"
            >
              <Crown className="w-8 h-8 text-white" />
            </motion.div>
            
            {/* Title */}
            <h3 className="text-2xl font-bold text-foreground mb-2">
              استمتع بقوة Nots Pro الكاملة
            </h3>
            
            <p className="text-muted-foreground mb-6 max-w-md mx-auto">
              ارفع مستوى تجربتك مع ميزات متقدمة وإمكانيات لا محدودة للذكاء الاصطناعي
            </p>
            
            {/* Pro Badge */}
            <Badge className="bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-4 py-1 text-sm font-medium mb-6">
              <Crown className="w-3 h-3 mr-1" />
              Nots Pro
            </Badge>
          </div>

          {/* Features List */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            {proFeatures.map((feature, index) => {
              const Icon = feature.icon
              
              return (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="flex items-start space-x-3 space-x-reverse p-4 rounded-lg bg-background/50 backdrop-blur-sm border border-border/30"
                >
                  <div className="w-8 h-8 rounded-lg bg-primary/10 flex items-center justify-center flex-shrink-0">
                    <Icon className="w-4 h-4 text-primary" />
                  </div>
                  
                  <div className="flex-1">
                    <h4 className="font-medium text-foreground text-sm mb-1">
                      {feature.title}
                    </h4>
                    <p className="text-xs text-muted-foreground">
                      {feature.description}
                    </p>
                  </div>
                  
                  <Check className="w-4 h-4 text-green-500 flex-shrink-0" />
                </motion.div>
              )
            })}
          </div>

          {/* Pricing */}
          <div className="text-center mb-6">
            <div className="flex items-center justify-center space-x-2 space-x-reverse mb-2">
              <span className="text-3xl font-bold text-foreground">29 ريال</span>
              <span className="text-muted-foreground">/شهر</span>
            </div>
            <p className="text-sm text-muted-foreground">
              أو 290 ريال سنوياً (وفر شهرين مجاناً)
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button
              size="lg"
              className="bg-gradient-to-r from-primary to-secondary hover:opacity-90 text-primary-foreground px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <Sparkles className="w-4 h-4 mr-2" />
              جرب Pro مجاناً لمدة 7 أيام
            </Button>
            
            <Button
              variant="outline"
              size="lg"
              className="border-2 border-primary text-primary hover:bg-primary hover:text-primary-foreground px-8 py-3 rounded-xl transition-all duration-300"
            >
              عرض جميع الميزات
              <ArrowLeft className="w-4 h-4 mr-2" />
            </Button>
          </div>

          {/* Trust Indicators */}
          <div className="flex items-center justify-center space-x-6 space-x-reverse mt-6 pt-6 border-t border-border/30 text-xs text-muted-foreground">
            <div className="flex items-center space-x-1 space-x-reverse">
              <Shield className="w-3 h-3" />
              <span>إلغاء في أي وقت</span>
            </div>
            <div className="flex items-center space-x-1 space-x-reverse">
              <Check className="w-3 h-3 text-green-500" />
              <span>ضمان استرداد 30 يوم</span>
            </div>
            <div className="flex items-center space-x-1 space-x-reverse">
              <Zap className="w-3 h-3" />
              <span>تفعيل فوري</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
