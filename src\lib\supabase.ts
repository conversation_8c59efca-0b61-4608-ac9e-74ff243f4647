import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://eidaboqpmhfvrroersjh.supabase.co'
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVpZGFib3FwbWhmdnJyb2Vyc2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIzMTMyMzksImV4cCI6MjA2Nzg4OTIzOX0.k8KFKbp3mc0sNF4OHPHIlf9ug1ZQajy7y-ip-fYpA8g'

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Types for our database
export interface Profile {
  id: string
  full_name: string
  email: string
  avatar_url?: string
  phone_number?: string
  country_code?: string
  language_preference: 'ar' | 'en'
  theme_preference: 'light' | 'dark' | 'system'
  usage_intent?: 'student' | 'employee' | 'creator' | 'personal'
  smart_features: {
    autoSummarization: boolean
    speechToText: boolean
    ideaExpansion: boolean
    enableAllFeatures: boolean
  }
  plan_choice: 'free' | 'upgrade'
  selected_folders: string[]
  custom_folders: string[]
  subscription_status: 'active' | 'inactive' | 'cancelled' | 'trial'
  subscription_expiry?: string
  email_verified: boolean
  profile_completed: boolean
  last_login?: string
  failed_login_attempts: number
  notes_count: number
  folders_count: number
  notifications_enabled: boolean
  metadata: Record<string, any>
  created_at: string
  updated_at: string
}

// Database types for better type safety
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: Profile
        Insert: Omit<Profile, 'id' | 'created_at' | 'updated_at'> & {
          id: string
        }
        Update: Partial<Omit<Profile, 'id' | 'created_at' | 'updated_at'>>
      }
    }
  }
}

// Helper functions for common operations
export const profileHelpers = {
  async getProfile(userId: string): Promise<Profile | null> {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single()
    
    if (error) {
      console.error('Error fetching profile:', error)
      return null
    }
    
    return data
  },

  async createProfile(profile: Database['public']['Tables']['profiles']['Insert']): Promise<Profile | null> {
    const { data, error } = await supabase
      .from('profiles')
      .insert(profile)
      .select()
      .single()
    
    if (error) {
      console.error('Error creating profile:', error)
      return null
    }
    
    return data
  },

  async updateProfile(userId: string, updates: Database['public']['Tables']['profiles']['Update']): Promise<Profile | null> {
    const { data, error } = await supabase
      .from('profiles')
      .update(updates)
      .eq('id', userId)
      .select()
      .single()
    
    if (error) {
      console.error('Error updating profile:', error)
      return null
    }
    
    return data
  },

  async updateLastLogin(userId: string): Promise<void> {
    await supabase
      .from('profiles')
      .update({ last_login: new Date().toISOString() })
      .eq('id', userId)
  }
}

// Auth error helper
export const getAuthErrorMessage = (error: any): string => {
  const message = error?.message || error?.error_description || ''
  
  switch (message) {
    case 'Invalid login credentials':
      return 'بيانات تسجيل الدخول غير صحيحة'
    case 'Email not confirmed':
      return 'يرجى تأكيد بريدك الإلكتروني أولاً'
    case 'User already registered':
      return 'هذا البريد الإلكتروني مسجل مسبقاً'
    case 'Password should be at least 6 characters':
      return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'
    case 'Unable to validate email address: invalid format':
      return 'تنسيق البريد الإلكتروني غير صحيح'
    case 'Signup is disabled':
      return 'التسجيل معطل حالياً'
    case 'Email rate limit exceeded':
      return 'تم تجاوز حد إرسال الرسائل. يرجى المحاولة لاحقاً'
    case 'Token has expired or is invalid':
      return 'انتهت صلاحية الرابط أو أنه غير صالح'
    case 'New password should be different from the old password':
      return 'كلمة المرور الجديدة يجب أن تكون مختلفة عن القديمة'
    default:
      if (message.includes('duplicate key value violates unique constraint')) {
        return 'هذا البريد الإلكتروني مسجل مسبقاً'
      }
      return 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى'
  }
}
