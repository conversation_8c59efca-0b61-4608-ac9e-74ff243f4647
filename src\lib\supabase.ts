import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://eidaboqpmhfvrroersjh.supabase.co'
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVpZGFib3FwbWhmdnJyb2Vyc2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIzMTMyMzksImV4cCI6MjA2Nzg4OTIzOX0.k8KFKbp3mc0sNF4OHPHIlf9ug1ZQajy7y-ip-fYpA8g'

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Types for our database
export interface Profile {
  id: string
  full_name: string
  email: string
  avatar_url?: string
  phone_number?: string
  country_code?: string
  language_preference: 'ar' | 'en'
  theme_preference: 'light' | 'dark' | 'system'
  usage_intent?: 'student' | 'employee' | 'creator' | 'personal'
  smart_features: {
    autoSummarization: boolean
    speechToText: boolean
    ideaExpansion: boolean
    enableAllFeatures: boolean
  }
  plan_choice: 'free' | 'upgrade'
  selected_folders: string[]
  custom_folders: string[]
  subscription_status: 'active' | 'inactive' | 'cancelled' | 'trial'
  subscription_expiry?: string
  email_verified: boolean
  profile_completed: boolean
  last_login?: string
  failed_login_attempts: number
  notes_count: number
  folders_count: number
  notifications_enabled: boolean
  metadata: Record<string, any>
  created_at: string
  updated_at: string
}

export interface Category {
  id: string
  user_id: string
  name: string
  description?: string | null
  color: string
  icon: string
  notes_count: number
  is_pinned: boolean
  is_favorite: boolean
  sort_order: number
  created_at: string
  updated_at: string
}

export interface Note {
  id: string
  user_id: string
  category_id?: string | null
  title: string
  content?: string | null
  category?: string
  categoryColor?: string
  isPinned: boolean
  isFavorite: boolean
  lastEdited: string
  wordCount: number
  readingTime: number
  tags?: string[]
  created_at: string
  updated_at: string
}

// Database types for better type safety
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: Profile
        Insert: Omit<Profile, 'id' | 'created_at' | 'updated_at'> & {
          id: string
        }
        Update: Partial<Omit<Profile, 'id' | 'created_at' | 'updated_at'>>
      }
    }
  }
}

// Helper functions for common operations
export const profileHelpers = {
  async getProfile(userId: string): Promise<Profile | null> {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single()
    
    if (error) {
      console.error('Error fetching profile:', error)
      return null
    }
    
    return data
  },

  async createProfile(profile: Database['public']['Tables']['profiles']['Insert']): Promise<Profile | null> {
    const { data, error } = await supabase
      .from('profiles')
      .insert(profile)
      .select()
      .single()
    
    if (error) {
      console.error('Error creating profile:', error)
      return null
    }
    
    return data
  },

  async updateProfile(userId: string, updates: Database['public']['Tables']['profiles']['Update']): Promise<Profile | null> {
    const { data, error } = await supabase
      .from('profiles')
      .update(updates)
      .eq('id', userId)
      .select()
      .single()
    
    if (error) {
      console.error('Error updating profile:', error)
      return null
    }
    
    return data
  },

  async updateLastLogin(userId: string): Promise<void> {
    await supabase
      .from('profiles')
      .update({ last_login: new Date().toISOString() })
      .eq('id', userId)
  }
}

// Auth error helper
export const getAuthErrorMessage = (error: any): string => {
  const message = error?.message || error?.error_description || ''
  
  switch (message) {
    case 'Invalid login credentials':
      return 'بيانات تسجيل الدخول غير صحيحة'
    case 'Email not confirmed':
      return 'يرجى تأكيد بريدك الإلكتروني أولاً'
    case 'User already registered':
      return 'هذا البريد الإلكتروني مسجل مسبقاً'
    case 'Password should be at least 6 characters':
      return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'
    case 'Unable to validate email address: invalid format':
      return 'تنسيق البريد الإلكتروني غير صحيح'
    case 'Signup is disabled':
      return 'التسجيل معطل حالياً'
    case 'Email rate limit exceeded':
      return 'تم تجاوز حد إرسال الرسائل. يرجى المحاولة لاحقاً'
    case 'Token has expired or is invalid':
      return 'انتهت صلاحية الرابط أو أنه غير صالح'
    case 'New password should be different from the old password':
      return 'كلمة المرور الجديدة يجب أن تكون مختلفة عن القديمة'
    default:
      if (message.includes('duplicate key value violates unique constraint')) {
        return 'هذا البريد الإلكتروني مسجل مسبقاً'
      }
      return 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى'
  }
}

// Category helpers
export const categoryHelpers = {
  async getCategories(userId: string): Promise<Category[]> {
    try {
      if (!userId) {
        console.warn('No user ID provided to getCategories')
        return []
      }

      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .eq('user_id', userId)
        .order('sort_order', { ascending: true })
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching categories:', error)
        throw new Error(`Failed to fetch categories: ${error.message}`)
      }

      console.log('Fetched categories:', data?.length || 0)
      return data || []
    } catch (error) {
      console.error('Error in getCategories:', error)
      throw error
    }
  },

  async createCategory(categoryData: Omit<Category, 'id' | 'created_at' | 'updated_at'>): Promise<Category> {
    try {
      if (!categoryData.user_id) {
        throw new Error('User ID is required to create a category')
      }

      console.log('Creating category with data:', categoryData)

      const { data, error } = await supabase
        .from('categories')
        .insert([{
          ...categoryData,
          notes_count: 0,
          is_pinned: categoryData.is_pinned || false,
          is_favorite: categoryData.is_favorite || false,
          sort_order: categoryData.sort_order || 0
        }])
        .select()
        .single()

      if (error) {
        console.error('Error creating category:', error)
        throw new Error(`Failed to create category: ${error.message}`)
      }

      if (!data) {
        throw new Error('No data returned from category creation')
      }

      console.log('Category created successfully:', data)
      return data
    } catch (error) {
      console.error('Error in createCategory:', error)
      throw error
    }
  },

  async updateCategory(id: string, updates: Partial<Omit<Category, 'id' | 'user_id' | 'created_at' | 'updated_at'>>): Promise<Category> {
    try {
      if (!id) {
        throw new Error('Category ID is required for update')
      }

      console.log('Updating category:', id, updates)

      const { data, error } = await supabase
        .from('categories')
        .update(updates)
        .eq('id', id)
        .select()
        .single()

      if (error) {
        console.error('Error updating category:', error)
        throw new Error(`Failed to update category: ${error.message}`)
      }

      if (!data) {
        throw new Error('No data returned from category update')
      }

      console.log('Category updated successfully:', data)
      return data
    } catch (error) {
      console.error('Error in updateCategory:', error)
      throw error
    }
  },

  async deleteCategory(id: string): Promise<void> {
    try {
      if (!id) {
        throw new Error('Category ID is required for deletion')
      }

      console.log('Deleting category:', id)

      const { error } = await supabase
        .from('categories')
        .delete()
        .eq('id', id)

      if (error) {
        console.error('Error deleting category:', error)
        throw new Error(`Failed to delete category: ${error.message}`)
      }

      console.log('Category deleted successfully')
    } catch (error) {
      console.error('Error in deleteCategory:', error)
      throw error
    }
  },

  // Real-time subscription for categories
  subscribeToCategories(userId: string, callback: (categories: Category[]) => void) {
    if (!userId) {
      console.warn('No user ID provided for subscription')
      return null
    }

    console.log('Setting up real-time subscription for user:', userId)

    const subscription = supabase
      .channel('categories_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'categories',
          filter: `user_id=eq.${userId}`
        },
        async (payload) => {
          console.log('Real-time category change:', payload)
          try {
            // Refetch all categories to ensure consistency
            const categories = await categoryHelpers.getCategories(userId)
            callback(categories)
          } catch (error) {
            console.error('Error handling real-time update:', error)
          }
        }
      )
      .subscribe()

    return subscription
  }
}
