
import { Moon, Sun } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useGlobalTheme } from "./GlobalThemeProvider"

export function ThemeToggle() {
  const { theme, setTheme } = useGlobalTheme()

  const handleThemeToggle = () => {
    if (theme === "light") {
      setTheme("dark")
    } else if (theme === "dark") {
      setTheme("system")
    } else {
      setTheme("light")
    }
  }

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={handleThemeToggle}
      className="h-10 w-10 hover:bg-accent/50 transition-colors"
      title={`الوضع الحالي: ${theme === "light" ? "فاتح" : theme === "dark" ? "داكن" : "النظام"}`}
    >
      <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
      <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
      <span className="sr-only">تبديل الوضع</span>
    </Button>
  )
}
