import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { 
  Tag,
  Plus,
  X,
  Hash
} from "lucide-react"

interface TagsManagerProps {
  tags: string[]
  onTagsChange: (tags: string[]) => void
}

export function TagsManager({ tags, onTagsChange }: TagsManagerProps) {
  const [newTag, setNewTag] = useState("")
  const [isAddingTag, setIsAddingTag] = useState(false)

  // Suggested tags based on common usage
  const suggestedTags = [
    "مهم", "عاجل", "فكرة", "مشروع", "اجتماع", 
    "مهمة", "تذكير", "بحث", "دراسة", "عمل"
  ]

  const addTag = () => {
    const trimmedTag = newTag.trim()
    if (trimmedTag && !tags.includes(trimmedTag)) {
      onTagsChange([...tags, trimmedTag])
      setNewTag("")
      setIsAddingTag(false)
    }
  }

  const removeTag = (tagToRemove: string) => {
    onTagsChange(tags.filter(tag => tag !== tagToRemove))
  }

  const addSuggestedTag = (tag: string) => {
    if (!tags.includes(tag)) {
      onTagsChange([...tags, tag])
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      addTag()
    } else if (e.key === 'Escape') {
      setIsAddingTag(false)
      setNewTag("")
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.1 }}
      className="space-y-4"
    >
      {/* Header */}
      <div className="flex items-center space-x-2 space-x-reverse">
        <div className="w-8 h-8 bg-gradient-to-r from-secondary/20 to-accent/20 rounded-lg flex items-center justify-center">
          <Tag className="w-4 h-4 text-secondary" />
        </div>
        <Label className="text-sm font-semibold text-foreground">
          العلامات
        </Label>
      </div>

      {/* Current Tags */}
      <div className="space-y-3">
        {tags.length > 0 && (
          <div className="flex flex-wrap gap-2">
            <AnimatePresence>
              {tags.map((tag, index) => (
                <motion.div
                  key={tag}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  transition={{ duration: 0.2 }}
                >
                  <Badge 
                    variant="secondary" 
                    className="flex items-center space-x-1 space-x-reverse bg-primary/10 text-primary border border-primary/20 hover:bg-primary/20 transition-colors"
                  >
                    <Hash className="w-3 h-3" />
                    <span>{tag}</span>
                    <button
                      onClick={() => removeTag(tag)}
                      className="ml-1 hover:bg-primary/20 rounded-full p-0.5 transition-colors"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </Badge>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        )}

        {/* Add New Tag */}
        <div className="space-y-2">
          {isAddingTag ? (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              className="flex space-x-2 space-x-reverse"
            >
              <Input
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="أضف علامة جديدة..."
                className="flex-1 rounded-xl border-border/50 bg-background/50"
                autoFocus
                dir="rtl"
              />
              <Button
                onClick={addTag}
                disabled={!newTag.trim()}
                size="sm"
                className="bg-gradient-to-r from-primary to-secondary hover:opacity-90 text-white rounded-xl"
              >
                <Plus className="w-4 h-4" />
              </Button>
              <Button
                onClick={() => {
                  setIsAddingTag(false)
                  setNewTag("")
                }}
                variant="outline"
                size="sm"
                className="rounded-xl border-border/50"
              >
                <X className="w-4 h-4" />
              </Button>
            </motion.div>
          ) : (
            <Button
              onClick={() => setIsAddingTag(true)}
              variant="outline"
              size="sm"
              className="w-full justify-start text-muted-foreground hover:text-foreground rounded-xl border-border/50 border-dashed"
            >
              <Plus className="w-4 h-4 mr-2" />
              إضافة علامة
            </Button>
          )}
        </div>
      </div>

      {/* Suggested Tags */}
      {suggestedTags.some(tag => !tags.includes(tag)) && (
        <div className="pt-3 border-t border-border/30">
          <Label className="text-xs text-muted-foreground mb-2 block">
            علامات مقترحة:
          </Label>
          <div className="flex flex-wrap gap-2">
            {suggestedTags
              .filter(tag => !tags.includes(tag))
              .slice(0, 6)
              .map((tag) => (
                <motion.div
                  key={tag}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    onClick={() => addSuggestedTag(tag)}
                    variant="ghost"
                    size="sm"
                    className="h-7 px-2 text-xs text-muted-foreground hover:text-foreground hover:bg-muted/50 rounded-lg border border-border/30"
                  >
                    <Hash className="w-3 h-3 mr-1" />
                    {tag}
                  </Button>
                </motion.div>
              ))}
          </div>
        </div>
      )}

      {/* Tags Statistics */}
      {tags.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
          className="p-3 bg-muted/30 rounded-xl border border-border/30"
        >
          <div className="text-xs text-muted-foreground space-y-1">
            <div className="flex justify-between">
              <span>عدد العلامات:</span>
              <span>{tags.length} علامة</span>
            </div>
            <div className="flex justify-between">
              <span>الأكثر استخداماً:</span>
              <span>{tags[0] || 'لا يوجد'}</span>
            </div>
          </div>
        </motion.div>
      )}
    </motion.div>
  )
}
