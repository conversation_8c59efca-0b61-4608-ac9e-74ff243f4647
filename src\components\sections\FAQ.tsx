import { useState } from "react"
import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { 
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import { HelpCircle, Search, MessageCircle, Mail, Phone } from "lucide-react"

const faqs = [
  {
    id: "1",
    question: "ما هو Nots وكيف يعمل؟",
    answer: "Nots هو تطبيق ملاحظات ذكي يستخدم الذكاء الاصطناعي لتحليل وتنظيم ملاحظاتك. يمكنه تلخيص النصوص الطويلة، توسيع الأفكار البسيطة، تحويل الصوت إلى نص، وتصنيف الملاحظات تلقائياً. كل ذلك مع دعم كامل للغة العربية."
  },
  {
    id: "2", 
    question: "هل Nots مجاني؟",
    answer: "نعم، Nots يقدم خطة مجانية تتضمن الميزات الأساسية مثل إنشاء الملاحظات والتنظيم البسيط. كما نوفر خطط مدفوعة تتضمن ميزات متقدمة مثل الذكاء الاصطناعي المتطور، مساحة تخزين أكبر، ومزامنة غير محدودة."
  },
  {
    id: "3",
    question: "هل يدعم التطبيق اللغة العربية بالكامل؟", 
    answer: "بالطبع! Nots مصمم خصيصاً للمستخدمين العرب. الواجهة بالكامل باللغة العربية مع دعم الكتابة من اليمين إلى اليسار (RTL). الذكاء الاصطناعي مدرب على فهم السياق العربي والتعامل مع النصوص العربية بدقة عالية."
  },
  {
    id: "4",
    question: "كيف يحمي Nots خصوصية بياناتي؟",
    answer: "نحن نأخذ الخصوصية على محمل الجد. جميع بياناتك مشفرة بتقنية 256 بت SSL، ولا نشارك معلوماتك مع أطراف ثالثة. يمكنك حذف حسابك وجميع بياناتك في أي وقت. كما نلتزم بمعايير GDPR لحماية البيانات."
  },
  {
    id: "5",
    question: "هل يمكنني استخدام Nots على أجهزة متعددة؟",
    answer: "نعم، Nots متاح على جميع المنصات - الويب، iOS، وAndroid. تتم مزامنة ملاحظاتك تلقائياً عبر جميع أجهزتك، لذا يمكنك الوصول إليها من أي مكان وفي أي وقت."
  },
  {
    id: "6",
    question: "ما هي دقة تحويل الصوت إلى نص؟",
    answer: "تقنية التعرف على الصوت في Nots تحقق دقة تصل إلى 99.5% للغة العربية. نستخدم أحدث تقنيات الذكاء الاصطناعي المدربة خصيصاً على اللهجات العربية المختلفة لضمان أفضل النتائج."
  },
  {
    id: "7",
    question: "هل يمكنني تصدير ملاحظاتي؟",
    answer: "بالطبع! يمكنك تصدير ملاحظاتك بصيغ متعددة مثل PDF، Word، أو نص عادي. كما يمكنك عمل نسخة احتياطية كاملة من جميع بياناتك في أي وقت."
  },
  {
    id: "8",
    question: "كيف يمكنني الحصول على الدعم الفني؟",
    answer: "نقدم دعماً فنياً على مدار الساعة عبر عدة قنوات: الدردشة المباشرة في التطبيق، البريد الإلكتروني، أو الهاتف. كما لدينا مركز مساعدة شامل يحتوي على أدلة مفصلة وفيديوهات تعليمية."
  },
  {
    id: "9",
    question: "هل يعمل Nots بدون اتصال بالإنترنت؟",
    answer: "نعم، يمكنك استخدام Nots للقراءة والكتابة حتى بدون اتصال بالإنترنت. ستتم مزامنة التغييرات تلقائياً عند عودة الاتصال. ومع ذلك، ميزات الذكاء الاصطناعي تتطلب اتصالاً بالإنترنت للعمل."
  },
  {
    id: "10",
    question: "كيف يمكنني ترقية حسابي؟",
    answer: "يمكنك ترقية حسابك في أي وقت من خلال إعدادات التطبيق أو موقعنا الإلكتروني. نقبل جميع طرق الدفع الرئيسية ونقدم ضمان استرداد الأموال خلال 30 يوماً إذا لم تكن راضياً عن الخدمة."
  }
]

export function FAQ() {
  const [searchQuery, setSearchQuery] = useState("")
  const [filteredFAQs, setFilteredFAQs] = useState(faqs)

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    if (query.trim() === "") {
      setFilteredFAQs(faqs)
    } else {
      const filtered = faqs.filter(
        faq => 
          faq.question.toLowerCase().includes(query.toLowerCase()) ||
          faq.answer.toLowerCase().includes(query.toLowerCase())
      )
      setFilteredFAQs(filtered)
    }
  }

  return (
    <section className="py-20 bg-background" id="faq">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-purple/10 text-primary font-medium mb-6">
            <HelpCircle className="w-5 h-5" />
            <span>❓ الأسئلة الشائعة</span>
          </div>
          
          <h2 className="text-3xl md:text-5xl font-bold nots-heading mb-6">
            أجوبة على <span className="bg-gradient-purple bg-clip-text text-transparent">أسئلتك</span>
            <br />الأكثر شيوعاً
          </h2>
          
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            لديك سؤال؟ ربما تجد الإجابة هنا. إذا لم تجد ما تبحث عنه، 
            لا تتردد في التواصل مع فريق الدعم.
          </p>
        </motion.div>

        {/* Search Bar */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="max-w-2xl mx-auto mb-12"
        >
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
            <Input
              type="text"
              placeholder="ابحث في الأسئلة الشائعة..."
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="pr-12 py-3 text-lg rounded-xl border-border/50 focus:border-primary"
            />
          </div>
        </motion.div>

        {/* FAQ Accordion */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="max-w-4xl mx-auto mb-16"
        >
          {filteredFAQs.length > 0 ? (
            <Accordion type="single" collapsible className="space-y-4">
              {filteredFAQs.map((faq, index) => (
                <motion.div
                  key={faq.id}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.05 }}
                  viewport={{ once: true }}
                >
                  <AccordionItem 
                    value={faq.id} 
                    className="border border-border/50 rounded-xl px-6 nots-shadow hover:nots-shadow-lg transition-all duration-300"
                  >
                    <AccordionTrigger className="text-right hover:no-underline py-6">
                      <span className="font-semibold text-foreground pr-4">
                        {faq.question}
                      </span>
                    </AccordionTrigger>
                    <AccordionContent className="text-muted-foreground leading-relaxed pb-6">
                      {faq.answer}
                    </AccordionContent>
                  </AccordionItem>
                </motion.div>
              ))}
            </Accordion>
          ) : (
            <div className="text-center py-12">
              <Search className="w-16 h-16 text-muted-foreground/50 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">لم نجد نتائج</h3>
              <p className="text-muted-foreground">
                جرب كلمات مختلفة أو تواصل مع فريق الدعم للمساعدة
              </p>
            </div>
          )}
        </motion.div>

        {/* Contact Support Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          viewport={{ once: true }}
          className="bg-nots-bg-light/50 rounded-2xl p-8 md:p-12"
        >
          <div className="text-center mb-8">
            <h3 className="text-2xl md:text-3xl font-bold nots-heading mb-4">
              لم تجد إجابة لسؤالك؟
            </h3>
            <p className="text-muted-foreground">
              فريق الدعم متاح على مدار الساعة لمساعدتك
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="text-center p-6 nots-shadow hover:nots-shadow-lg transition-all duration-300 border-border/50 group">
              <CardContent className="p-0">
                <div className="w-16 h-16 bg-nots-blue/10 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <MessageCircle className="w-8 h-8 text-nots-blue" />
                </div>
                <h4 className="font-semibold mb-2">دردشة مباشرة</h4>
                <p className="text-sm text-muted-foreground mb-4">
                  تحدث مع فريق الدعم فوراً
                </p>
                <Button variant="outline" size="sm" className="text-nots-blue border-nots-blue hover:bg-nots-blue hover:text-white">
                  ابدأ المحادثة
                </Button>
              </CardContent>
            </Card>

            <Card className="text-center p-6 nots-shadow hover:nots-shadow-lg transition-all duration-300 border-border/50 group">
              <CardContent className="p-0">
                <div className="w-16 h-16 bg-nots-green/10 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <Mail className="w-8 h-8 text-nots-green" />
                </div>
                <h4 className="font-semibold mb-2">البريد الإلكتروني</h4>
                <p className="text-sm text-muted-foreground mb-4">
                  أرسل لنا رسالة مفصلة
                </p>
                <Button variant="outline" size="sm" className="text-nots-green border-nots-green hover:bg-nots-green hover:text-white">
                  إرسال رسالة
                </Button>
              </CardContent>
            </Card>

            <Card className="text-center p-6 nots-shadow hover:nots-shadow-lg transition-all duration-300 border-border/50 group">
              <CardContent className="p-0">
                <div className="w-16 h-16 bg-nots-orange/10 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <Phone className="w-8 h-8 text-nots-orange" />
                </div>
                <h4 className="font-semibold mb-2">الهاتف</h4>
                <p className="text-sm text-muted-foreground mb-4">
                  اتصل بنا مباشرة
                </p>
                <Button variant="outline" size="sm" className="text-nots-orange border-nots-orange hover:bg-nots-orange hover:text-white">
                  +966 11 123 4567
                </Button>
              </CardContent>
            </Card>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
