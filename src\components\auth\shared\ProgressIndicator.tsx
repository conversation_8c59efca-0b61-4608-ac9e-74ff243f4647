import { motion } from "framer-motion"
import { Check } from "lucide-react"

interface ProgressIndicatorProps {
  currentStep: number
  totalSteps: number
  onStepClick?: (step: number) => void
  stepLabels?: string[]
}

const defaultStepLabels = [
  "المعلومات الأساسية",
  "نوع الاستخدام", 
  "الميزات الذكية",
  "خطة الاشتراك",
  "الإعداد الأولي"
]

export function ProgressIndicator({ 
  currentStep, 
  totalSteps, 
  onStepClick,
  stepLabels = defaultStepLabels 
}: ProgressIndicatorProps) {
  return (
    <div className="w-full mb-8">
      {/* Progress Bar */}
      <div className="relative">
        <div className="flex items-center justify-between mb-4">
          {Array.from({ length: totalSteps }, (_, index) => {
            const stepNumber = index + 1
            const isCompleted = stepNumber < currentStep
            const isCurrent = stepNumber === currentStep
            const isClickable = isCompleted && onStepClick
            
            return (
              <div key={stepNumber} className="flex flex-col items-center">
                {/* Step Circle */}
                <motion.button
                  onClick={() => isClickable && onStepClick(stepNumber)}
                  disabled={!isClickable}
                  className={`
                    relative w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium
                    transition-all duration-300 mb-2
                    ${isCompleted 
                      ? 'bg-primary text-primary-foreground shadow-lg' 
                      : isCurrent 
                        ? 'bg-primary/20 text-primary border-2 border-primary' 
                        : 'bg-muted text-muted-foreground border border-border'
                    }
                    ${isClickable ? 'cursor-pointer hover:scale-110' : 'cursor-default'}
                  `}
                  whileHover={isClickable ? { scale: 1.1 } : {}}
                  whileTap={isClickable ? { scale: 0.95 } : {}}
                >
                  {isCompleted ? (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ duration: 0.3 }}
                    >
                      <Check className="w-5 h-5" />
                    </motion.div>
                  ) : (
                    <span>{stepNumber}</span>
                  )}
                </motion.button>
                
                {/* Step Label */}
                <span className={`
                  text-xs text-center max-w-20 leading-tight
                  ${isCurrent ? 'text-primary font-medium' : 'text-muted-foreground'}
                `}>
                  {stepLabels[index]}
                </span>
              </div>
            )
          })}
        </div>
        
        {/* Progress Line */}
        <div className="absolute top-5 left-5 right-5 h-0.5 bg-muted -z-10">
          <motion.div
            className="h-full bg-primary"
            initial={{ width: '0%' }}
            animate={{ 
              width: `${((currentStep - 1) / (totalSteps - 1)) * 100}%` 
            }}
            transition={{ duration: 0.5, ease: "easeInOut" }}
          />
        </div>
      </div>
      
      {/* Current Step Info */}
      <div className="text-center">
        <p className="text-sm text-muted-foreground">
          الخطوة {currentStep} من {totalSteps}
        </p>
      </div>
    </div>
  )
}
