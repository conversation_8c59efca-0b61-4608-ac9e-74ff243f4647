'use client'

import { motion } from 'framer-motion'
import { 
  Crown, 
  Zap, 
  Shield, 
  Cloud, 
  <PERSON>rk<PERSON>, 
  ArrowRight,
  Check,
  Star
} from 'lucide-react'
import { Button } from '@/components/ui/button'

interface Feature {
  icon: React.ReactNode
  title: string
  description: string
}

export function UpgradeCTA() {
  const features: Feature[] = [
    {
      icon: <Zap className="w-5 h-5" />,
      title: 'ذكاء اصطناعي متقدم',
      description: 'تحليل وملخصات ذكية لا محدودة'
    },
    {
      icon: <Cloud className="w-5 h-5" />,
      title: 'نسخ احتياطي تلقائي',
      description: 'حماية كاملة لجميع ملاحظاتك'
    },
    {
      icon: <Shield className="w-5 h-5" />,
      title: 'أمان متقدم',
      description: 'تشفير من الطراز العسكري'
    }
  ]

  const benefits = [
    'ملاحظات غير محدودة',
    'مساحة تخزين 100 جيجابايت',
    'مزامنة عبر جميع الأجهزة',
    'تصدير بصيغ متعددة',
    'دعم فني أولوية',
    'ميزات حصرية جديدة'
  ]

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.6, delay: 0.3 }}
      className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-primary via-primary/90 to-secondary shadow-2xl"
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-0 left-0 w-32 h-32 bg-white rounded-full -translate-x-16 -translate-y-16" />
        <div className="absolute top-1/2 right-0 w-24 h-24 bg-white rounded-full translate-x-12 -translate-y-12" />
        <div className="absolute bottom-0 left-1/3 w-20 h-20 bg-white rounded-full translate-y-10" />
      </div>

      {/* Floating Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          animate={{ 
            y: [0, -10, 0],
            rotate: [0, 5, 0]
          }}
          transition={{ 
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute top-6 right-6 text-white/20"
        >
          <Sparkles className="w-8 h-8" />
        </motion.div>
        
        <motion.div
          animate={{ 
            y: [0, 10, 0],
            rotate: [0, -5, 0]
          }}
          transition={{ 
            duration: 3,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1
          }}
          className="absolute bottom-8 left-8 text-white/20"
        >
          <Star className="w-6 h-6" />
        </motion.div>
      </div>

      <div className="relative p-8 text-white">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center mb-8"
        >
          <div className="flex items-center justify-center gap-2 mb-4">
            <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
              <Crown className="w-6 h-6 text-white" />
            </div>
            <div className="px-3 py-1 bg-white/20 rounded-full backdrop-blur-sm">
              <span className="text-sm font-medium">نوتس برو</span>
            </div>
          </div>
          
          <h3 className="text-2xl font-bold mb-2">
            ارتقِ إلى المستوى التالي
          </h3>
          
          <p className="text-white/80 text-sm leading-relaxed">
            اكتشف قوة الذكاء الاصطناعي المتقدم وميزات حصرية 
            لتجربة كتابة استثنائية
          </p>
        </motion.div>

        {/* Features Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
          className="grid grid-cols-1 gap-4 mb-8"
        >
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}
              className="flex items-start gap-3 p-4 bg-white/10 rounded-xl backdrop-blur-sm border border-white/20"
            >
              <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center flex-shrink-0">
                {feature.icon}
              </div>
              <div className="flex-1">
                <h4 className="font-semibold text-sm mb-1">{feature.title}</h4>
                <p className="text-white/70 text-xs leading-relaxed">{feature.description}</p>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Benefits List */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="mb-8"
        >
          <h4 className="font-semibold text-sm mb-4 text-center">ما ستحصل عليه:</h4>
          <div className="grid grid-cols-1 gap-2">
            {benefits.map((benefit, index) => (
              <motion.div
                key={benefit}
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.4, delay: 0.9 + index * 0.05 }}
                className="flex items-center gap-3 text-sm"
              >
                <div className="w-4 h-4 bg-white/20 rounded-full flex items-center justify-center flex-shrink-0">
                  <Check className="w-2.5 h-2.5" />
                </div>
                <span className="text-white/90">{benefit}</span>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Pricing */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1.0 }}
          className="text-center mb-8"
        >
          <div className="flex items-center justify-center gap-2 mb-2">
            <span className="text-3xl font-bold">٢٩ ريال</span>
            <span className="text-white/70 text-sm">/شهرياً</span>
          </div>
          <p className="text-white/60 text-xs">
            أو ٢٩٠ ريال سنوياً (وفر ٢٠٪)
          </p>
        </motion.div>

        {/* CTA Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1.1 }}
          className="space-y-3"
        >
          <Button
            size="lg"
            className="w-full bg-white text-primary hover:bg-white/90 font-semibold shadow-lg hover:shadow-xl transition-all duration-300 group"
          >
            <span>ابدأ الآن - تجربة مجانية ٧ أيام</span>
            <ArrowRight className="w-4 h-4 mr-2 group-hover:translate-x-1 transition-transform" />
          </Button>
          
          <p className="text-center text-white/60 text-xs">
            لا توجد رسوم خفية • إلغاء في أي وقت
          </p>
        </motion.div>

        {/* Trust Indicators */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 1.2 }}
          className="flex items-center justify-center gap-4 mt-6 pt-6 border-t border-white/20"
        >
          <div className="flex items-center gap-1 text-white/70 text-xs">
            <Shield className="w-3 h-3" />
            <span>آمن ١٠٠٪</span>
          </div>
          <div className="flex items-center gap-1 text-white/70 text-xs">
            <Star className="w-3 h-3" />
            <span>تقييم ٤.٩/٥</span>
          </div>
          <div className="flex items-center gap-1 text-white/70 text-xs">
            <Crown className="w-3 h-3" />
            <span>+١٠ آلاف مستخدم</span>
          </div>
        </motion.div>
      </div>
    </motion.div>
  )
}
