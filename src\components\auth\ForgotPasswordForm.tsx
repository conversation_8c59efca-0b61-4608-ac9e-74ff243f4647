import { motion } from "framer-motion"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Link } from "react-router-dom"
import { Mail, ArrowRight } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { AuthLayout } from "./shared/AuthLayout"
import { forgotPasswordSchema, type ForgotPasswordFormData } from "@/lib/validations/auth"
import { useAuthStore } from "@/stores/authStore"
import { toast } from "sonner"

export function ForgotPasswordForm() {
  const { forgotPassword, isLoading } = useAuthStore()

  const {
    register,
    handleSubmit,
    formState: { errors, isValid }
  } = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordSchema),
    mode: "onChange"
  })

  const onSubmit = async (data: ForgotPasswordFormData) => {
    const success = await forgotPassword(data.email)
    
    if (success) {
      toast.success("✅ تم إرسال رابط إعادة التعيين!", {
        description: "تحقق من بريدك الإلكتروني واتبع التعليمات"
      })
    } else {
      toast.error("فشل في إرسال رابط إعادة التعيين", {
        description: "يرجى التحقق من البريد الإلكتروني والمحاولة مرة أخرى"
      })
    }
  }

  return (
    <AuthLayout
      title="نسيت كلمة المرور؟"
      description="أدخل بريدك الإلكتروني وسنرسل لك رابط إعادة تعيين كلمة المرور"
    >
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Email Field */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="space-y-2"
        >
          <Label htmlFor="email" className="text-sm font-medium text-foreground">
            البريد الإلكتروني
          </Label>
          <div className="relative">
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              className={`pr-10 transition-all duration-200 ${
                errors.email ? 'border-red-500 focus:border-red-500' : ''
              }`}
              {...register("email")}
            />
            <Mail className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          </div>
          {errors.email && (
            <motion.p
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-sm text-red-500"
            >
              {errors.email.message}
            </motion.p>
          )}
        </motion.div>

        {/* Submit Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Button
            type="submit"
            disabled={!isValid || isLoading}
            className="w-full bg-gradient-to-r from-primary to-secondary hover:opacity-90 text-primary-foreground py-3 text-lg font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <div className="flex items-center space-x-2 space-x-reverse">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                <span>جاري الإرسال...</span>
              </div>
            ) : (
              'إرسال رابط إعادة التعيين'
            )}
          </Button>
        </motion.div>

        {/* Back to Login */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="text-center"
        >
          <Link
            to="/auth/login"
            className="inline-flex items-center space-x-1 space-x-reverse text-sm text-primary hover:text-primary/80 transition-colors"
          >
            <ArrowRight className="w-4 h-4" />
            <span>العودة لتسجيل الدخول</span>
          </Link>
        </motion.div>

        {/* Help Text */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="p-4 bg-muted/50 rounded-xl"
        >
          <h4 className="font-medium text-foreground mb-2">لم تستلم الرسالة؟</h4>
          <ul className="text-sm text-muted-foreground space-y-1">
            <li>• تحقق من مجلد الرسائل غير المرغوب فيها</li>
            <li>• تأكد من صحة البريد الإلكتروني</li>
            <li>• انتظر بضع دقائق وحاول مرة أخرى</li>
          </ul>
        </motion.div>
      </form>
    </AuthLayout>
  )
}
