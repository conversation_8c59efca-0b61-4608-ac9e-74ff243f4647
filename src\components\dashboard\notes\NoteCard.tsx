import { motion } from "framer-motion"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ViewType } from "@/components/dashboard/shared/ViewToggle"
import { 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Eye, 
  Pin, 
  Star,
  Calendar,
  Clock,
  BookOpen,
  Tag
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import type { Note } from "@/lib/supabase"
import { formatDistanceToNow } from "date-fns"
import { ar } from "date-fns/locale"

interface NoteCardProps {
  note: Note
  index: number
  onEdit: () => void
  onDelete: () => void
  onTogglePin: () => void
  onToggleFavorite: () => void
  onView: () => void
  viewType?: ViewType
}

export function NoteCard({
  note,
  index,
  onEdit,
  onDelete,
  onTogglePin,
  onToggleFavorite,
  onView,
  viewType = 'grid'
}: NoteCardProps) {

  // Get layout classes based on view type
  const getCardClasses = () => {
    switch (viewType) {
      case 'list':
        return "flex flex-row items-center p-4 h-24"
      case 'compact':
        return "flex flex-col p-3 h-40"
      case 'masonry':
        return "flex flex-col p-4"
      default: // grid
        return "flex flex-col p-6 h-64"
    }
  }

  const formatDate = (dateString: string): string => {
    try {
      const date = new Date(dateString)
      if (isNaN(date.getTime())) return 'منذ وقت قريب'
      return formatDistanceToNow(date, {
        addSuffix: true,
        locale: ar
      })
    } catch {
      return 'منذ وقت قريب'
    }
  }

  const getReadingTimeText = (minutes: number): string => {
    if (minutes < 1) return 'أقل من دقيقة'
    if (minutes === 1) return 'دقيقة واحدة'
    if (minutes === 2) return 'دقيقتان'
    if (minutes <= 10) return `${minutes} دقائق`
    return `${minutes} دقيقة`
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      whileHover={{ y: viewType === 'list' ? 0 : -4, scale: viewType === 'compact' ? 1.05 : 1.02 }}
      whileTap={{ scale: 0.98 }}
      className="group"
    >
      <Card className={`
        relative overflow-hidden border-border/50 hover:border-primary/30
        transition-all duration-300 hover:shadow-lg bg-gradient-to-br
        from-background via-muted/10 to-accent/5 cursor-pointer h-full
        ${getCardClasses()}
      `}>
        {/* Streamlined Color Indicator */}
        {note.categoryColor && (
          <div
            className="absolute top-0 right-0 w-1 h-full opacity-60 group-hover:opacity-100 transition-opacity duration-300"
            style={{ backgroundColor: note.categoryColor }}
          />
        )}

        {/* Subtle Background Effect */}
        {note.categoryColor && (
          <div
            className="absolute inset-0 opacity-[0.01] group-hover:opacity-[0.03] transition-opacity duration-300"
            style={{
              background: `radial-gradient(circle at top right, ${note.categoryColor}, transparent 70%)`
            }}
          />
        }

        <CardContent className={`relative z-10 h-full ${viewType === 'list' ? 'flex items-center' : 'flex flex-col'}`}>
          {/* Streamlined Header */}
          <div className={`flex items-center justify-between ${viewType === 'list' ? 'flex-1' : 'mb-4'}`}>
            <div className={`flex items-center ${viewType === 'list' ? 'space-x-4 space-x-reverse' : 'space-x-3 space-x-reverse'}`}>
              {/* Note Info */}
              <div className={`${viewType === 'list' ? 'flex-1' : ''}`}>
                <div className="flex items-center space-x-2 space-x-reverse mb-1">
                  <h3 className={`font-semibold text-foreground group-hover:text-primary transition-colors duration-300 line-clamp-1 ${
                    viewType === 'compact' ? 'text-sm' : viewType === 'list' ? 'text-lg' : 'text-base'
                  }`}>
                    {note.title || 'ملاحظة بدون عنوان'}
                  </h3>

                  {/* Status Badges */}
                  <div className="flex items-center space-x-1 space-x-reverse">
                    {note.isPinned && (
                      <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
                        <Pin className="w-3 h-3" />
                      </Badge>
                    )}
                    {note.isFavorite && (
                      <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
                        <Star className="w-3 h-3" />
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Content Preview */}
                {viewType !== 'compact' && note.content && (
                  <p className={`text-muted-foreground ${viewType === 'list' ? 'text-sm' : 'text-xs'} line-clamp-2 mb-2`}>
                    {note.content.replace(/<[^>]*>/g, '').substring(0, 100)}...
                  </p>
                )}

                {/* Category Badge */}
                {note.category && viewType !== 'list' && (
                  <Badge
                    variant="outline"
                    className="text-xs px-2 py-1 mb-2"
                    style={{
                      borderColor: `${note.categoryColor}40`,
                      color: note.categoryColor,
                      backgroundColor: `${note.categoryColor}10`
                    }}
                  >
                    {note.category}
                  </Badge>
                )}
              </div>
            </div>

            {/* Streamlined Actions Menu - Only Three Dots */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="opacity-60 group-hover:opacity-100 transition-all duration-300 w-8 h-8 hover:bg-muted/50 rounded-lg hover:scale-105"
                >
                  <MoreHorizontal className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem onClick={onView} className="hover:bg-muted/50 transition-colors">
                  <Eye className="w-4 h-4 mr-2" />
                  عرض الملاحظة
                </DropdownMenuItem>
                <DropdownMenuItem onClick={onEdit} className="hover:bg-muted/50 transition-colors">
                  <Edit className="w-4 h-4 mr-2" />
                  تعديل الملاحظة
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={onTogglePin} className="hover:bg-muted/50 transition-colors">
                  <Pin className="w-4 h-4 mr-2" />
                  {note.isPinned ? 'إلغاء التثبيت' : 'تثبيت الملاحظة'}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={onToggleFavorite} className="hover:bg-muted/50 transition-colors">
                  <Star className="w-4 h-4 mr-2" />
                  {note.isFavorite ? 'إزالة من المفضلة' : 'إضافة للمفضلة'}
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={onDelete} className="hover:bg-destructive/10 text-destructive transition-colors">
                  <Trash2 className="w-4 h-4 mr-2" />
                  حذف الملاحظة
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          {/* Footer - Stats and Date */}
          {viewType !== 'compact' && viewType !== 'list' && (
            <div className="mt-auto pt-4 border-t border-border/30">
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <div className="flex items-center space-x-4 space-x-reverse">
                  <div className="flex items-center space-x-1 space-x-reverse">
                    <BookOpen className="w-3 h-3" />
                    <span>{note.wordCount || 0} كلمة</span>
                  </div>
                  <div className="flex items-center space-x-1 space-x-reverse">
                    <Clock className="w-3 h-3" />
                    <span>{getReadingTimeText(note.readingTime || 1)}</span>
                  </div>
                </div>
                <span>{formatDate(note.updatedAt || note.createdAt)}</span>
              </div>
            </div>
          )}

          {/* List View Additional Info */}
          {viewType === 'list' && (
            <div className="flex items-center space-x-4 space-x-reverse text-xs text-muted-foreground">
              <div className="flex items-center space-x-1 space-x-reverse">
                <BookOpen className="w-3 h-3" />
                <span>{note.wordCount || 0}</span>
              </div>
              {note.category && (
                <Badge
                  variant="outline"
                  className="text-xs px-2 py-1"
                  style={{
                    borderColor: `${note.categoryColor}40`,
                    color: note.categoryColor,
                    backgroundColor: `${note.categoryColor}10`
                  }}
                >
                  {note.category}
                </Badge>
              )}
              <span>{formatDate(note.updatedAt || note.createdAt)}</span>
            </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
