import { motion } from "framer-motion"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Eye, 
  Pin, 
  Star,
  Calendar,
  Clock,
  BookOpen,
  Tag
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import type { Note } from "@/lib/supabase"
import { formatDistanceToNow } from "date-fns"
import { ar } from "date-fns/locale"

interface NoteCardProps {
  note: Note
  index: number
  onEdit: () => void
  onDelete: () => void
  onTogglePin: () => void
  onToggleFavorite: () => void
  onView: () => void
}

export function NoteCard({
  note,
  index,
  onEdit,
  onDelete,
  onTogglePin,
  onToggleFavorite,
  onView
}: NoteCardProps) {
  
  const formatDate = (dateString: string): string => {
    try {
      const date = new Date(dateString)
      if (isNaN(date.getTime())) return 'منذ وقت قريب'
      return formatDistanceToNow(date, {
        addSuffix: true,
        locale: ar
      })
    } catch {
      return 'منذ وقت قريب'
    }
  }

  const getReadingTimeText = (minutes: number): string => {
    if (minutes < 1) return 'أقل من دقيقة'
    if (minutes === 1) return 'دقيقة واحدة'
    if (minutes === 2) return 'دقيقتان'
    if (minutes <= 10) return `${minutes} دقائق`
    return `${minutes} دقيقة`
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      whileHover={{ y: -6, scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      className="group"
    >
      <Card className="relative overflow-hidden border-border/50 hover:border-primary/30 transition-all duration-500 hover:shadow-2xl bg-gradient-to-br from-background via-muted/10 to-accent/5 cursor-pointer h-full">
        {/* Enhanced Color Indicator */}
        {note.categoryColor && (
          <div 
            className="absolute top-0 right-0 w-2 h-full opacity-80 group-hover:opacity-100 transition-opacity duration-300"
            style={{ 
              background: `linear-gradient(to bottom, ${note.categoryColor}, ${note.categoryColor}80)` 
            }}
          />
        )}
        
        {/* Enhanced Background Effects */}
        {note.categoryColor && (
          <div 
            className="absolute inset-0 opacity-[0.02] group-hover:opacity-[0.05] transition-opacity duration-500"
            style={{ 
              background: `radial-gradient(circle at top right, ${note.categoryColor}, transparent 60%)` 
            }}
          />
        )}
        
        {/* Multiple Animated Background Shapes */}
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 10, 0],
            opacity: [0.05, 0.1, 0.05]
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
          }}
          className="absolute -top-6 -left-6 w-20 h-20 rounded-full blur-xl group-hover:opacity-20 transition-opacity duration-300"
          style={{ backgroundColor: note.categoryColor || '#3B82F6' }}
        />

        <CardContent className="p-6 relative z-10 h-full flex flex-col">
          {/* Header - Enhanced */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-start space-x-3 space-x-reverse flex-1">
              {/* Status Indicators */}
              <div className="flex flex-col space-y-1">
                {note.isPinned && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="w-5 h-5 bg-orange-500 rounded-full flex items-center justify-center shadow-sm"
                  >
                    <Pin className="w-3 h-3 text-white" />
                  </motion.div>
                )}
                {note.isFavorite && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.1 }}
                    className="w-5 h-5 bg-yellow-500 rounded-full flex items-center justify-center shadow-sm"
                  >
                    <Star className="w-3 h-3 text-white fill-current" />
                  </motion.div>
                )}
              </div>
              
              {/* Category Badge */}
              {note.category && (
                <Badge 
                  variant="outline" 
                  className="text-xs px-2 py-1 border"
                  style={{ 
                    borderColor: `${note.categoryColor}40`,
                    color: note.categoryColor,
                    backgroundColor: `${note.categoryColor}10`
                  }}
                >
                  {note.category}
                </Badge>
              )}
            </div>

            {/* Quick Actions */}
            <div className="flex items-center space-x-2 space-x-reverse opacity-0 group-hover:opacity-100 transition-all duration-300">
              <Button
                variant="ghost"
                size="icon"
                onClick={onTogglePin}
                className={`w-8 h-8 rounded-lg hover:scale-105 transition-all duration-200 ${
                  note.isPinned 
                    ? 'bg-orange-100 dark:bg-orange-900/30 text-orange-600 hover:bg-orange-200 dark:hover:bg-orange-900/50' 
                    : 'hover:bg-muted/50'
                }`}
                title={note.isPinned ? 'إلغاء التثبيت' : 'تثبيت الملاحظة'}
              >
                <Pin className={`w-4 h-4 ${note.isPinned ? 'text-orange-600' : 'text-muted-foreground'}`} />
              </Button>
              
              <Button
                variant="ghost"
                size="icon"
                onClick={onToggleFavorite}
                className={`w-8 h-8 rounded-lg hover:scale-105 transition-all duration-200 ${
                  note.isFavorite 
                    ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 hover:bg-yellow-200 dark:hover:bg-yellow-900/50' 
                    : 'hover:bg-muted/50'
                }`}
                title={note.isFavorite ? 'إزالة من المفضلة' : 'إضافة للمفضلة'}
              >
                <Star className={`w-4 h-4 ${note.isFavorite ? 'text-yellow-600 fill-current' : 'text-muted-foreground'}`} />
              </Button>

              {/* Actions Menu */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="w-8 h-8 hover:bg-muted/50 rounded-lg hover:scale-105 transition-all duration-200"
                  >
                    <MoreHorizontal className="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-52 bg-background/95 backdrop-blur-md border border-border/50 shadow-xl">
                  <DropdownMenuItem onClick={onView} className="hover:bg-muted/50 transition-colors">
                    <Eye className="w-4 h-4 mr-2" />
                    عرض الملاحظة
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={onEdit} className="hover:bg-muted/50 transition-colors">
                    <Edit className="w-4 h-4 mr-2" />
                    تعديل الملاحظة
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={onTogglePin} className="hover:bg-muted/50 transition-colors">
                    <Pin className="w-4 h-4 mr-2" />
                    {note.isPinned ? 'إلغاء التثبيت' : 'تثبيت الملاحظة'}
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={onToggleFavorite} className="hover:bg-muted/50 transition-colors">
                    <Star className="w-4 h-4 mr-2" />
                    {note.isFavorite ? 'إزالة من المفضلة' : 'إضافة للمفضلة'}
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    onClick={onDelete}
                    className="text-red-600 focus:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    حذف الملاحظة
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Content - Enhanced */}
          <div className="flex-1 space-y-4" onClick={onView}>
            <div>
              <h3 className="text-xl font-bold text-foreground mb-3 group-hover:text-primary transition-colors duration-300 line-clamp-2 leading-tight">
                {note.title}
              </h3>
              
              <p className="text-sm text-muted-foreground line-clamp-3 leading-relaxed">
                {note.content || 'لا يوجد محتوى لهذه الملاحظة...'}
              </p>
            </div>
            
            {/* Tags */}
            {note.tags && note.tags.length > 0 && (
              <div className="flex items-center space-x-2 space-x-reverse flex-wrap gap-1">
                <Tag className="w-3 h-3 text-muted-foreground" />
                {note.tags.slice(0, 3).map((tag, index) => (
                  <Badge key={index} variant="secondary" className="text-xs px-2 py-0.5">
                    {tag}
                  </Badge>
                ))}
                {note.tags.length > 3 && (
                  <Badge variant="secondary" className="text-xs px-2 py-0.5">
                    +{note.tags.length - 3}
                  </Badge>
                )}
              </div>
            )}
          </div>

          {/* Footer - Enhanced */}
          <div className="space-y-3 pt-4 border-t border-border/30 mt-4">
            {/* Stats Row */}
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <div className="flex items-center space-x-3 space-x-reverse">
                <div className="flex items-center space-x-1 space-x-reverse">
                  <BookOpen className="w-3 h-3" />
                  <span>{note.wordCount} كلمة</span>
                </div>
                <div className="flex items-center space-x-1 space-x-reverse">
                  <Clock className="w-3 h-3" />
                  <span>{getReadingTimeText(note.readingTime)}</span>
                </div>
              </div>
            </div>

            {/* Last Edited */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-1 space-x-reverse text-xs text-muted-foreground">
                <Calendar className="w-3 h-3" />
                <span>آخر تعديل {formatDate(note.lastEdited)}</span>
              </div>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={onEdit}
                className="opacity-0 group-hover:opacity-100 transition-opacity h-7 px-2 text-xs hover:bg-muted/50"
              >
                تعديل
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
