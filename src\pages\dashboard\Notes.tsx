import { useState, useEffect } from "react"
import { useNavigate } from "react-router-dom"
import { motion } from "framer-motion"
import { ThemeProvider } from "@/components/theme/ThemeProvider"
import { DashboardLayout } from "@/components/dashboard/DashboardLayout"
import { NotesHeader } from "@/components/dashboard/notes/NotesHeader"
import { NotesFilters, type FilterType, type SortType } from "@/components/dashboard/notes/NotesFilters"
import { NotesGrid } from "@/components/dashboard/notes/NotesGrid"
import { EmptyState } from "@/components/dashboard/notes/EmptyState"
import { NoteModal } from "@/components/dashboard/notes/NoteModal"
import { useAuthStore } from "@/stores/authStore"
import type { Note } from "@/lib/supabase"
import { toast } from "sonner"

// Sample Arabic notes data
const sampleNotes: Note[] = [
  {
    id: "1",
    user_id: "sample",
    title: "أفكار المشروع الجديد",
    content: "مشروع تطبيق إدارة الملاحظات الذكي باللغة العربية. الهدف هو إنشاء تطبيق يدعم الكتابة من اليمين إلى اليسار ويوفر تجربة مستخدم ممتازة للمستخدمين العرب. المميزات المطلوبة: البحث الذكي، التصنيف التلقائي، المزامنة السحابية، والدعم الكامل للغة العربية.",
    category: "العمل",
    categoryColor: "#3B82F6",
    isPinned: true,
    isFavorite: false,
    lastEdited: "2024-01-15T10:30:00Z",
    wordCount: 45,
    readingTime: 1,
    tags: ["مشروع", "تطوير", "عربي"],
    created_at: "2024-01-15T10:30:00Z",
    updated_at: "2024-01-15T10:30:00Z"
  },
  {
    id: "2",
    user_id: "sample",
    title: "قائمة المهام اليومية",
    content: "المهام المطلوب إنجازها اليوم:\n- مراجعة التصميم الجديد\n- كتابة التوثيق\n- اجتماع الفريق الساعة 3\n- إرسال التقرير الأسبوعي\n- التحضير لعرض الغد",
    category: "شخصي",
    categoryColor: "#10B981",
    isPinned: false,
    isFavorite: true,
    lastEdited: "2024-01-14T14:20:00Z",
    wordCount: 25,
    readingTime: 1,
    tags: ["مهام", "يومي"],
    created_at: "2024-01-14T14:20:00Z",
    updated_at: "2024-01-14T14:20:00Z"
  },
  {
    id: "3",
    user_id: "sample",
    title: "ملاحظات الاجتماع",
    content: "اجتماع فريق التطوير - 12 يناير 2024\n\nالنقاط المهمة:\n- تم الانتهاء من المرحلة الأولى\n- البدء في المرحلة الثانية الأسبوع القادم\n- تحديث التصميم حسب ملاحظات العملاء\n- إضافة ميزة البحث المتقدم\n\nالمهام الجديدة:\n- تحسين الأداء\n- إضافة الدعم للغات متعددة",
    category: "العمل",
    categoryColor: "#3B82F6",
    isPinned: false,
    isFavorite: false,
    lastEdited: "2024-01-12T16:45:00Z",
    wordCount: 52,
    readingTime: 1,
    tags: ["اجتماع", "فريق", "تطوير"],
    created_at: "2024-01-12T16:45:00Z",
    updated_at: "2024-01-12T16:45:00Z"
  },
  {
    id: "4",
    user_id: "sample",
    title: "وصفات الطبخ المفضلة",
    content: "كبسة الدجاج:\nالمقادير: دجاج، أرز بسمتي، بصل، طماطم، بهارات كبسة، ملح، زيت\n\nالطريقة:\n1. تتبيل الدجاج بالبهارات\n2. قلي الدجاج حتى ينضج\n3. إضافة البصل والطماطم\n4. إضافة الأرز والماء\n5. الطبخ على نار هادئة لمدة 30 دقيقة",
    category: "شخصي",
    categoryColor: "#10B981",
    isPinned: false,
    isFavorite: true,
    lastEdited: "2024-01-10T19:30:00Z",
    wordCount: 38,
    readingTime: 1,
    tags: ["طبخ", "وصفات", "كبسة"],
    created_at: "2024-01-10T19:30:00Z",
    updated_at: "2024-01-10T19:30:00Z"
  },
  {
    id: "5",
    user_id: "sample",
    title: "خطة السفر الصيفية",
    content: "رحلة إلى تركيا - صيف 2024\n\nالمدن المخططة:\n- إسطنبول (3 أيام)\n- كابادوكيا (2 أيام)\n- أنطاليا (4 أيام)\n\nالأنشطة:\n- زيارة آيا صوفيا\n- رحلة البالون الهوائي\n- الاستمتاع بالشواطئ\n\nالميزانية المتوقعة: 8000 ريال",
    category: "السفر",
    categoryColor: "#F59E0B",
    isPinned: true,
    isFavorite: true,
    lastEdited: "2024-01-08T11:15:00Z",
    wordCount: 42,
    readingTime: 1,
    tags: ["سفر", "تركيا", "صيف"],
    created_at: "2024-01-08T11:15:00Z",
    updated_at: "2024-01-08T11:15:00Z"
  },
  {
    id: "6",
    user_id: "sample",
    title: "أفكار للمحتوى الرقمي",
    content: "أفكار لمحتوى وسائل التواصل الاجتماعي:\n\n1. نصائح البرمجة اليومية\n2. مراجعات الأدوات التقنية\n3. قصص نجاح المطورين العرب\n4. دروس تعليمية قصيرة\n5. مقارنات بين التقنيات\n\nالهدف: نشر محتوى مفيد وتعليمي للمجتمع التقني العربي",
    category: "الأفكار",
    categoryColor: "#8B5CF6",
    isPinned: false,
    isFavorite: false,
    lastEdited: "2024-01-07T13:22:00Z",
    wordCount: 35,
    readingTime: 1,
    tags: ["محتوى", "تقنية", "تعليم"],
    created_at: "2024-01-07T13:22:00Z",
    updated_at: "2024-01-07T13:22:00Z"
  },
  {
    id: "7",
    user_id: "sample",
    title: "ملاحظات الدورة التدريبية",
    content: "دورة تطوير تطبيقات الويب المتقدمة\n\nالموضوعات المغطاة:\n- React و TypeScript\n- إدارة الحالة مع Zustand\n- تصميم واجهات المستخدم\n- التحسين والأداء\n- النشر والاستضافة\n\nالمشروع النهائي: تطبيق إدارة المهام مع ميزات متقدمة",
    category: "الدراسة",
    categoryColor: "#EC4899",
    isPinned: false,
    isFavorite: false,
    lastEdited: "2024-01-05T09:45:00Z",
    wordCount: 33,
    readingTime: 1,
    tags: ["دورة", "تطوير", "ويب"],
    created_at: "2024-01-05T09:45:00Z",
    updated_at: "2024-01-05T09:45:00Z"
  },
  {
    id: "8",
    user_id: "sample",
    title: "قائمة الكتب للقراءة",
    content: "الكتب المخططة للقراءة هذا العام:\n\n📚 التقنية:\n- Clean Code\n- Design Patterns\n- System Design Interview\n\n📖 التطوير الشخصي:\n- العادات الذرية\n- فكر تصبح غنياً\n- قوة التفكير الإيجابي\n\n🎯 الهدف: قراءة كتاب واحد شهرياً",
    category: "شخصي",
    categoryColor: "#10B981",
    isPinned: false,
    isFavorite: true,
    lastEdited: "2024-01-03T20:10:00Z",
    wordCount: 41,
    readingTime: 1,
    tags: ["كتب", "قراءة", "تعلم"],
    created_at: "2024-01-03T20:10:00Z",
    updated_at: "2024-01-03T20:10:00Z"
  }
]

const Notes = () => {
  const navigate = useNavigate()
  const { isAuthenticated, user, initializeAuth } = useAuthStore()
  
  // State management
  const [notes, setNotes] = useState<Note[]>(sampleNotes)
  const [filteredNotes, setFilteredNotes] = useState<Note[]>(sampleNotes)
  const [isLoading, setIsLoading] = useState(true)
  const [isAuthLoading, setIsAuthLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [filterType, setFilterType] = useState<FilterType>('all')
  const [sortType, setSortType] = useState<SortType>('newest')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingNote, setEditingNote] = useState<Note | null>(null)
  const [viewingNote, setViewingNote] = useState<Note | null>(null)

  // Initialize auth and check authentication status
  useEffect(() => {
    const checkAuth = async () => {
      try {
        await initializeAuth()
      } catch (error) {
        console.error('Auth initialization failed:', error)
      } finally {
        setIsAuthLoading(false)
      }
    }
    
    checkAuth()
  }, [initializeAuth])

  // Handle authentication redirect with proper loading state
  useEffect(() => {
    if (!isAuthLoading && !isAuthenticated) {
      console.log('User not authenticated, redirecting to login')
      navigate('/auth/login')
    }
  }, [isAuthenticated, isAuthLoading, navigate])

  // Load notes (simulate loading)
  useEffect(() => {
    if (user?.id && !isAuthLoading) {
      console.log('Loading notes for user:', user.id)
      setTimeout(() => {
        setIsLoading(false)
      }, 1000)
    }
  }, [user?.id, isAuthLoading])

  // Filter and sort notes
  useEffect(() => {
    let filtered = [...notes]

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(note =>
        note.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        note.content?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        note.tags?.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    }

    // Apply type filter
    switch (filterType) {
      case 'pinned':
        filtered = filtered.filter(note => note.isPinned)
        break
      case 'favorite':
        filtered = filtered.filter(note => note.isFavorite)
        break
      case 'recent':
        const threeDaysAgo = new Date()
        threeDaysAgo.setDate(threeDaysAgo.getDate() - 3)
        filtered = filtered.filter(note => new Date(note.lastEdited) > threeDaysAgo)
        break
    }

    // Apply sorting
    switch (sortType) {
      case 'newest':
        filtered.sort((a, b) => new Date(b.lastEdited).getTime() - new Date(a.lastEdited).getTime())
        break
      case 'oldest':
        filtered.sort((a, b) => new Date(a.lastEdited).getTime() - new Date(b.lastEdited).getTime())
        break
      case 'most_edited':
        filtered.sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())
        break
      case 'alphabetical':
        filtered.sort((a, b) => a.title.localeCompare(b.title, 'ar'))
        break
    }

    setFilteredNotes(filtered)
  }, [notes, searchQuery, filterType, sortType])

  const handleCreateNote = () => {
    // Navigate to editor for new note
    navigate('/dashboard/notes/new')
  }

  const handleEditNote = (note: Note) => {
    // Navigate to editor for existing note
    navigate(`/dashboard/notes/${note.id}`)
  }

  const handleViewNote = (note: Note) => {
    setViewingNote(note)
    setEditingNote(null)
    setIsModalOpen(true)
  }

  const handleDeleteNote = (noteId: string) => {
    setNotes(prev => prev.filter(note => note.id !== noteId))
    toast.success('تم حذف الملاحظة بنجاح')
  }

  const handleTogglePin = (noteId: string, isPinned: boolean) => {
    setNotes(prev => prev.map(note => 
      note.id === noteId ? { ...note, isPinned: !isPinned } : note
    ))
    toast.success(isPinned ? 'تم إلغاء تثبيت الملاحظة' : 'تم تثبيت الملاحظة')
  }

  const handleToggleFavorite = (noteId: string, isFavorite: boolean) => {
    setNotes(prev => prev.map(note => 
      note.id === noteId ? { ...note, isFavorite: !isFavorite } : note
    ))
    toast.success(isFavorite ? 'تم إزالة من المفضلة' : 'تم إضافة للمفضلة')
  }

  const handleModalSave = (noteData: Partial<Note>) => {
    if (editingNote) {
      // Update existing note
      setNotes(prev => prev.map(note => 
        note.id === editingNote.id ? { ...note, ...noteData, updated_at: new Date().toISOString() } : note
      ))
      toast.success('تم تحديث الملاحظة بنجاح')
    } else {
      // Create new note
      const newNote: Note = {
        id: Date.now().toString(),
        user_id: user?.id || 'sample',
        title: noteData.title || '',
        content: noteData.content || '',
        category: noteData.category,
        categoryColor: getCategoryColor(noteData.category),
        isPinned: false,
        isFavorite: false,
        lastEdited: new Date().toISOString(),
        wordCount: noteData.wordCount || 0,
        readingTime: noteData.readingTime || 1,
        tags: noteData.tags,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
      setNotes(prev => [newNote, ...prev])
      toast.success('تم إنشاء الملاحظة بنجاح')
    }
  }

  const getCategoryColor = (category?: string) => {
    const colors: { [key: string]: string } = {
      'العمل': '#3B82F6',
      'شخصي': '#10B981',
      'الدراسة': '#EC4899',
      'الأفكار': '#8B5CF6',
      'السفر': '#F59E0B'
    }
    return colors[category || ''] || '#6B7280'
  }

  // Show loading while checking authentication
  if (isAuthLoading) {
    return (
      <ThemeProvider defaultTheme="system" storageKey="nots-notes-theme">
        <DashboardLayout>
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center space-y-4">
              <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto" />
              <p className="text-muted-foreground">جاري التحقق من المصادقة...</p>
            </div>
          </div>
        </DashboardLayout>
      </ThemeProvider>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  return (
    <ThemeProvider defaultTheme="system" storageKey="nots-notes-theme">
      <DashboardLayout>
        <div className="space-y-6">
          {/* Header */}
          <NotesHeader />

          {/* Filters and Actions */}
          <NotesFilters
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
            filterType={filterType}
            onFilterChange={setFilterType}
            sortType={sortType}
            onSortChange={setSortType}
            onCreateNote={handleCreateNote}
          />

          {/* Notes Grid */}
          <div className="min-h-[400px]">
            {filteredNotes.length > 0 ? (
              <NotesGrid
                notes={filteredNotes}
                isLoading={isLoading}
                onEditNote={handleEditNote}
                onDeleteNote={handleDeleteNote}
                onTogglePin={handleTogglePin}
                onToggleFavorite={handleToggleFavorite}
                onViewNote={handleViewNote}
              />
            ) : (
              <EmptyState
                hasNotes={notes.length > 0}
                searchQuery={searchQuery}
                onCreateNote={handleCreateNote}
              />
            )}
          </div>
        </div>

        {/* Note Modal */}
        <NoteModal
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false)
            setEditingNote(null)
            setViewingNote(null)
          }}
          onSave={handleModalSave}
          note={editingNote || viewingNote}
          isViewing={!!viewingNote}
        />
      </DashboardLayout>
    </ThemeProvider>
  )
}

export default Notes
