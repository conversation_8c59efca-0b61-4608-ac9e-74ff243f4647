import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { 
  Lightbulb,
  ChevronDown,
  ChevronUp,
  Clock,
  Calendar,
  ArrowRight,
  Sparkles
} from 'lucide-react'

export interface SchedulingSuggestionsProps {
  onSuggestionApply?: (suggestion: any) => void
}

export function SchedulingSuggestions({ onSuggestionApply }: SchedulingSuggestionsProps) {
  const [isExpanded, setIsExpanded] = useState(true)

  // Mock suggestions
  const mockSuggestions = [
    {
      id: '1',
      title: 'أفضل وقت للاجتماعات',
      description: 'يبدو أن يوم الخميس مناسب للمهام الجديدة، حيث لديك وقت فراغ بين الساعة 11 صباحاً و 2 مساءً.',
      icon: Calendar,
      color: '#6366f1'
    },
    {
      id: '2',
      title: 'تحسين الإنتاجية',
      description: 'أفضل أوقاتك للتركيز هي بين 9-11 صباحاً، جدولة المهام المهمة خلال هذه الفترة.',
      icon: Clock,
      color: '#22c55e'
    }
  ]

  const handleApply = (suggestion: any) => {
    onSuggestionApply?.(suggestion)
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.5 }}
      className="bg-background rounded-xl border border-border/50 shadow-sm p-4"
    >
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="flex items-center justify-between w-full mb-3"
      >
        <div className="flex items-center gap-2">
          <Lightbulb className="w-5 h-5 text-amber-500" />
          <h3 className="font-semibold">اقتراحات ذكية</h3>
        </div>
        {isExpanded ? (
          <ChevronUp className="w-4 h-4 text-muted-foreground" />
        ) : (
          <ChevronDown className="w-4 h-4 text-muted-foreground" />
        )}
      </button>

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
            className="space-y-3"
          >
            {mockSuggestions.map((suggestion, index) => {
              const Icon = suggestion.icon
              
              return (
                <motion.div
                  key={suggestion.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.2, delay: index * 0.1 }}
                  className="p-3 rounded-lg border border-border/30 bg-muted/10 relative overflow-hidden"
                >
                  {/* Background gradient */}
                  <div 
                    className="absolute inset-0 opacity-5" 
                    style={{ 
                      background: `linear-gradient(to right, ${suggestion.color}22, transparent)` 
                    }} 
                  />
                  
                  <div className="relative">
                    {/* Header */}
                    <div className="flex items-center gap-2 mb-2">
                      <div 
                        className="w-8 h-8 rounded-full flex items-center justify-center"
                        style={{ backgroundColor: `${suggestion.color}22` }}
                      >
                        <Icon className="w-4 h-4" style={{ color: suggestion.color }} />
                      </div>
                      <span className="font-medium text-sm">
                        {suggestion.title}
                      </span>
                    </div>
                    
                    {/* Description */}
                    <div className="text-xs text-muted-foreground mb-3 flex items-start gap-2">
                      <Sparkles className="w-3 h-3 text-amber-500 mt-0.5 flex-shrink-0" />
                      <p>{suggestion.description}</p>
                    </div>
                    
                    {/* Action */}
                    <div className="flex justify-end">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleApply(suggestion)}
                        className="text-xs text-primary hover:text-primary/80 hover:bg-primary/10 transition-colors"
                      >
                        تطبيق
                        <ArrowRight className="w-3 h-3 mr-1" />
                      </Button>
                    </div>
                  </div>
                </motion.div>
              )
            })}

            {/* Empty state */}
            {mockSuggestions.length === 0 && (
              <div className="text-center py-6">
                <Lightbulb className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">لا توجد اقتراحات حالياً</p>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}
