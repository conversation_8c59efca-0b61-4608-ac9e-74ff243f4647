import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Lightbulb,
  ChevronDown,
  ChevronUp,
  Clock,
  Calendar,
  ArrowRight,
  Sparkles
} from 'lucide-react'

import { CalendarEvent } from './CalendarView'

export interface SchedulingSuggestionsProps {
  events?: CalendarEvent[]
  onSuggestionApply?: (suggestion: any) => void
}

export function SchedulingSuggestions({ events = [], onSuggestionApply }: SchedulingSuggestionsProps) {
  const [isExpanded, setIsExpanded] = useState(true)
  const [isLoading, setIsLoading] = useState(false)
  const [suggestions, setSuggestions] = useState<any[]>([])

  // Generate AI suggestions based on events
  useEffect(() => {
    if (events.length === 0) return

    setIsLoading(true)

    // Simulate AI processing delay
    setTimeout(() => {
      const generatedSuggestions = generateSmartSuggestions(events)
      setSuggestions(generatedSuggestions)
      setIsLoading(false)
    }, 1500)
  }, [events])

  // AI suggestion generation logic
  const generateSmartSuggestions = (events: CalendarEvent[]) => {
    // Get current date and time
    const now = new Date()
    const currentHour = now.getHours()
    const currentDay = now.getDay()

    // Find free time slots
    const busyHours = new Set<string>()
    events.forEach(event => {
      const start = new Date(event.start)
      const end = new Date(event.end)

      for (let time = start; time < end; time.setHours(time.getHours() + 1)) {
        const day = time.getDay()
        const hour = time.getHours()
        busyHours.add(`${day}-${hour}`)
      }
    })

    // Find optimal meeting times
    const freeTimeSlots: {day: number; hour: number}[] = []
    for (let day = 0; day < 7; day++) {
      for (let hour = 9; hour < 17; hour++) { // 9 AM to 5 PM
        if (!busyHours.has(`${day}-${hour}`)) {
          freeTimeSlots.push({day, hour})
        }
      }
    }

    // Find workload patterns
    const workloadByDay = Array(7).fill(0)
    events.forEach(event => {
      const day = new Date(event.start).getDay()
      workloadByDay[day]++
    })

    // Find busiest and least busy days
    const maxWorkload = Math.max(...workloadByDay)
    const minWorkload = Math.min(...workloadByDay)
    const busiestDayIndex = workloadByDay.indexOf(maxWorkload)
    const leastBusyDayIndex = workloadByDay.indexOf(minWorkload)

    const dayNames = ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت']
    const busiestDay = dayNames[busiestDayIndex]
    const leastBusyDay = dayNames[leastBusyDayIndex]

    // Generate suggestions
    const suggestions = []

    // Optimal meeting time suggestion
    if (freeTimeSlots.length > 0) {
      const optimalSlot = freeTimeSlots[0]
      suggestions.push({
        id: '1',
        title: 'أفضل وقت للاجتماعات',
        description: `يبدو أن يوم ${dayNames[optimalSlot.day]} مناسب للمهام الجديدة، حيث لديك وقت فراغ في الساعة ${optimalSlot.hour}:00.`,
        icon: Calendar,
        color: '#6366f1',
        action: {
          type: 'schedule',
          day: optimalSlot.day,
          hour: optimalSlot.hour
        }
      })
    }

    // Workload balance suggestion
    suggestions.push({
      id: '2',
      title: 'توازن المهام',
      description: `يوم ${busiestDay} هو أكثر أيامك انشغالاً. حاول نقل بعض المهام إلى يوم ${leastBusyDay} لتحقيق توازن أفضل.`,
      icon: Clock,
      color: '#22c55e',
      action: {
        type: 'balance',
        fromDay: busiestDayIndex,
        toDay: leastBusyDayIndex
      }
    })

    // Focus time suggestion
    const focusHours = currentHour < 12 ? '9-11 صباحاً' : '2-4 مساءً'
    suggestions.push({
      id: '3',
      title: 'وقت التركيز',
      description: `أفضل أوقاتك للتركيز هي بين ${focusHours}، جدولة المهام المهمة خلال هذه الفترة.`,
      icon: Clock,
      color: '#3b82f6',
      action: {
        type: 'focus',
        hours: focusHours
      }
    })

    return suggestions
  }

  const handleApply = (suggestion: any) => {
    onSuggestionApply?.(suggestion)
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.5 }}
      className="bg-background rounded-xl border border-border/50 shadow-sm p-4"
    >
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="flex items-center justify-between w-full mb-3"
      >
        <div className="flex items-center gap-2">
          <Lightbulb className="w-5 h-5 text-amber-500" />
          <h3 className="font-semibold">اقتراحات ذكية</h3>
        </div>
        {isExpanded ? (
          <ChevronUp className="w-4 h-4 text-muted-foreground" />
        ) : (
          <ChevronDown className="w-4 h-4 text-muted-foreground" />
        )}
      </button>

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
            className="space-y-3"
          >
            {/* Loading State */}
            {isLoading && (
              <div className="text-center py-6">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                  className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full mx-auto mb-2"
                />
                <p className="text-sm text-muted-foreground">جاري تحليل البيانات...</p>
              </div>
            )}

            {/* Suggestions */}
            {!isLoading && suggestions.map((suggestion, index) => {
              const Icon = suggestion.icon

              return (
                <motion.div
                  key={suggestion.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.2, delay: index * 0.1 }}
                  className="p-3 rounded-lg border border-border/30 bg-muted/10 relative overflow-hidden"
                >
                  {/* Background gradient */}
                  <div
                    className="absolute inset-0 opacity-5"
                    style={{
                      background: `linear-gradient(to right, ${suggestion.color}22, transparent)`
                    }}
                  />

                  <div className="relative">
                    {/* Header */}
                    <div className="flex items-center gap-2 mb-2">
                      <div
                        className="w-8 h-8 rounded-full flex items-center justify-center"
                        style={{ backgroundColor: `${suggestion.color}22` }}
                      >
                        <Icon className="w-4 h-4" style={{ color: suggestion.color }} />
                      </div>
                      <span className="font-medium text-sm">
                        {suggestion.title}
                      </span>
                      <Badge variant="secondary" className="text-xs ml-auto">
                        AI
                      </Badge>
                    </div>

                    {/* Description */}
                    <div className="text-xs text-muted-foreground mb-3 flex items-start gap-2">
                      <Sparkles className="w-3 h-3 text-amber-500 mt-0.5 flex-shrink-0" />
                      <p>{suggestion.description}</p>
                    </div>

                    {/* Action */}
                    <div className="flex justify-end">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleApply(suggestion)}
                        className="text-xs text-primary hover:text-primary/80 hover:bg-primary/10 transition-colors"
                      >
                        تطبيق
                        <ArrowRight className="w-3 h-3 mr-1" />
                      </Button>
                    </div>
                  </div>
                </motion.div>
              )
            })}

            {/* Empty state */}
            {!isLoading && suggestions.length === 0 && (
              <div className="text-center py-6">
                <Lightbulb className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">لا توجد اقتراحات حالياً</p>
                <p className="text-xs text-muted-foreground mt-1">أضف المزيد من الأحداث للحصول على اقتراحات ذكية</p>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}
