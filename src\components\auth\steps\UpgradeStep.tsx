import { useState } from "react"
import { motion } from "framer-motion"
import { Crown, Zap, Check, Star } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useAuthStore } from "@/stores/authStore"
import { toast } from "sonner"

interface UpgradeStepProps {
  onNext: () => void
  onBack: () => void
}

const planOptions = [
  {
    id: 'free' as const,
    title: 'الخطة المجانية',
    description: 'ابدأ رحلتك مع Nots مجاناً',
    price: 'مجاناً',
    icon: Zap,
    color: 'from-blue-500 to-blue-600',
    features: [
      'حتى 100 ملاحظة',
      'ميزات ذكية أساسية',
      'مزامنة عبر جهازين',
      'دعم فني أساسي'
    ]
  },
  {
    id: 'upgrade' as const,
    title: 'Nots Premium',
    description: 'احصل على أقصى استفادة من الذكاء الاصطناعي',
    price: '29 ريال/شهر',
    originalPrice: '49 ريال/شهر',
    icon: Crown,
    color: 'from-purple-500 to-purple-600',
    popular: true,
    features: [
      'ملاحظات غير محدودة',
      'جميع الميزات الذكية',
      'مزامنة عبر أجهزة غير محدودة',
      'تصدير متقدم (PDF, Word)',
      'دعم فني أولوية',
      'نسخ احتياطية تلقائية'
    ]
  }
]

export function UpgradeStep({ onNext, onBack }: UpgradeStepProps) {
  const { signUpData, updateSignUpData } = useAuthStore()
  const [selectedPlan, setSelectedPlan] = useState(signUpData.planChoice || 'free')

  const handleNext = () => {
    updateSignUpData({ planChoice: selectedPlan })
    
    if (selectedPlan === 'upgrade') {
      toast.success("✨ تم اختيار خطة Premium بنجاح!", {
        description: "ستحصل على جميع الميزات المتقدمة"
      })
    } else {
      toast.success("✨ تم اختيار الخطة المجانية بنجاح!", {
        description: "يمكنك الترقية في أي وقت لاحقاً"
      })
    }
    
    onNext()
  }

  const handleSkip = () => {
    updateSignUpData({ planChoice: 'free' })
    toast.info("تم اختيار الخطة المجانية", {
      description: "يمكنك الترقية لاحقاً من إعدادات الحساب"
    })
    onNext()
  }

  return (
    <div className="space-y-6">
      {/* Plans */}
      <div className="space-y-4">
        {planOptions.map((plan, index) => {
          const Icon = plan.icon
          const isSelected = selectedPlan === plan.id
          
          return (
            <motion.button
              key={plan.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.2 }}
              onClick={() => setSelectedPlan(plan.id)}
              className={`
                relative w-full p-6 rounded-xl border-2 text-right transition-all duration-300
                ${isSelected 
                  ? 'border-primary bg-primary/10 shadow-lg' 
                  : 'border-border hover:border-primary/50 hover:bg-muted/50'
                }
              `}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              {/* Popular Badge */}
              {plan.popular && (
                <div className="absolute -top-3 right-6">
                  <div className="bg-gradient-to-r from-yellow-400 to-yellow-500 text-white px-3 py-1 rounded-full text-xs font-medium flex items-center space-x-1 space-x-reverse">
                    <Star className="w-3 h-3" />
                    <span>الأكثر شعبية</span>
                  </div>
                </div>
              )}

              <div className="flex items-start space-x-4 space-x-reverse">
                <div className={`
                  w-12 h-12 rounded-lg bg-gradient-to-r ${plan.color} 
                  flex items-center justify-center text-white shadow-lg flex-shrink-0
                `}>
                  <Icon className="w-6 h-6" />
                </div>
                
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className={`
                      text-xl font-bold transition-colors
                      ${isSelected ? 'text-primary' : 'text-foreground'}
                    `}>
                      {plan.title}
                    </h3>
                    
                    <div className="text-left">
                      <div className={`
                        text-2xl font-bold transition-colors
                        ${isSelected ? 'text-primary' : 'text-foreground'}
                      `}>
                        {plan.price}
                      </div>
                      {plan.originalPrice && (
                        <div className="text-sm text-muted-foreground line-through">
                          {plan.originalPrice}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <p className="text-muted-foreground mb-4">
                    {plan.description}
                  </p>
                  
                  <div className="space-y-2">
                    {plan.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center space-x-2 space-x-reverse text-sm">
                        <Check className={`
                          w-4 h-4 flex-shrink-0
                          ${isSelected ? 'text-primary' : 'text-green-500'}
                        `} />
                        <span className="text-foreground">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>
                
                {/* Selection Indicator */}
                <div className={`
                  w-5 h-5 rounded-full border-2 transition-all duration-200 flex-shrink-0
                  ${isSelected 
                    ? 'border-primary bg-primary' 
                    : 'border-muted-foreground'
                  }
                `}>
                  {isSelected && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="w-full h-full rounded-full bg-primary flex items-center justify-center"
                    >
                      <div className="w-2 h-2 rounded-full bg-white" />
                    </motion.div>
                  )}
                </div>
              </div>
            </motion.button>
          )
        })}
      </div>

      {/* Special Offer */}
      {selectedPlan === 'upgrade' && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
          className="p-4 bg-gradient-to-r from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 border border-yellow-200 dark:border-yellow-800 rounded-xl"
        >
          <div className="flex items-center space-x-2 space-x-reverse text-yellow-800 dark:text-yellow-200">
            <Star className="w-5 h-5" />
            <span className="font-medium">عرض خاص للمستخدمين الجدد!</span>
          </div>
          <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
            احصل على خصم 40% لأول 3 أشهر + نسخة تجريبية مجانية لمدة 7 أيام
          </p>
        </motion.div>
      )}

      {/* Action Buttons */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.6 }}
        className="flex flex-col space-y-3"
      >
        <Button
          onClick={handleNext}
          className="w-full bg-gradient-to-r from-primary to-secondary hover:opacity-90 text-primary-foreground py-3 text-lg font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
        >
          {selectedPlan === 'upgrade' ? 'متابعة مع Premium' : 'متابعة مع الخطة المجانية'}
        </Button>
        
        <div className="flex space-x-3 space-x-reverse">
          <Button
            variant="outline"
            onClick={onBack}
            className="flex-1 border-2 border-border text-foreground hover:bg-muted py-3 rounded-xl transition-all duration-300"
          >
            رجوع
          </Button>
          
          <Button
            variant="ghost"
            onClick={handleSkip}
            className="flex-1 text-muted-foreground hover:text-foreground hover:bg-muted/50 py-3 rounded-xl transition-all duration-300"
          >
            تخطي
          </Button>
        </div>
      </motion.div>

      {/* Money Back Guarantee */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.8 }}
        className="text-center"
      >
        <p className="text-sm text-muted-foreground">
          ضمان استرداد المال خلال 30 يوماً • إلغاء في أي وقت
        </p>
      </motion.div>
    </div>
  )
}
