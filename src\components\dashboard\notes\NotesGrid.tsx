import { motion } from "framer-motion"
import { NoteCard } from "./NoteCard"
import type { Note } from "@/lib/supabase"

interface NotesGridProps {
  notes: Note[]
  isLoading: boolean
  onEditNote: (note: Note) => void
  onDeleteNote: (noteId: string) => void
  onTogglePin: (noteId: string, isPinned: boolean) => void
  onToggleFavorite: (noteId: string, isFavorite: boolean) => void
  onViewNote: (note: Note) => void
}

export function NotesGrid({
  notes,
  isLoading,
  onEditNote,
  onDeleteNote,
  onTogglePin,
  onToggleFavorite,
  onViewNote
}: NotesGridProps) {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {Array.from({ length: 8 }).map((_, index) => (
          <div
            key={index}
            className="h-64 bg-muted/30 rounded-xl animate-pulse"
          />
        ))}
      </div>
    )
  }

  if (notes.length === 0) {
    return null // EmptyState will be handled by parent component
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
    >
      {notes.map((note, index) => (
        <NoteCard
          key={note.id}
          note={note}
          index={index}
          onEdit={() => onEditNote(note)}
          onDelete={() => onDeleteNote(note.id)}
          onTogglePin={() => onTogglePin(note.id, note.isPinned)}
          onToggleFavorite={() => onToggleFavorite(note.id, note.isFavorite)}
          onView={() => onViewNote(note)}
        />
      ))}
    </motion.div>
  )
}
