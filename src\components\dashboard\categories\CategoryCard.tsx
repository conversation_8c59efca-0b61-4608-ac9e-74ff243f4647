import { motion } from "framer-motion"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ViewType } from "@/components/dashboard/shared/ViewToggle"
import {
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Pin,
  Star,
  Calendar,
  FileText,
  Folder,
  Briefcase,
  Book,
  Brain,
  Target,
  BarChart3,
  Home,
  Heart,
  Lightbulb,
  Camera,
  Music,
  Plane,
  ShoppingCart,
  Coffee,
  Gamepad2,
  Dumbbell,
  Car,
  Smartphone,
  Laptop,
  Palette,
  Clock,
  Edit3
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import type { Category } from "@/lib/supabase"
import { formatDistanceToNow } from "date-fns"
import { ar } from "date-fns/locale"

// Icon mapping for categories
const iconMap: { [key: string]: React.ElementType } = {
  'folder': Folder,
  'briefcase': Briefcase,
  'book': Book,
  'brain': Brain,
  'edit': Edit3,
  'target': Target,
  'bar-chart': BarChart3,
  'home': Home,
  'heart': Heart,
  'star': Star,
  'lightbulb': Lightbulb,
  'camera': Camera,
  'music': Music,
  'plane': Plane,
  'shopping-cart': ShoppingCart,
  'coffee': Coffee,
  'gamepad': Gamepad2,
  'dumbbell': Dumbbell,
  'car': Car,
  'smartphone': Smartphone,
  'laptop': Laptop,
  'palette': Palette,
  'clock': Clock,
  'calendar': Calendar,
}

interface CategoryCardProps {
  category: Category
  index: number
  onEdit: () => void
  onDelete: () => void
  onTogglePin: () => void
  onToggleFavorite: () => void
  onView?: () => void
  viewType?: ViewType
}

export function CategoryCard({
  category,
  index,
  onEdit,
  onDelete,
  onTogglePin,
  onToggleFavorite,
  onView,
  viewType = 'grid'
}: CategoryCardProps) {
  const IconComponent = iconMap[category.icon] || iconMap['folder']
  
  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { 
        addSuffix: true, 
        locale: ar 
      })
    } catch {
      return 'منذ وقت قريب'
    }
  }

  // Get layout classes based on view type
  const getCardClasses = () => {
    switch (viewType) {
      case 'list':
        return "flex flex-row items-center p-4 h-24"
      case 'compact':
        return "flex flex-col p-3 h-32"
      case 'masonry':
        return "flex flex-col p-4"
      default: // grid
        return "flex flex-col p-6 h-48"
    }
  }

  const getIconSize = () => {
    switch (viewType) {
      case 'compact':
        return "w-6 h-6"
      case 'list':
        return "w-8 h-8"
      default:
        return "w-10 h-10"
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      whileHover={{ y: viewType === 'list' ? 0 : -4, scale: viewType === 'compact' ? 1.05 : 1.02 }}
      whileTap={{ scale: 0.98 }}
      className="group"
    >
      <Card className={`
        relative overflow-hidden border-border/50 hover:border-primary/30
        transition-all duration-300 hover:shadow-lg bg-gradient-to-br
        from-background via-muted/10 to-accent/5 cursor-pointer h-full
        ${getCardClasses()}
      `}>
        {/* Streamlined Color Indicator */}
        <div
          className="absolute top-0 right-0 w-1 h-full opacity-60 group-hover:opacity-100 transition-opacity duration-300"
          style={{ backgroundColor: category.color }}
        />

        {/* Subtle Background Effect */}
        <div
          className="absolute inset-0 opacity-[0.01] group-hover:opacity-[0.03] transition-opacity duration-300"
          style={{
            background: `radial-gradient(circle at top right, ${category.color}, transparent 70%)`
          }}
        />


        <CardContent className={`relative z-10 h-full ${viewType === 'list' ? 'flex items-center' : 'flex flex-col'}`}>
          {/* Streamlined Header */}
          <div className={`flex items-center justify-between ${viewType === 'list' ? 'flex-1' : 'mb-4'}`}>
            <div className={`flex items-center ${viewType === 'list' ? 'space-x-4 space-x-reverse' : 'space-x-3 space-x-reverse'}`}>
              {/* Simplified Icon Container */}
              <div
                className={`
                  ${getIconSize() === 'w-6 h-6' ? 'w-10 h-10' : getIconSize() === 'w-8 h-8' ? 'w-12 h-12' : 'w-14 h-14'}
                  rounded-xl flex items-center justify-center shadow-sm
                  group-hover:shadow-md transition-shadow duration-300
                `}
                style={{ backgroundColor: `${category.color}15` }}
              >
                <IconComponent
                  className={`${getIconSize()} group-hover:scale-110 transition-transform duration-300`}
                  style={{ color: category.color }}
                />
              </div>

              {/* Category Info */}
              <div className={`${viewType === 'list' ? 'flex-1' : ''}`}>
                <div className="flex items-center space-x-2 space-x-reverse mb-1">
                  <h3 className={`font-semibold text-foreground group-hover:text-primary transition-colors duration-300 ${
                    viewType === 'compact' ? 'text-sm' : viewType === 'list' ? 'text-lg' : 'text-base'
                  }`}>
                    {category.name}
                  </h3>

                  {/* Status Badges */}
                  <div className="flex items-center space-x-1 space-x-reverse">
                    {category.is_pinned && (
                      <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
                        <Pin className="w-3 h-3" />
                      </Badge>
                    )}
                    {category.is_favorite && (
                      <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
                        <Star className="w-3 h-3" />
                      </Badge>
                    )}
                  </div>
                </div>

                {viewType !== 'compact' && category.description && (
                  <p className={`text-muted-foreground ${viewType === 'list' ? 'text-sm' : 'text-xs'} line-clamp-2`}>
                    {category.description}
                  </p>
              </div>
            </div>

            {/* Streamlined Actions Menu - Only Three Dots */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="opacity-60 group-hover:opacity-100 transition-all duration-300 w-8 h-8 hover:bg-muted/50 rounded-lg hover:scale-105"
                >
                  <MoreHorizontal className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                {onView && (
                  <DropdownMenuItem onClick={onView} className="hover:bg-muted/50 transition-colors">
                    <Eye className="w-4 h-4 mr-2" />
                    عرض الملاحظات
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem onClick={onEdit} className="hover:bg-muted/50 transition-colors">
                  <Edit className="w-4 h-4 mr-2" />
                  تعديل الفئة
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={onTogglePin} className="hover:bg-muted/50 transition-colors">
                  <Pin className="w-4 h-4 mr-2" />
                  {category.is_pinned ? 'إلغاء التثبيت' : 'تثبيت الفئة'}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={onToggleFavorite} className="hover:bg-muted/50 transition-colors">
                  <Star className="w-4 h-4 mr-2" />
                  {category.is_favorite ? 'إزالة من المفضلة' : 'إضافة للمفضلة'}
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={onDelete} className="hover:bg-destructive/10 text-destructive transition-colors">
                  <Trash2 className="w-4 h-4 mr-2" />
                  حذف الفئة
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          {/* Footer - Stats and Date */}
          {viewType !== 'compact' && viewType !== 'list' && (
            <div className="mt-auto pt-4 border-t border-border/30">
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <div className="flex items-center space-x-2 space-x-reverse">
                  <FileText className="w-3 h-3" />
                  <span>{category.notes_count || 0} ملاحظة</span>
                </div>
                <span>{formatDate(category.updated_at || category.created_at)}</span>
              </div>
            </div>
          )}

          {/* List View Additional Info */}
          {viewType === 'list' && (
            <div className="flex items-center space-x-4 space-x-reverse text-xs text-muted-foreground">
              <div className="flex items-center space-x-1 space-x-reverse">
                <FileText className="w-3 h-3" />
                <span>{category.notes_count || 0}</span>
              </div>
              <span>{formatDate(category.updated_at || category.created_at)}</span>
            </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
