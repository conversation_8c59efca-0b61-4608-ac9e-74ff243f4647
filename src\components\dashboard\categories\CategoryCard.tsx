import { motion } from "framer-motion"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Eye, 
  Pin, 
  Star,
  Calendar,
  FileText
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import type { Category } from "@/lib/supabase"
import { formatDistanceToNow } from "date-fns"
import { ar } from "date-fns/locale"

// Icon mapping for categories
const iconMap: { [key: string]: React.ElementType } = {
  'folder': require('lucide-react').Folder,
  'briefcase': require('lucide-react').Briefcase,
  'book': require('lucide-react').Book,
  'brain': require('lucide-react').Brain,
  'edit': require('lucide-react').Edit,
  'target': require('lucide-react').Target,
  'bar-chart': require('lucide-react').BarChart3,
  'home': require('lucide-react').Home,
  'heart': require('lucide-react').Heart,
  'star': require('lucide-react').Star,
  'lightbulb': require('lucide-react').Lightbulb,
  'camera': require('lucide-react').Camera,
  'music': require('lucide-react').Music,
  'plane': require('lucide-react').Plane,
  'shopping-cart': require('lucide-react').ShoppingCart,
  'coffee': require('lucide-react').Coffee,
}

interface CategoryCardProps {
  category: Category
  index: number
  onEdit: () => void
  onDelete: () => void
  onTogglePin: () => void
  onToggleFavorite: () => void
}

export function CategoryCard({
  category,
  index,
  onEdit,
  onDelete,
  onTogglePin,
  onToggleFavorite
}: CategoryCardProps) {
  const IconComponent = iconMap[category.icon] || iconMap['folder']
  
  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { 
        addSuffix: true, 
        locale: ar 
      })
    } catch {
      return 'منذ وقت قريب'
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      whileHover={{ y: -4, scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
    >
      <Card className="relative overflow-hidden border-border/50 hover:border-primary/20 transition-all duration-300 hover:shadow-xl bg-gradient-to-br from-background to-muted/20 group cursor-pointer h-full">
        {/* Color Indicator */}
        <div 
          className="absolute top-0 right-0 w-1 h-full"
          style={{ backgroundColor: category.color }}
        />
        
        {/* Background Gradient */}
        <div 
          className="absolute inset-0 opacity-5 group-hover:opacity-10 transition-opacity duration-300"
          style={{ backgroundColor: category.color }}
        />
        
        {/* Animated Background Shape */}
        <motion.div
          animate={{
            scale: [1, 1.1, 1],
            rotate: [0, 5, 0],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut",
          }}
          className="absolute -top-4 -left-4 w-16 h-16 opacity-10 rounded-full blur-xl"
          style={{ backgroundColor: category.color }}
        />

        <CardContent className="p-6 relative z-10 h-full flex flex-col">
          {/* Header */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center space-x-3 space-x-reverse">
              <div 
                className="w-12 h-12 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-sm"
                style={{ backgroundColor: `${category.color}20` }}
              >
                <IconComponent 
                  className="w-6 h-6" 
                  style={{ color: category.color }}
                />
              </div>
              
              <div className="flex flex-col space-y-1">
                {category.is_pinned && (
                  <Pin className="w-4 h-4 text-orange-500" />
                )}
                {category.is_favorite && (
                  <Star className="w-4 h-4 text-yellow-500 fill-current" />
                )}
              </div>
            </div>

            {/* Actions Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="opacity-0 group-hover:opacity-100 transition-opacity w-8 h-8 hover:bg-muted/50"
                >
                  <MoreHorizontal className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem onClick={onEdit}>
                  <Edit className="w-4 h-4 mr-2" />
                  تعديل
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Eye className="w-4 h-4 mr-2" />
                  عرض الملاحظات
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={onTogglePin}>
                  <Pin className="w-4 h-4 mr-2" />
                  {category.is_pinned ? 'إلغاء التثبيت' : 'تثبيت'}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={onToggleFavorite}>
                  <Star className="w-4 h-4 mr-2" />
                  {category.is_favorite ? 'إزالة من المفضلة' : 'إضافة للمفضلة'}
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  onClick={onDelete}
                  className="text-red-600 focus:text-red-600"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  حذف
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Content */}
          <div className="flex-1">
            <h3 className="text-lg font-bold text-foreground mb-2 group-hover:text-primary transition-colors duration-300 line-clamp-1">
              {category.name}
            </h3>
            
            {category.description && (
              <p className="text-sm text-muted-foreground mb-4 line-clamp-2 leading-relaxed">
                {category.description}
              </p>
            )}
          </div>

          {/* Footer */}
          <div className="space-y-3 pt-4 border-t border-border/30">
            {/* Notes Count */}
            <div className="flex items-center justify-between">
              <Badge 
                variant="secondary" 
                className="flex items-center space-x-1 space-x-reverse"
              >
                <FileText className="w-3 h-3" />
                <span>{category.notes_count} ملاحظة</span>
              </Badge>
              
              {category.notes_count === 0 && (
                <Badge variant="outline" className="text-orange-600 border-orange-200">
                  غير نشطة
                </Badge>
              )}
            </div>

            {/* Creation Date */}
            <div className="flex items-center space-x-1 space-x-reverse text-xs text-muted-foreground">
              <Calendar className="w-3 h-3" />
              <span>{formatDate(category.created_at)}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
