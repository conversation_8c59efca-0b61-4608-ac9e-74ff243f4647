import { motion } from "framer-motion"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Pin,
  Star,
  Calendar,
  FileText,
  Folder,
  Briefcase,
  Book,
  Brain,
  Target,
  BarChart3,
  Home,
  Heart,
  Lightbulb,
  Camera,
  Music,
  Plane,
  ShoppingCart,
  Coffee,
  Gamepad2,
  Dumbbell,
  Car,
  Smartphone,
  Laptop,
  Palette,
  Clock,
  Edit3
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import type { Category } from "@/lib/supabase"
import { formatDistanceToNow } from "date-fns"
import { ar } from "date-fns/locale"

// Icon mapping for categories
const iconMap: { [key: string]: React.ElementType } = {
  'folder': Folder,
  'briefcase': Briefcase,
  'book': Book,
  'brain': Brain,
  'edit': Edit3,
  'target': Target,
  'bar-chart': Bar<PERSON>hart3,
  'home': Home,
  'heart': Heart,
  'star': Star,
  'lightbulb': Lightbulb,
  'camera': Camera,
  'music': Music,
  'plane': Plane,
  'shopping-cart': ShoppingCart,
  'coffee': Coffee,
  'gamepad': Gamepad2,
  'dumbbell': Dumbbell,
  'car': Car,
  'smartphone': Smartphone,
  'laptop': Laptop,
  'palette': Palette,
  'clock': Clock,
  'calendar': Calendar,
}

interface CategoryCardProps {
  category: Category
  index: number
  onEdit: () => void
  onDelete: () => void
  onTogglePin: () => void
  onToggleFavorite: () => void
}

export function CategoryCard({
  category,
  index,
  onEdit,
  onDelete,
  onTogglePin,
  onToggleFavorite
}: CategoryCardProps) {
  const IconComponent = iconMap[category.icon] || iconMap['folder']
  
  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { 
        addSuffix: true, 
        locale: ar 
      })
    } catch {
      return 'منذ وقت قريب'
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      whileHover={{ y: -6, scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      className="group"
    >
      <Card className="relative overflow-hidden border-border/50 hover:border-primary/30 transition-all duration-500 hover:shadow-2xl bg-gradient-to-br from-background via-muted/10 to-accent/5 cursor-pointer h-full">
        {/* Enhanced Color Indicator */}
        <div
          className="absolute top-0 right-0 w-2 h-full opacity-80 group-hover:opacity-100 transition-opacity duration-300"
          style={{
            background: `linear-gradient(to bottom, ${category.color}, ${category.color}80)`
          }}
        />

        {/* Enhanced Background Effects */}
        <div
          className="absolute inset-0 opacity-[0.02] group-hover:opacity-[0.05] transition-opacity duration-500"
          style={{
            background: `radial-gradient(circle at top right, ${category.color}, transparent 60%)`
          }}
        />

        {/* Multiple Animated Background Shapes */}
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 10, 0],
            opacity: [0.05, 0.1, 0.05]
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
          }}
          className="absolute -top-6 -left-6 w-20 h-20 rounded-full blur-xl group-hover:opacity-20 transition-opacity duration-300"
          style={{ backgroundColor: category.color }}
        />

        <motion.div
          animate={{
            scale: [1, 1.1, 1],
            rotate: [0, -5, 0],
            opacity: [0.03, 0.08, 0.03]
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
          className="absolute -bottom-4 -right-4 w-16 h-16 rounded-full blur-xl"
          style={{ backgroundColor: category.color }}
        />

        <CardContent className="p-6 relative z-10 h-full flex flex-col">
          {/* Header - Simplified and Enhanced */}
          <div className="flex items-start justify-between mb-6">
            <div className="flex items-center space-x-4 space-x-reverse">
              {/* Enhanced Icon Container */}
              <motion.div
                className="relative"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.2 }}
              >
                <div
                  className="w-14 h-14 rounded-2xl flex items-center justify-center shadow-lg relative overflow-hidden group-hover:shadow-xl transition-shadow duration-300"
                  style={{ backgroundColor: `${category.color}15` }}
                >
                  {/* Icon background glow */}
                  <div
                    className="absolute inset-0 rounded-2xl opacity-20 blur-sm group-hover:opacity-30 transition-opacity duration-300"
                    style={{ backgroundColor: category.color }}
                  />
                  <IconComponent
                    className="w-7 h-7 relative z-10 group-hover:scale-110 transition-transform duration-300"
                    style={{ color: category.color }}
                  />
                </div>

                {/* Status Indicators */}
                <div className="absolute -top-1 -right-1 flex flex-col space-y-1">
                  {category.is_pinned && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="w-5 h-5 bg-orange-500 rounded-full flex items-center justify-center shadow-sm"
                    >
                      <Pin className="w-3 h-3 text-white" />
                    </motion.div>
                  )}
                  {category.is_favorite && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.1 }}
                      className="w-5 h-5 bg-yellow-500 rounded-full flex items-center justify-center shadow-sm"
                    >
                      <Star className="w-3 h-3 text-white fill-current" />
                    </motion.div>
                  )}
                </div>
              </motion.div>
            </div>

            {/* Actions Menu - Enhanced */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="opacity-0 group-hover:opacity-100 transition-all duration-300 w-9 h-9 hover:bg-muted/50 rounded-xl hover:scale-105"
                >
                  <MoreHorizontal className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-52 bg-background/95 backdrop-blur-md border border-border/50 shadow-xl">
                <DropdownMenuItem onClick={onEdit} className="hover:bg-muted/50 transition-colors">
                  <Edit className="w-4 h-4 mr-2" />
                  تعديل الفئة
                </DropdownMenuItem>
                <DropdownMenuItem className="hover:bg-muted/50 transition-colors">
                  <Eye className="w-4 h-4 mr-2" />
                  عرض الملاحظات
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={onTogglePin} className="hover:bg-muted/50 transition-colors">
                  <Pin className="w-4 h-4 mr-2" />
                  {category.is_pinned ? 'إلغاء التثبيت' : 'تثبيت الفئة'}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={onToggleFavorite} className="hover:bg-muted/50 transition-colors">
                  <Star className="w-4 h-4 mr-2" />
                  {category.is_favorite ? 'إزالة من المفضلة' : 'إضافة للمفضلة'}
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={onDelete}
                  className="text-red-600 focus:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  حذف الفئة
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Content - Enhanced */}
          <div className="flex-1 space-y-4">
            <div>
              <h3 className="text-xl font-bold text-foreground mb-3 group-hover:text-primary transition-colors duration-300 line-clamp-1 leading-tight">
                {category.name}
              </h3>

              {category.description ? (
                <p className="text-sm text-muted-foreground line-clamp-2 leading-relaxed">
                  {category.description}
                </p>
              ) : (
                <p className="text-sm text-muted-foreground/60 italic">
                  لا يوجد وصف لهذه الفئة
                </p>
              )}
            </div>

            {/* Usage Progress Bar */}
            <div className="space-y-2">
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>مستوى الاستخدام</span>
                <span>{category.notes_count > 0 ? 'نشطة' : 'غير مستخدمة'}</span>
              </div>
              <div className="w-full bg-muted/30 rounded-full h-1.5">
                <motion.div
                  className="h-1.5 rounded-full transition-all duration-500"
                  style={{
                    backgroundColor: category.color,
                    width: `${Math.min((category.notes_count / 10) * 100, 100)}%`
                  }}
                  initial={{ width: 0 }}
                  animate={{ width: `${Math.min((category.notes_count / 10) * 100, 100)}%` }}
                  transition={{ duration: 1, delay: index * 0.1 }}
                />
              </div>
            </div>
          </div>

          {/* Footer - Enhanced */}
          <div className="space-y-4 pt-6 border-t border-border/30">
            {/* Stats Row */}
            <div className="flex items-center justify-between">
              <Badge
                variant="secondary"
                className="flex items-center space-x-1 space-x-reverse bg-muted/50 hover:bg-muted/70 transition-colors"
                style={{
                  borderColor: `${category.color}30`,
                  backgroundColor: `${category.color}10`
                }}
              >
                <FileText className="w-3 h-3" />
                <span className="font-medium">{category.notes_count} ملاحظة</span>
              </Badge>

              <div className="flex items-center space-x-2 space-x-reverse">
                {category.notes_count === 0 && (
                  <Badge variant="outline" className="text-orange-600 border-orange-200 bg-orange-50 dark:bg-orange-900/20">
                    غير نشطة
                  </Badge>
                )}
                {category.notes_count > 5 && (
                  <Badge variant="outline" className="text-green-600 border-green-200 bg-green-50 dark:bg-green-900/20">
                    نشطة
                  </Badge>
                )}
              </div>
            </div>

            {/* Creation Date and Quick Actions */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-1 space-x-reverse text-xs text-muted-foreground">
                <Calendar className="w-3 h-3" />
                <span>{formatDate(category.created_at)}</span>
              </div>

              <div className="flex items-center space-x-1 space-x-reverse">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onEdit}
                  className="opacity-0 group-hover:opacity-100 transition-opacity h-7 px-2 text-xs hover:bg-muted/50"
                >
                  تعديل
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
