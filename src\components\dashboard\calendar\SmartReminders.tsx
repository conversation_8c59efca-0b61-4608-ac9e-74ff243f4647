import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Bell,
  ChevronDown,
  ChevronUp,
  Clock,
  CheckCircle2,
  Pause,
  Sparkles
} from 'lucide-react'

export interface SmartRemindersProps {
  reminders?: any[]
  onReminderAction?: (id: string, action: 'complete' | 'snooze' | 'dismiss') => void
}

export function SmartReminders({ reminders = [], onReminderAction }: SmartRemindersProps) {
  const [isExpanded, setIsExpanded] = useState(true)

  // Mock reminders
  const mockReminders = [
    {
      id: '1',
      title: 'اجتماع فريق العمل',
      time: '10:00 ص',
      timeRemaining: '30 دقيقة',
      priority: 'high',
      category: 'عمل',
      color: '#ef4444',
      aiSuggestion: 'تحضير المستندات قبل الاجتماع'
    },
    {
      id: '2',
      title: 'موعد طبي',
      time: '2:00 م',
      timeRemaining: 'غداً',
      priority: 'medium',
      category: 'صحة',
      color: '#22c55e',
      aiSuggestion: 'تجهيز التقارير الطبية السابقة'
    }
  ]

  const handleAction = (id: string, action: 'complete' | 'snooze' | 'dismiss') => {
    onReminderAction?.(id, action)
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.4 }}
      className="bg-background rounded-xl border border-border/50 shadow-sm p-4"
    >
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="flex items-center justify-between w-full mb-3"
      >
        <div className="flex items-center gap-2">
          <Bell className="w-5 h-5 text-primary" />
          <h3 className="font-semibold">التذكيرات الذكية</h3>
          <Badge variant="secondary" className="text-xs">
            {mockReminders.length}
          </Badge>
        </div>
        {isExpanded ? (
          <ChevronUp className="w-4 h-4 text-muted-foreground" />
        ) : (
          <ChevronDown className="w-4 h-4 text-muted-foreground" />
        )}
      </button>

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
            className="space-y-3"
          >
            {mockReminders.map((reminder, index) => (
              <motion.div
                key={reminder.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.2, delay: index * 0.1 }}
                className="p-3 rounded-lg border border-border/30 bg-muted/10 relative overflow-hidden"
              >
                {/* Background gradient based on priority */}
                <div 
                  className="absolute inset-0 opacity-10" 
                  style={{ 
                    background: `linear-gradient(to right, ${reminder.color}22, transparent)` 
                  }} 
                />
                
                <div className="relative">
                  {/* Header */}
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <div
                          className="w-2 h-2 rounded-full"
                          style={{ backgroundColor: reminder.color }}
                        />
                        <span className="font-medium text-sm">
                          {reminder.title}
                        </span>
                      </div>
                      <div className="flex items-center gap-2 text-xs text-muted-foreground mt-1">
                        <Clock className="w-3 h-3" />
                        <span>{reminder.time}</span>
                        <span>•</span>
                        <span className="text-primary font-medium">
                          متبقي {reminder.timeRemaining}
                        </span>
                      </div>
                    </div>
                    <Badge variant="secondary" className="text-xs">
                      {reminder.category}
                    </Badge>
                  </div>
                  
                  {/* AI Suggestion */}
                  <div className="bg-muted/30 rounded-md p-2 mb-2 text-xs flex items-start gap-2">
                    <Sparkles className="w-3 h-3 text-amber-500 mt-0.5 flex-shrink-0" />
                    <span className="text-muted-foreground">{reminder.aiSuggestion}</span>
                  </div>
                  
                  {/* Actions */}
                  <div className="flex items-center justify-end gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleAction(reminder.id, 'complete')}
                      className="h-7 px-2 text-xs"
                    >
                      <CheckCircle2 className="w-3 h-3 mr-1 text-green-500" />
                      تم
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleAction(reminder.id, 'snooze')}
                      className="h-7 px-2 text-xs"
                    >
                      <Pause className="w-3 h-3 mr-1 text-blue-500" />
                      تأجيل
                    </Button>
                  </div>
                </div>
              </motion.div>
            ))}

            {/* Empty state */}
            {mockReminders.length === 0 && (
              <div className="text-center py-6">
                <Bell className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">لا توجد تذكيرات حالياً</p>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}
