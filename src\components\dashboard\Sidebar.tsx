import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Link, useLocation } from "react-router-dom"
import { Button } from "@/components/ui/button"
import { 
  Home, 
  StickyNote, 
  FolderOpen, 
  Bell, 
  Calendar, 
  Settings, 
  Crown,
  Menu,
  X,
  Sparkles,
  ChevronLeft,
  Plus
} from "lucide-react"

interface SidebarProps {
  isCollapsed: boolean
  onToggle: () => void
  isMobile?: boolean
}

const navigationItems = [
  {
    id: 'dashboard',
    name: 'لوحة التحكم',
    icon: Home,
    path: '/dashboard',
    color: 'text-primary'
  },
  {
    id: 'notes',
    name: 'الملاحظات',
    icon: StickyNote,
    path: '/dashboard/notes',
    color: 'text-blue-500'
  },
  {
    id: 'categories',
    name: 'التصنيفات',
    icon: FolderOpen,
    path: '/dashboard/categories',
    color: 'text-green-500'
  },
  {
    id: 'notifications',
    name: 'الإشعارات',
    icon: Bell,
    path: '/dashboard/notifications',
    color: 'text-orange-500'
  },
  {
    id: 'calendar',
    name: 'التقويم',
    icon: Calendar,
    path: '/dashboard/calendar',
    color: 'text-purple-500'
  },
  {
    id: 'settings',
    name: 'الإعدادات',
    icon: Settings,
    path: '/dashboard/settings',
    color: 'text-gray-500'
  },
  {
    id: 'upgrade',
    name: 'الترقية',
    icon: Crown,
    path: '/dashboard/upgrade',
    color: 'text-yellow-500'
  }
]

export function Sidebar({ isCollapsed, onToggle, isMobile = false }: SidebarProps) {
  const location = useLocation()
  const [hoveredItem, setHoveredItem] = useState<string | null>(null)

  return (
    <>
      {/* Mobile Overlay */}
      {isMobile && !isCollapsed && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={onToggle}
        />
      )}

      {/* Sidebar */}
      <motion.aside
        initial={false}
        animate={{
          width: isCollapsed ? (isMobile ? 0 : 80) : 280,
          x: isMobile && isCollapsed ? -280 : 0
        }}
        transition={{ duration: 0.3, ease: "easeInOut" }}
        className={`
          h-full bg-background/95 backdrop-blur-md border-l border-border/50 shadow-xl
          ${isMobile ? 'fixed top-0 right-0 z-50' : 'relative'}
          overflow-hidden flex-shrink-0
        `}
      >
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-muted/30 via-accent/10 to-background"></div>
        
        {/* Animated Background Shapes */}
        <motion.div
          animate={{
            rotate: 360,
            scale: [1, 1.1, 1],
          }}
          transition={{
            rotate: { duration: 20, repeat: Infinity, ease: "linear" },
            scale: { duration: 8, repeat: Infinity, ease: "easeInOut" },
          }}
          className="absolute top-10 left-10 w-16 h-16 rounded-full bg-primary/10 blur-xl"
        />

        <div className="relative z-10 h-full flex flex-col">
          {/* Header */}
          <div className="p-4 border-b border-border/30 bg-gradient-to-r from-primary/5 to-secondary/5">
            <div className="flex items-center justify-between">
              {!isCollapsed && (
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="flex items-center space-x-3 space-x-reverse"
                >
                  <div className="w-10 h-10 bg-gradient-to-r from-primary to-secondary rounded-xl flex items-center justify-center shadow-lg">
                    <Sparkles className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h1 className="text-xl font-bold text-primary">Nots</h1>
                    <p className="text-xs text-muted-foreground">لوحة التحكم</p>
                  </div>
                </motion.div>
              )}

              {isCollapsed && (
                <div className="w-10 h-10 bg-gradient-to-r from-primary to-secondary rounded-xl flex items-center justify-center shadow-lg mx-auto">
                  <Sparkles className="w-5 h-5 text-white" />
                </div>
              )}

              <Button
                variant="ghost"
                size="icon"
                onClick={onToggle}
                className="hover:bg-muted/50 hover:scale-105 transition-all duration-200 rounded-lg"
                title={isCollapsed ? "توسيع الشريط الجانبي" : "طي الشريط الجانبي"}
              >
                <ChevronLeft className={`w-4 h-4 transition-transform duration-200 ${isCollapsed ? 'rotate-180' : ''}`} />
              </Button>
            </div>
          </div>

          {/* Quick Add Button */}
          <div className="p-4">
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Button
                className="w-full bg-gradient-to-r from-primary to-secondary hover:opacity-90 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 group"
                size={isCollapsed ? "icon" : "default"}
              >
                <Plus className="w-4 h-4 group-hover:rotate-90 transition-transform duration-200" />
                {!isCollapsed && <span className="mr-2 font-medium">ملاحظة جديدة</span>}
              </Button>
            </motion.div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-1">
            {navigationItems.map((item) => {
              const Icon = item.icon
              const isActive = location.pathname === item.path
              const isHovered = hoveredItem === item.id

              return (
                <motion.div
                  key={item.id}
                  onHoverStart={() => setHoveredItem(item.id)}
                  onHoverEnd={() => setHoveredItem(null)}
                  whileHover={{ x: -2 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Link
                    to={item.path}
                    className={`
                      relative flex items-center space-x-3 space-x-reverse p-3 rounded-xl transition-all duration-200 group
                      ${isActive
                        ? 'bg-gradient-to-r from-primary/15 to-secondary/10 text-primary border border-primary/20 shadow-sm'
                        : isHovered
                          ? 'bg-muted/70 text-foreground shadow-sm'
                          : 'hover:bg-muted/50 text-muted-foreground hover:text-foreground'
                      }
                    `}
                  >
                    {/* Active indicator line */}
                    {isActive && (
                      <motion.div
                        layoutId="activeIndicatorLine"
                        className="absolute right-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-gradient-to-b from-primary to-secondary rounded-l-full"
                      />
                    )}

                    <div className={`
                      w-8 h-8 rounded-lg flex items-center justify-center transition-all duration-200
                      ${isActive
                        ? 'bg-primary/20 shadow-sm'
                        : isHovered
                          ? 'bg-primary/10'
                          : 'group-hover:bg-primary/5'
                      }
                    `}>
                      <Icon className={`w-4 h-4 transition-all duration-200 ${
                        isActive ? 'text-primary' : isHovered ? item.color : 'text-muted-foreground group-hover:text-foreground'
                      }`} />
                    </div>

                    <AnimatePresence>
                      {!isCollapsed && (
                        <motion.div
                          initial={{ opacity: 0, x: 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: 20 }}
                          className="flex-1"
                        >
                          <span className={`font-medium transition-all duration-200 ${
                            isActive ? 'text-primary' : 'text-foreground'
                          }`}>
                            {item.name}
                          </span>
                        </motion.div>
                      )}
                    </AnimatePresence>

                    {isActive && !isCollapsed && (
                      <motion.div
                        layoutId="activeIndicator"
                        className="w-2 h-2 bg-primary rounded-full shadow-sm"
                      />
                    )}

                    {/* Tooltip for collapsed state */}
                    {isCollapsed && isHovered && (
                      <motion.div
                        initial={{ opacity: 0, x: 10 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: 10 }}
                        className="absolute left-full ml-2 px-2 py-1 bg-foreground text-background text-sm rounded-md whitespace-nowrap z-50"
                      >
                        {item.name}
                      </motion.div>
                    )}
                  </Link>
                </motion.div>
              )
            })}
          </nav>

          {/* Footer */}
          <div className="p-4 border-t border-border/30 bg-gradient-to-r from-muted/20 to-accent/10">
            {!isCollapsed ? (
              <div className="text-center">
                <p className="text-xs text-muted-foreground">
                  Nots Dashboard v1.0
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  مدعوم بالذكاء الاصطناعي
                </p>
              </div>
            ) : (
              <div className="flex justify-center">
                <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
              </div>
            )}
          </div>
        </div>
      </motion.aside>
    </>
  )
}
