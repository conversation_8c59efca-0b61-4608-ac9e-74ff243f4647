import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Link, useLocation } from "react-router-dom"
import { Button } from "@/components/ui/button"
import { 
  Home, 
  StickyNote, 
  FolderOpen, 
  Bell, 
  Calendar, 
  Settings, 
  Crown,
  Menu,
  X,
  Sparkles,
  ChevronLeft,
  Plus
} from "lucide-react"

interface SidebarProps {
  isCollapsed: boolean
  onToggle: () => void
  isMobile?: boolean
}

const navigationItems = [
  {
    id: 'dashboard',
    name: 'لوحة التحكم',
    icon: Home,
    path: '/dashboard',
    color: 'text-primary'
  },
  {
    id: 'notes',
    name: 'الملاحظات',
    icon: StickyNote,
    path: '/dashboard/notes',
    color: 'text-blue-500'
  },
  {
    id: 'categories',
    name: 'التصنيفات',
    icon: FolderOpen,
    path: '/dashboard/categories',
    color: 'text-green-500'
  },
  {
    id: 'notifications',
    name: 'الإشعارات',
    icon: Bell,
    path: '/dashboard/notifications',
    color: 'text-orange-500'
  },
  {
    id: 'calendar',
    name: 'التقويم',
    icon: Calendar,
    path: '/dashboard/calendar',
    color: 'text-purple-500'
  },
  {
    id: 'settings',
    name: 'الإعدادات',
    icon: Settings,
    path: '/dashboard/settings',
    color: 'text-gray-500'
  },
  {
    id: 'upgrade',
    name: 'الترقية',
    icon: Crown,
    path: '/dashboard/upgrade',
    color: 'text-yellow-500'
  }
]

export function Sidebar({ isCollapsed, onToggle, isMobile = false }: SidebarProps) {
  const location = useLocation()
  const [hoveredItem, setHoveredItem] = useState<string | null>(null)

  return (
    <>
      {/* Mobile Overlay */}
      {isMobile && !isCollapsed && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={onToggle}
        />
      )}

      {/* Sidebar */}
      <motion.aside
        initial={false}
        animate={{
          width: isCollapsed ? (isMobile ? 0 : 80) : 280,
          x: isMobile && isCollapsed ? -280 : 0
        }}
        transition={{ duration: 0.3, ease: "easeInOut" }}
        className={`
          fixed top-0 right-0 h-full bg-background/95 backdrop-blur-md border-l border-border/50 z-50
          ${isMobile ? 'lg:relative' : 'relative'}
          overflow-hidden
        `}
      >
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-muted/30 via-accent/10 to-background"></div>
        
        {/* Animated Background Shapes */}
        <motion.div
          animate={{
            rotate: 360,
            scale: [1, 1.1, 1],
          }}
          transition={{
            rotate: { duration: 20, repeat: Infinity, ease: "linear" },
            scale: { duration: 8, repeat: Infinity, ease: "easeInOut" },
          }}
          className="absolute top-10 left-10 w-16 h-16 rounded-full bg-primary/10 blur-xl"
        />

        <div className="relative z-10 h-full flex flex-col">
          {/* Header */}
          <div className="p-4 border-b border-border/30">
            <div className="flex items-center justify-between">
              {!isCollapsed && (
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="flex items-center space-x-2 space-x-reverse"
                >
                  <div className="w-8 h-8 bg-gradient-to-r from-primary to-secondary rounded-lg flex items-center justify-center">
                    <Sparkles className="w-4 h-4 text-white" />
                  </div>
                  <h1 className="text-xl font-bold text-primary">Nots</h1>
                </motion.div>
              )}
              
              <Button
                variant="ghost"
                size="icon"
                onClick={onToggle}
                className="hover:bg-muted/50"
              >
                {isCollapsed ? <Menu className="w-5 h-5" /> : <X className="w-5 h-5" />}
              </Button>
            </div>
          </div>

          {/* Quick Add Button */}
          <div className="p-4">
            <Button
              className="w-full bg-gradient-to-r from-primary to-secondary hover:opacity-90 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
              size={isCollapsed ? "icon" : "default"}
            >
              <Plus className="w-4 h-4" />
              {!isCollapsed && <span className="mr-2">ملاحظة جديدة</span>}
            </Button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-2">
            {navigationItems.map((item) => {
              const Icon = item.icon
              const isActive = location.pathname === item.path
              
              return (
                <motion.div
                  key={item.id}
                  onHoverStart={() => setHoveredItem(item.id)}
                  onHoverEnd={() => setHoveredItem(null)}
                  whileHover={{ x: -4 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Link
                    to={item.path}
                    className={`
                      flex items-center space-x-3 space-x-reverse p-3 rounded-xl transition-all duration-200
                      ${isActive 
                        ? 'bg-primary/10 text-primary border border-primary/20' 
                        : 'hover:bg-muted/50 text-muted-foreground hover:text-foreground'
                      }
                    `}
                  >
                    <Icon className={`w-5 h-5 ${isActive ? 'text-primary' : item.color}`} />
                    
                    <AnimatePresence>
                      {!isCollapsed && (
                        <motion.span
                          initial={{ opacity: 0, x: 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: 20 }}
                          className="font-medium"
                        >
                          {item.name}
                        </motion.span>
                      )}
                    </AnimatePresence>

                    {isActive && !isCollapsed && (
                      <motion.div
                        layoutId="activeIndicator"
                        className="mr-auto w-2 h-2 bg-primary rounded-full"
                      />
                    )}
                  </Link>
                </motion.div>
              )
            })}
          </nav>

          {/* Collapse Toggle */}
          {!isMobile && (
            <div className="p-4 border-t border-border/30">
              <Button
                variant="ghost"
                size="sm"
                onClick={onToggle}
                className="w-full justify-start hover:bg-muted/50"
              >
                <ChevronLeft className={`w-4 h-4 transition-transform ${isCollapsed ? 'rotate-180' : ''}`} />
                {!isCollapsed && <span className="mr-2">طي الشريط الجانبي</span>}
              </Button>
            </div>
          )}
        </div>
      </motion.aside>
    </>
  )
}
