import { motion } from "framer-motion"
import { Theme<PERSON><PERSON>ider } from "@/components/theme/ThemeProvider"
import { ThemeToggle } from "@/components/theme/ThemeToggle"
import { <PERSON>rk<PERSON>, ArrowRight } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"

interface AuthLayoutProps {
  children: React.ReactNode
  title: string
  description?: string
  showBackButton?: boolean
  onBack?: () => void
  showLogo?: boolean
}

export function AuthLayout({ 
  children, 
  title, 
  description, 
  showBackButton = false, 
  onBack,
  showLogo = true 
}: AuthLayoutProps) {
  return (
    <ThemeProvider defaultTheme="system" storageKey="nots-auth-theme">
      <div className="min-h-screen bg-background font-tajawal" dir="rtl">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-muted/30 via-accent/10 to-background"></div>
        
        {/* Animated Background Shapes */}
        <motion.div
          animate={{
            rotate: 360,
            scale: [1, 1.1, 1],
          }}
          transition={{
            rotate: { duration: 20, repeat: Infinity, ease: "linear" },
            scale: { duration: 8, repeat: Infinity, ease: "easeInOut" },
          }}
          className="absolute top-20 right-20 w-32 h-32 rounded-full bg-primary/20 blur-xl"
        />
        
        <motion.div
          animate={{
            rotate: -360,
            scale: [1, 1.2, 1],
          }}
          transition={{
            rotate: { duration: 25, repeat: Infinity, ease: "linear" },
            scale: { duration: 12, repeat: Infinity, ease: "easeInOut" },
          }}
          className="absolute bottom-20 left-20 w-48 h-48 rounded-full bg-secondary/15 blur-xl"
        />

        {/* Header */}
        <header className="relative z-10 flex items-center justify-between p-6">
          <div className="flex items-center space-x-4 space-x-reverse">
            {showBackButton && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onBack}
                className="flex items-center space-x-1 space-x-reverse text-muted-foreground hover:text-foreground"
              >
                <ArrowRight className="w-4 h-4" />
                <span>رجوع</span>
              </Button>
            )}
          </div>
          
          {showLogo && (
            <motion.div
              whileHover={{ scale: 1.05 }}
              className="flex items-center space-x-2 space-x-reverse"
            >
              <div className="w-8 h-8 bg-gradient-to-r from-primary to-secondary rounded-xl flex items-center justify-center shadow-lg">
                <Sparkles className="w-5 h-5 text-primary-foreground" />
              </div>
              <h1 className="text-2xl font-bold text-primary nots-heading">Nots</h1>
            </motion.div>
          )}
          
          <ThemeToggle />
        </header>

        {/* Main Content */}
        <main className="relative z-10 flex items-center justify-center min-h-[calc(100vh-120px)] px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="w-full max-w-md mx-auto"
          >
            {/* Title and Description */}
            <div className="text-center mb-8">
              <motion.h2
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="text-3xl font-bold text-foreground mb-3 nots-heading"
              >
                {title}
              </motion.h2>
              
              {description && (
                <motion.p
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="text-muted-foreground text-lg leading-relaxed"
                >
                  {description}
                </motion.p>
              )}
            </div>

            {/* Form Content */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="bg-card/50 backdrop-blur-md border border-border/50 rounded-2xl p-8 nots-shadow-lg"
            >
              {children}
            </motion.div>
          </motion.div>
        </main>

        {/* Footer */}
        <footer className="relative z-10 text-center py-6 text-sm text-muted-foreground">
          <p>© 2024 Nots. جميع الحقوق محفوظة.</p>
        </footer>
      </div>
    </ThemeProvider>
  )
}
