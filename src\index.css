
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Arabic-first design system with RTL support */

@layer base {
  :root {
    /* Nots App - Purple-based color scheme */
    --background: 0 0% 100%;
    --foreground: 0 0% 0%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 0%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 0%;

    /* Primary purple: #6246ea */
    --primary: 252 77% 59%;
    --primary-foreground: 0 0% 100%;

    /* Secondary purple: #5D2DE6 */
    --secondary: 254 73% 54%;
    --secondary-foreground: 0 0% 100%;

    /* Background sections: #F3F6FD */
    --muted: 225 50% 98%;
    --muted-foreground: 215 16% 47%;

    /* Accent blue: #318CF6 */
    --accent: 210 94% 58%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 252 77% 59%;

    --radius: 0.75rem;

    /* Nots specific colors */
    --nots-purple: 252 77% 59%; /* #6246ea */
    --nots-purple-dark: 254 73% 54%; /* #5D2DE6 */
    --nots-purple-light: 248 85% 75%; /* #6061F0 */
    --nots-bg-light: 225 50% 98%; /* #F3F6FD */
    --nots-accent: 225 100% 85%; /* #DAD0FA */
    --nots-blue: 210 94% 58%; /* #318CF6 */
    --nots-green: 88 50% 53%; /* #88CE57 */
    --nots-orange: 20 85% 58%; /* #ED6C3E */
    --nots-text-secondary: 0 0% 30%; /* #4D4D4D */
    --nots-text-muted: 215 16% 47%; /* #475569 */

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Additional theme-aware colors */
    --app-store-bg: 0 0% 0%;
    --google-play-bg: 34 89% 47%;
    --phone-frame: 220 13% 18%;
    --phone-screen: 0 0% 100%;
    --mock-content: 220 13% 91%;
    --mock-text: 215 16% 47%;
  }

  .dark {
    /* Dark theme - Nots purple-based colors */
    --background: 240 10% 4%;
    --foreground: 0 0% 95%;

    --card: 240 10% 6%;
    --card-foreground: 0 0% 95%;

    --popover: 240 10% 6%;
    --popover-foreground: 0 0% 95%;

    --primary: 252 77% 65%;
    --primary-foreground: 240 10% 4%;

    --secondary: 254 73% 60%;
    --secondary-foreground: 240 10% 4%;

    --muted: 240 10% 10%;
    --muted-foreground: 215 16% 65%;

    --accent: 210 94% 65%;
    --accent-foreground: 240 10% 4%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 95%;

    --border: 240 10% 15%;
    --input: 240 10% 15%;
    --ring: 252 77% 65%;

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Additional theme-aware colors for dark mode */
    --app-store-bg: 0 0% 0%;
    --google-play-bg: 34 89% 47%;
    --phone-frame: 240 10% 15%;
    --phone-screen: 240 10% 6%;
    --mock-content: 240 10% 15%;
    --mock-text: 215 16% 65%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-tajawal;
    direction: rtl;
  }

  /* Arabic typography improvements */
  h1, h2, h3, h4, h5, h6 {
    @apply font-tajawal;
    line-height: 1.4;
    letter-spacing: -0.02em;
  }

  p {
    line-height: 1.7;
    letter-spacing: 0.01em;
  }

  /* RTL improvements for better Arabic text flow */
  [dir="rtl"] .text-right {
    text-align: right;
  }

  [dir="rtl"] .text-left {
    text-align: left;
  }

  /* Custom Arabic scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-primary/50 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-primary;
  }
}

@layer components {
  /* Nots app specific component styles */
  .nots-heading {
    @apply font-tajawal font-bold tracking-tight;
  }

  .nots-body {
    @apply font-tajawal leading-relaxed;
  }

  .gradient-purple {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--secondary)) 100%);
  }

  .gradient-purple-light {
    background: linear-gradient(135deg, hsl(var(--secondary)) 0%, hsl(var(--accent)) 100%);
  }

  .gradient-blue {
    background: linear-gradient(135deg, hsl(var(--accent)) 0%, hsl(var(--primary)) 100%);
  }

  /* Theme-aware gradient utilities */
  .bg-gradient-purple {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--secondary)) 100%);
  }

  .bg-gradient-purple-light {
    background: linear-gradient(135deg, hsl(var(--secondary)) 0%, hsl(var(--accent)) 100%);
  }

  .glass-effect {
    backdrop-filter: blur(10px);
    background: hsl(var(--background) / 0.1);
    border: 1px solid hsl(var(--border) / 0.2);
  }

  .nots-shadow {
    box-shadow: 0 10px 25px -5px hsl(var(--primary) / 0.1), 0 4px 6px -2px hsl(var(--primary) / 0.05);
  }

  .nots-shadow-lg {
    box-shadow: 0 20px 25px -5px hsl(var(--primary) / 0.1), 0 10px 10px -5px hsl(var(--primary) / 0.04);
  }

  .nots-shadow-xl {
    box-shadow: 0 25px 50px -12px hsl(var(--primary) / 0.15), 0 20px 25px -5px hsl(var(--primary) / 0.1);
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Theme-aware utility classes */
  .bg-app-store {
    background-color: hsl(var(--app-store-bg));
  }

  .bg-google-play {
    background-color: hsl(var(--google-play-bg));
  }

  .bg-phone-frame {
    background-color: hsl(var(--phone-frame));
  }

  .bg-phone-screen {
    background-color: hsl(var(--phone-screen));
  }

  .bg-mock-content {
    background-color: hsl(var(--mock-content));
  }

  .text-mock {
    color: hsl(var(--mock-text));
  }
}
