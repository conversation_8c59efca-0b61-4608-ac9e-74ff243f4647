import { useEffect, useState } from 'react'

import { DashboardLayout } from '@/components/dashboard/DashboardLayout'
import { CalendarView } from '@/components/dashboard/calendar/CalendarView'
import { CalendarHeader } from '@/components/dashboard/calendar/CalendarHeader'
import { CalendarSidebar } from '@/components/dashboard/calendar/CalendarSidebar'
import { SmartReminders } from '@/components/dashboard/calendar/SmartReminders'
import { SchedulingSuggestions } from '@/components/dashboard/calendar/SchedulingSuggestions'
import { SearchFilters } from '@/components/dashboard/calendar/CalendarSearch'
import { motion } from 'framer-motion'

export default function CalendarPage() {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [currentView, setCurrentView] = useState<'month' | 'week' | 'day' | 'list'>('month')
  const [searchQuery, setSearchQuery] = useState('')
  const [filters, setFilters] = useState<SearchFilters>({
    categories: [],
    priorities: [],
    dateRange: {}
  })
  
  useEffect(() => {
    // Set page title
    document.title = 'التقويم | Nots'
  }, [])

  const handleEventClick = (eventId: string) => {
    // Handle event click from sidebar
    console.log('Event clicked:', eventId)
  }

  const handleReminderAction = (id: string, action: 'complete' | 'snooze' | 'dismiss') => {
    // Handle reminder actions
    console.log('Reminder action:', id, action)
  }

  const handleSuggestionApply = (suggestion: any) => {
    // Handle applying AI suggestions
    console.log('Suggestion applied:', suggestion)
  }

  return (
    <DashboardLayout>
      <div className="flex flex-col h-full w-full">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="flex flex-col lg:flex-row gap-6 h-full"
        >
          {/* Main Calendar Area */}
          <div className="flex-1 flex flex-col min-h-0">
            <CalendarHeader 
              currentDate={currentDate}
              onDateChange={setCurrentDate}
              currentView={currentView}
              onViewChange={setCurrentView}
              searchQuery={searchQuery}
              onSearchChange={setSearchQuery}
              onFilterChange={setFilters}
            />
            <div className="flex-1 bg-background rounded-xl border border-border/50 shadow-sm overflow-hidden mt-4 min-h-[600px]">
              <CalendarView 
                currentDate={currentDate}
                currentView={currentView}
                searchQuery={searchQuery}
                filters={filters}
              />
            </div>
          </div>
          
          {/* Sidebar */}
          <div className="w-full lg:w-80 flex flex-col gap-4">
            <CalendarSidebar 
              onEventClick={handleEventClick}
              onFilterChange={setFilters}
            />
            <SmartReminders 
              onReminderAction={handleReminderAction}
            />
            <SchedulingSuggestions 
              onSuggestionApply={handleSuggestionApply}
            />
          </div>
        </motion.div>
      </div>
    </DashboardLayout>
  )
}
