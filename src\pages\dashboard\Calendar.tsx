import { useEffect, useState } from 'react'

import { DashboardLayout } from '@/components/dashboard/DashboardLayout'
import {
  CalendarView,
  CalendarHeader,
  CalendarSidebar,
  SmartReminders,
  SchedulingSuggestions,
  NotificationManager,
  CalendarAnalytics,
  FocusMode,
  RecurringEventManager,
  type CalendarEvent,
  type SearchFilters,
  type NotificationSettings,
  type FocusSession
} from '@/components/dashboard/calendar'
import { motion } from 'framer-motion'

export default function CalendarPage() {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [currentView, setCurrentView] = useState<'month' | 'week' | 'day' | 'list'>('month')
  const [searchQuery, setSearchQuery] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [events, setEvents] = useState<CalendarEvent[]>([])
  const [filters, setFilters] = useState<SearchFilters>({
    categories: [],
    priorities: [],
    dateRange: {}
  })

  // Notification settings state
  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({
    enabled: true,
    defaultReminders: [10, 60], // 10 minutes and 1 hour before
    soundEnabled: true,
    browserNotifications: false
  })

  // Focus mode state
  const [currentFocusSession, setCurrentFocusSession] = useState<FocusSession | null>(null)
  const [isNotificationBlocked, setIsNotificationBlocked] = useState(false)

  // Recurring events state
  const [showRecurringModal, setShowRecurringModal] = useState(false)
  const [selectedEventForRecurrence, setSelectedEventForRecurrence] = useState<CalendarEvent | null>(null)

  useEffect(() => {
    // Set page title
    document.title = 'التقويم | Nots'
  }, [])

  // Handle view changes with loading state
  const handleViewChange = (newView: 'month' | 'week' | 'day' | 'list') => {
    setIsLoading(true)
    setCurrentView(newView)
    // Simulate loading time for better UX
    setTimeout(() => setIsLoading(false), 300)
  }

  // Handle date changes with loading state
  const handleDateChange = (newDate: Date) => {
    setIsLoading(true)
    setCurrentDate(newDate)
    setTimeout(() => setIsLoading(false), 200)
  }

  const handleEventClick = (eventId: string) => {
    // Handle event click from sidebar
    console.log('Event clicked:', eventId)
  }

  const handleReminderAction = (id: string, action: 'complete' | 'snooze' | 'dismiss') => {
    // Handle reminder actions
    console.log('Reminder action:', id, action)
  }

  const handleSuggestionApply = (suggestion: any) => {
    // Handle applying AI suggestions
    console.log('Suggestion applied:', suggestion)
  }

  // Focus mode handlers
  const handleFocusSessionStart = (session: FocusSession) => {
    setCurrentFocusSession(session)
    console.log('Focus session started:', session)
  }

  const handleFocusSessionEnd = (session: FocusSession) => {
    setCurrentFocusSession(null)
    console.log('Focus session ended:', session)
  }

  const handleNotificationBlock = (blocked: boolean) => {
    setIsNotificationBlocked(blocked)
  }

  // Event management handlers
  const handleEventSave = (event: CalendarEvent) => {
    setEvents(prevEvents => {
      const existingIndex = prevEvents.findIndex(e => e.id === event.id)
      if (existingIndex >= 0) {
        // Update existing event
        const newEvents = [...prevEvents]
        newEvents[existingIndex] = event
        return newEvents
      } else {
        // Add new event
        return [...prevEvents, event]
      }
    })
  }

  const handleEventDelete = (eventId: string) => {
    setEvents(prevEvents => prevEvents.filter(e => e.id !== eventId))
  }

  // Recurring events handlers
  const handleRecurrenceApply = (rule: any) => {
    if (selectedEventForRecurrence) {
      // Apply recurrence rule to selected event
      console.log('Applying recurrence rule:', rule, 'to event:', selectedEventForRecurrence)
      // Here you would implement the logic to create recurring events
    }
    setShowRecurringModal(false)
    setSelectedEventForRecurrence(null)
  }

  return (
    <DashboardLayout>
      <div className="h-full w-full overflow-hidden">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="h-full flex flex-col"
        >
          {/* Calendar Header */}
          <div className="flex-shrink-0 mb-6">
            <CalendarHeader
              currentDate={currentDate}
              onDateChange={handleDateChange}
              currentView={currentView}
              onViewChange={handleViewChange}
              searchQuery={searchQuery}
              onSearchChange={setSearchQuery}
              onFilterChange={setFilters}
            />
          </div>

          {/* Main Content Area */}
          <div className="flex-1 flex gap-6 min-h-0">
            {/* Calendar Main Area */}
            <div className="flex-1 flex flex-col min-h-0">
              <div className={`flex-1 bg-background rounded-xl border border-border/50 shadow-sm overflow-hidden transition-opacity duration-300 ${
                isLoading ? 'opacity-60' : 'opacity-100'
              }`}>
                <CalendarView
                  currentDate={currentDate}
                  currentView={currentView}
                  searchQuery={searchQuery}
                  filters={filters}
                  isLoading={isLoading}
                  events={events}
                  onEventsChange={setEvents}
                  onEventSave={handleEventSave}
                  onEventDelete={handleEventDelete}
                />
              </div>
            </div>

            {/* Analytics Section */}
            <div className="flex-shrink-0 mt-4">
              <CalendarAnalytics
                events={events}
                dateRange={{
                  start: new Date(currentDate.getFullYear(), currentDate.getMonth(), 1),
                  end: new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0)
                }}
              />
            </div>
          </div>

            {/* Sidebar */}
            <div className="w-80 flex-shrink-0 flex flex-col gap-4 max-h-full overflow-y-auto">
              <CalendarSidebar
                onEventClick={handleEventClick}
                onFilterChange={setFilters}
              />

              <NotificationManager
                events={events}
                settings={notificationSettings}
                onSettingsChange={setNotificationSettings}
              />

              <FocusMode
                onSessionStart={handleFocusSessionStart}
                onSessionEnd={handleFocusSessionEnd}
                onNotificationBlock={handleNotificationBlock}
              />

              <SmartReminders
                onReminderAction={handleReminderAction}
              />

              <SchedulingSuggestions
                events={events}
                onSuggestionApply={handleSuggestionApply}
              />
            </div>
          </div>
        </motion.div>
      </div>

      {/* Recurring Event Modal */}
      <RecurringEventManager
        isOpen={showRecurringModal}
        onClose={() => {
          setShowRecurringModal(false)
          setSelectedEventForRecurrence(null)
        }}
        onSave={handleRecurrenceApply}
      />
    </DashboardLayout>
  )
}
