import { useEffect, useState } from 'react'

import { DashboardLayout } from '@/components/dashboard/DashboardLayout'
import { CalendarView } from '@/components/dashboard/calendar/CalendarView'
import { CalendarHeader } from '@/components/dashboard/calendar/CalendarHeader'
import { CalendarSidebar } from '@/components/dashboard/calendar/CalendarSidebar'
import { SmartReminders } from '@/components/dashboard/calendar/SmartReminders'
import { SchedulingSuggestions } from '@/components/dashboard/calendar/SchedulingSuggestions'
import { SearchFilters } from '@/components/dashboard/calendar/CalendarSearch'
import { motion } from 'framer-motion'

export default function CalendarPage() {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [currentView, setCurrentView] = useState<'month' | 'week' | 'day' | 'list'>('month')
  const [searchQuery, setSearchQuery] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [filters, setFilters] = useState<SearchFilters>({
    categories: [],
    priorities: [],
    dateRange: {}
  })

  useEffect(() => {
    // Set page title
    document.title = 'التقويم | Nots'
  }, [])

  // Handle view changes with loading state
  const handleViewChange = (newView: 'month' | 'week' | 'day' | 'list') => {
    setIsLoading(true)
    setCurrentView(newView)
    // Simulate loading time for better UX
    setTimeout(() => setIsLoading(false), 300)
  }

  // Handle date changes with loading state
  const handleDateChange = (newDate: Date) => {
    setIsLoading(true)
    setCurrentDate(newDate)
    setTimeout(() => setIsLoading(false), 200)
  }

  const handleEventClick = (eventId: string) => {
    // Handle event click from sidebar
    console.log('Event clicked:', eventId)
  }

  const handleReminderAction = (id: string, action: 'complete' | 'snooze' | 'dismiss') => {
    // Handle reminder actions
    console.log('Reminder action:', id, action)
  }

  const handleSuggestionApply = (suggestion: any) => {
    // Handle applying AI suggestions
    console.log('Suggestion applied:', suggestion)
  }

  return (
    <DashboardLayout>
      <div className="h-full w-full overflow-hidden">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="h-full flex flex-col"
        >
          {/* Calendar Header */}
          <div className="flex-shrink-0 mb-6">
            <CalendarHeader
              currentDate={currentDate}
              onDateChange={handleDateChange}
              currentView={currentView}
              onViewChange={handleViewChange}
              searchQuery={searchQuery}
              onSearchChange={setSearchQuery}
              onFilterChange={setFilters}
            />
          </div>

          {/* Main Content Area */}
          <div className="flex-1 flex gap-6 min-h-0">
            {/* Calendar Main Area */}
            <div className="flex-1 flex flex-col min-h-0">
              <div className={`flex-1 bg-background rounded-xl border border-border/50 shadow-sm overflow-hidden transition-opacity duration-300 ${
                isLoading ? 'opacity-60' : 'opacity-100'
              }`}>
                <CalendarView
                  currentDate={currentDate}
                  currentView={currentView}
                  searchQuery={searchQuery}
                  filters={filters}
                  isLoading={isLoading}
                />
              </div>
            </div>

            {/* Sidebar */}
            <div className="w-80 flex-shrink-0 flex flex-col gap-4 max-h-full overflow-y-auto">
              <CalendarSidebar
                onEventClick={handleEventClick}
                onFilterChange={setFilters}
              />
              <SmartReminders
                onReminderAction={handleReminderAction}
              />
              <SchedulingSuggestions
                onSuggestionApply={handleSuggestionApply}
              />
            </div>
          </div>
        </motion.div>
      </div>
    </DashboardLayout>
  )
}
