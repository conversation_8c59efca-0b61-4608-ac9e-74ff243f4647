// pages/dashboard/Calendar.tsx
import { useEffect, useState } from 'react'
import {
  CalendarView,
  CalendarHeader,
  CalendarSidebar,
  SmartReminders,
  SchedulingSuggestions,
  NotificationManager,
  CalendarAnalytics,
  FocusMode,
  RecurringEventManager,
  type CalendarEvent,
  type SearchFilters,
  type NotificationSettings,
  type FocusSession
} from '@/components/dashboard/calendar'
import { motion } from 'framer-motion'
import { DashboardLayout } from '@/components/dashboard/DashboardLayout'

export default function CalendarPage() {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [currentView, setCurrentView] = useState<'month' | 'week' | 'day' | 'list'>('month')
  const [searchQuery, setSearchQuery] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [events, setEvents] = useState<CalendarEvent[]>([])
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [filters, setFilters] = useState<SearchFilters>({
    categories: [],
    priorities: [],
    dateRange: {}
  })

  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({
    enabled: true,
    defaultReminders: [10, 60],
    soundEnabled: true,
    browserNotifications: false
  })

  const [currentFocusSession, setCurrentFocusSession] = useState<FocusSession | null>(null)
  const [isNotificationBlocked, setIsNotificationBlocked] = useState(false)

  const [showRecurringModal, setShowRecurringModal] = useState(false)
  const [selectedEventForRecurrence, setSelectedEventForRecurrence] = useState<CalendarEvent | null>(null)

  useEffect(() => {
    document.title = 'التقويم | Nots'
  }, [])

  const handleViewChange = (newView: 'month' | 'week' | 'day' | 'list') => {
    setIsLoading(true)
    setCurrentView(newView)
    setTimeout(() => setIsLoading(false), 300)
  }

  const handleDateChange = (newDate: Date) => {
    setIsLoading(true)
    setCurrentDate(newDate)
    setTimeout(() => setIsLoading(false), 200)
  }

  const handleEventClick = (eventId: string) => {
    console.log('Event clicked:', eventId)
  }

  const handleReminderAction = (id: string, action: 'complete' | 'snooze' | 'dismiss') => {
    console.log('Reminder action:', id, action)
  }

  const handleSuggestionApply = (suggestion: any) => {
    console.log('Suggestion applied:', suggestion)
  }

  const handleFocusSessionStart = (session: FocusSession) => {
    setCurrentFocusSession(session)
    console.log('Focus session started:', session)
  }

  const handleFocusSessionEnd = (session: FocusSession) => {
    setCurrentFocusSession(null)
    console.log('Focus session ended:', session)
  }

  const handleNotificationBlock = (blocked: boolean) => {
    setIsNotificationBlocked(blocked)
  }

  const handleEventSave = (event: CalendarEvent) => {
    setEvents(prevEvents => {
      const existingIndex = prevEvents.findIndex(e => e.id === event.id)
      if (existingIndex >= 0) {
        const newEvents = [...prevEvents]
        newEvents[existingIndex] = event
        return newEvents
      } else {
        return [...prevEvents, event]
      }
    })
  }

  const handleEventDelete = (eventId: string) => {
    setEvents(prevEvents => prevEvents.filter(e => e.id !== eventId))
  }

  const handleRecurrenceApply = (rule: any) => {
    if (selectedEventForRecurrence) {
      console.log('Applying recurrence rule:', rule, 'to event:', selectedEventForRecurrence)
    }
    setShowRecurringModal(false)
    setSelectedEventForRecurrence(null)
  }

  // Fullscreen Calendar Component
  const FullscreenCalendar = () => (
    <div className="fixed inset-0 z-50 bg-background">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        transition={{ duration: 0.3 }}
        className="h-full flex flex-col"
      >
        {/* Fullscreen Header */}
        <div className="flex-shrink-0 p-6 border-b border-border/30">
          <CalendarHeader
            currentDate={currentDate}
            onDateChange={handleDateChange}
            currentView={currentView}
            onViewChange={handleViewChange}
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
            onFilterChange={setFilters}
            isFullscreen={true}
            onFullscreenToggle={() => setIsFullscreen(false)}
          />
        </div>

        {/* Fullscreen Calendar View */}
        <div className="flex-1 p-6 overflow-hidden">
          <div className="h-full bg-background rounded-xl border border-border/50 shadow-sm overflow-hidden">
            <CalendarView
              currentDate={currentDate}
              currentView={currentView}
              searchQuery={searchQuery}
              filters={filters}
              isLoading={isLoading}
              events={events}
              onEventsChange={setEvents}
              onEventSave={handleEventSave}
              onEventDelete={handleEventDelete}
            />
          </div>
        </div>
      </motion.div>
    </div>
  )

  // Normal Calendar Layout
  if (isFullscreen) {
    return <FullscreenCalendar />
  }

  return (
    <DashboardLayout>
      <div className="h-full w-full overflow-hidden">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="h-full flex flex-col"
        >
        {/* Header */}
        <div className="flex-shrink-0 mb-6">
          <CalendarHeader
            currentDate={currentDate}
            onDateChange={handleDateChange}
            currentView={currentView}
            onViewChange={handleViewChange}
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
            onFilterChange={setFilters}
            isFullscreen={false}
            onFullscreenToggle={() => setIsFullscreen(true)}
          />
        </div>

        {/* Content */}
        <div className="flex-1 flex gap-6 min-h-0">
          <div className="flex-1 flex flex-col min-h-0">
            <div className={`flex-1 bg-background rounded-xl border border-border/50 shadow-sm overflow-hidden transition-opacity duration-300 ${
              isLoading ? 'opacity-60' : 'opacity-100'
            }`}>
              <CalendarView
                currentDate={currentDate}
                currentView={currentView}
                searchQuery={searchQuery}
                filters={filters}
                isLoading={isLoading}
                events={events}
                onEventsChange={setEvents}
                onEventSave={handleEventSave}
                onEventDelete={handleEventDelete}
              />
            </div>
          </div>

          <div className="flex-shrink-0 mt-4">
            <CalendarAnalytics
              events={events}
              dateRange={{
                start: new Date(currentDate.getFullYear(), currentDate.getMonth(), 1),
                end: new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0)
              }}
            />
          </div>
        </div>

        {/* Enhanced Sidebar with Better Organization */}
        <div className="w-80 flex-shrink-0 flex flex-col gap-6 max-h-full overflow-y-auto pr-2">
          {/* Primary Navigation Section */}
          <div className="space-y-4">
            <CalendarSidebar
              onEventClick={handleEventClick}
              onFilterChange={setFilters}
            />
          </div>

          {/* Productivity Tools Section */}
          <div className="space-y-4 border-t border-border/30 pt-4">
            <div className="px-2">
              <h3 className="text-sm font-semibold text-muted-foreground mb-3 uppercase tracking-wide">
                أدوات الإنتاجية
              </h3>
            </div>

            <NotificationManager
              events={events}
              settings={notificationSettings}
              onSettingsChange={setNotificationSettings}
            />

            <FocusMode
              onSessionStart={handleFocusSessionStart}
              onSessionEnd={handleFocusSessionEnd}
              onNotificationBlock={handleNotificationBlock}
            />
          </div>

          {/* AI Assistance Section */}
          <div className="space-y-4 border-t border-border/30 pt-4">
            <div className="px-2">
              <h3 className="text-sm font-semibold text-muted-foreground mb-3 uppercase tracking-wide">
                المساعد الذكي
              </h3>
            </div>

            <SmartReminders
              onReminderAction={handleReminderAction}
            />

            <SchedulingSuggestions
              events={events}
              onSuggestionApply={handleSuggestionApply}
            />
          </div>
        </div>
      </motion.div>

      <RecurringEventManager
        isOpen={showRecurringModal}
        onClose={() => {
          setShowRecurringModal(false)
          setSelectedEventForRecurrence(null)
        }}
        onSave={handleRecurrenceApply}
      />
      </div>
    </DashboardLayout>
  )
}


