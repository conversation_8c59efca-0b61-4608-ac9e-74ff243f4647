import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { CategorySelector } from "./CategorySelector"
import { TagsManager } from "./TagsManager"
import { 
  ChevronLeft,
  ChevronRight,
  Calendar,
  Clock,
  BookOpen,
  FileText,
  Download,
  History,
  BarChart3,
  Eye,
  EyeOff
} from "lucide-react"
import { formatDistanceToNow } from "date-fns"
import { ar } from "date-fns/locale"

interface EditorSidebarProps {
  isCollapsed: boolean
  onToggle: () => void
  category?: string
  categoryColor?: string
  tags: string[]
  wordCount: number
  lastSaved: string
  onCategoryChange: (category: string, color: string) => void
  onTagsChange: (tags: string[]) => void
}

export function EditorSidebar({
  isCollapsed,
  onToggle,
  category,
  categoryColor,
  tags,
  wordCount,
  lastSaved,
  onCategoryChange,
  onTagsChange
}: EditorSidebarProps) {
  const [activeSection, setActiveSection] = useState<string>('metadata')

  const formatDate = (dateString?: string): string => {
    if (!dateString) return 'لم يتم الحفظ بعد'
    try {
      const date = new Date(dateString)
      if (isNaN(date.getTime())) return 'منذ وقت قريب'
      return formatDistanceToNow(date, {
        addSuffix: true,
        locale: ar
      })
    } catch {
      return 'منذ وقت قريب'
    }
  }

  const getReadingTime = (words: number): string => {
    const minutes = Math.max(1, Math.ceil(words / 200))
    if (minutes === 1) return 'دقيقة واحدة'
    if (minutes === 2) return 'دقيقتان'
    if (minutes <= 10) return `${minutes} دقائق`
    return `${minutes} دقيقة`
  }

  const sidebarWidth = isCollapsed ? 'w-16' : 'w-80'

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ 
        opacity: 1, 
        x: 0,
        width: isCollapsed ? 64 : 320
      }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
      className={`
        ${sidebarWidth} h-full bg-background/95 backdrop-blur-md border-r border-border/50 shadow-xl
        overflow-hidden flex-shrink-0 relative
      `}
    >
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-muted/30 via-accent/10 to-background" />
      
      {/* Animated Background Shapes */}
      <motion.div
        animate={{
          rotate: 360,
          scale: [1, 1.1, 1],
        }}
        transition={{
          rotate: { duration: 20, repeat: Infinity, ease: "linear" },
          scale: { duration: 8, repeat: Infinity, ease: "easeInOut" },
        }}
        className="absolute top-10 left-10 w-16 h-16 rounded-full bg-secondary/10 blur-xl"
      />

      <div className="relative z-10 h-full flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-border/30 bg-gradient-to-r from-secondary/5 to-accent/5">
          <div className="flex items-center justify-between">
            {!isCollapsed && (
              <motion.h3
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                className="text-lg font-semibold text-foreground"
              >
                تفاصيل الملاحظة
              </motion.h3>
            )}
            
            <Button
              onClick={onToggle}
              variant="ghost"
              size="icon"
              className="w-8 h-8 hover:bg-muted/50 rounded-lg"
            >
              {isCollapsed ? (
                <ChevronLeft className="w-4 h-4" />
              ) : (
                <ChevronRight className="w-4 h-4" />
              )}
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          <AnimatePresence>
            {!isCollapsed && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.3 }}
                className="p-4 space-y-6"
              >
                {/* Statistics */}
                <div className="space-y-4">
                  <h4 className="text-sm font-semibold text-foreground flex items-center space-x-2 space-x-reverse">
                    <BarChart3 className="w-4 h-4" />
                    <span>إحصائيات</span>
                  </h4>
                  
                  <div className="grid grid-cols-2 gap-3">
                    <div className="p-3 bg-muted/30 rounded-xl border border-border/30 text-center">
                      <div className="text-lg font-bold text-primary">{wordCount}</div>
                      <div className="text-xs text-muted-foreground">كلمة</div>
                    </div>
                    
                    <div className="p-3 bg-muted/30 rounded-xl border border-border/30 text-center">
                      <div className="text-lg font-bold text-secondary">{getReadingTime(wordCount)}</div>
                      <div className="text-xs text-muted-foreground">للقراءة</div>
                    </div>
                  </div>

                  <div className="space-y-2 text-sm">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2 space-x-reverse text-muted-foreground">
                        <Calendar className="w-4 h-4" />
                        <span>آخر حفظ:</span>
                      </div>
                      <span className="text-foreground">{lastSaved || 'لم يتم الحفظ بعد'}</span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2 space-x-reverse text-muted-foreground">
                        <Clock className="w-4 h-4" />
                        <span>وقت القراءة:</span>
                      </div>
                      <span className="text-foreground">{getReadingTime(wordCount)}</span>
                    </div>
                  </div>
                </div>

                <Separator />

                {/* Category Selector */}
                <CategorySelector
                  selectedCategory={category}
                  selectedCategoryColor={categoryColor}
                  onCategoryChange={onCategoryChange}
                />

                <Separator />

                {/* Tags Manager */}
                <TagsManager
                  tags={tags}
                  onTagsChange={onTagsChange}
                />

                <Separator />

                {/* Export Options */}
                <div className="space-y-4">
                  <h4 className="text-sm font-semibold text-foreground flex items-center space-x-2 space-x-reverse">
                    <Download className="w-4 h-4" />
                    <span>تصدير</span>
                  </h4>
                  
                  <div className="space-y-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full justify-start rounded-xl border-border/50"
                    >
                      <FileText className="w-4 h-4 mr-2" />
                      تصدير PDF
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full justify-start rounded-xl border-border/50"
                    >
                      <FileText className="w-4 h-4 mr-2" />
                      تصدير Word
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full justify-start rounded-xl border-border/50"
                    >
                      <FileText className="w-4 h-4 mr-2" />
                      تصدير Markdown
                    </Button>
                  </div>
                </div>

                <Separator />

                {/* Version History */}
                <div className="space-y-4">
                  <h4 className="text-sm font-semibold text-foreground flex items-center space-x-2 space-x-reverse">
                    <History className="w-4 h-4" />
                    <span>تاريخ الإصدارات</span>
                  </h4>
                  
                  <div className="space-y-2">
                    <div className="p-3 bg-muted/30 rounded-xl border border-border/30">
                      <div className="flex items-center justify-between mb-2">
                        <Badge variant="secondary" className="text-xs">
                          الحالي
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          منذ 5 دقائق
                        </span>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        تم إضافة محتوى جديد وتحديث العلامات
                      </p>
                    </div>
                    
                    <div className="p-3 bg-muted/20 rounded-xl border border-border/20">
                      <div className="flex items-center justify-between mb-2">
                        <Badge variant="outline" className="text-xs">
                          v1.2
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          منذ ساعة
                        </span>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        تم تحديث العنوان والتصنيف
                      </p>
                    </div>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full text-xs text-muted-foreground hover:text-foreground"
                    >
                      عرض جميع الإصدارات
                    </Button>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Collapsed State Icons */}
          {isCollapsed && (
            <div className="p-2 space-y-2">
              <Button
                variant="ghost"
                size="icon"
                className="w-12 h-12 mx-auto hover:bg-muted/50 rounded-xl"
                title="إحصائيات"
              >
                <BarChart3 className="w-5 h-5" />
              </Button>
              
              <Button
                variant="ghost"
                size="icon"
                className="w-12 h-12 mx-auto hover:bg-muted/50 rounded-xl"
                title="التصنيف"
              >
                <BookOpen className="w-5 h-5" />
              </Button>
              
              <Button
                variant="ghost"
                size="icon"
                className="w-12 h-12 mx-auto hover:bg-muted/50 rounded-xl"
                title="تصدير"
              >
                <Download className="w-5 h-5" />
              </Button>
            </div>
          )}
        </div>
      </div>
    </motion.div>
  )
}
