import { useState } from "react"
import { motion } from "framer-motion"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Link } from "react-router-dom"
import { Eye, EyeOff, Check, X, Chrome } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { PasswordStrengthMeter } from "../shared/PasswordStrengthMeter"
import { basicInfoSchema, type BasicInfoFormData } from "@/lib/validations/auth"
import { useAuthStore } from "@/stores/authStore"
import { toast } from "sonner"

interface BasicInfoStepProps {
  onNext: () => void
}

export function BasicInfoStep({ onNext }: BasicInfoStepProps) {
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const { signUpData, updateSignUpData, signInWithGoogle, isLoading } = useAuthStore()

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isValid }
  } = useForm<BasicInfoFormData>({
    resolver: zodResolver(basicInfoSchema),
    mode: "onChange",
    defaultValues: {
      fullName: signUpData.fullName,
      email: signUpData.email,
      password: signUpData.password,
      confirmPassword: signUpData.confirmPassword,
    }
  })

  const password = watch("password")
  const confirmPassword = watch("confirmPassword")

  const onSubmit = (data: BasicInfoFormData) => {
    updateSignUpData(data)
    onNext()
  }

  const handleGoogleSignUp = async () => {
    const result = await signInWithGoogle()

    if (!result.success) {
      toast.error("فشل في التسجيل مع Google", {
        description: result.error || "يرجى المحاولة مرة أخرى"
      })
    }
    // Success will be handled by the OAuth callback
  }

  const passwordsMatch = password && confirmPassword && password === confirmPassword

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Full Name Field */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="space-y-2"
      >
        <Label htmlFor="fullName" className="text-sm font-medium text-foreground">
          الاسم الكامل *
        </Label>
        <Input
          id="fullName"
          type="text"
          placeholder="أدخل اسمك الكامل"
          className={`transition-all duration-200 ${
            errors.fullName ? 'border-red-500 focus:border-red-500' : ''
          }`}
          {...register("fullName")}
        />
        {errors.fullName && (
          <motion.p
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-sm text-red-500 flex items-center space-x-1 space-x-reverse"
          >
            <X className="w-4 h-4" />
            <span>{errors.fullName.message}</span>
          </motion.p>
        )}
      </motion.div>

      {/* Email Field */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="space-y-2"
      >
        <Label htmlFor="email" className="text-sm font-medium text-foreground">
          البريد الإلكتروني *
        </Label>
        <Input
          id="email"
          type="email"
          placeholder="<EMAIL>"
          className={`transition-all duration-200 ${
            errors.email ? 'border-red-500 focus:border-red-500' : ''
          }`}
          {...register("email")}
        />
        {errors.email && (
          <motion.p
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-sm text-red-500 flex items-center space-x-1 space-x-reverse"
          >
            <X className="w-4 h-4" />
            <span>{errors.email.message}</span>
          </motion.p>
        )}
      </motion.div>

      {/* Password Field */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        className="space-y-2"
      >
        <Label htmlFor="password" className="text-sm font-medium text-foreground">
          كلمة المرور *
        </Label>
        <div className="relative">
          <Input
            id="password"
            type={showPassword ? "text" : "password"}
            placeholder="أدخل كلمة مرور قوية"
            className={`pr-10 transition-all duration-200 ${
              errors.password ? 'border-red-500 focus:border-red-500' : ''
            }`}
            {...register("password")}
          />
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
          >
            {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
          </button>
        </div>
        {errors.password && (
          <motion.p
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-sm text-red-500 flex items-center space-x-1 space-x-reverse"
          >
            <X className="w-4 h-4" />
            <span>{errors.password.message}</span>
          </motion.p>
        )}
        <PasswordStrengthMeter password={password || ""} />
      </motion.div>

      {/* Confirm Password Field */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
        className="space-y-2"
      >
        <Label htmlFor="confirmPassword" className="text-sm font-medium text-foreground">
          تأكيد كلمة المرور *
        </Label>
        <div className="relative">
          <Input
            id="confirmPassword"
            type={showConfirmPassword ? "text" : "password"}
            placeholder="أعد إدخال كلمة المرور"
            className={`pr-10 transition-all duration-200 ${
              errors.confirmPassword ? 'border-red-500 focus:border-red-500' : 
              passwordsMatch ? 'border-green-500 focus:border-green-500' : ''
            }`}
            {...register("confirmPassword")}
          />
          <button
            type="button"
            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
          >
            {showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
          </button>
          {confirmPassword && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              {passwordsMatch ? (
                <Check className="w-4 h-4 text-green-500" />
              ) : (
                <X className="w-4 h-4 text-red-500" />
              )}
            </div>
          )}
        </div>
        {errors.confirmPassword && (
          <motion.p
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-sm text-red-500 flex items-center space-x-1 space-x-reverse"
          >
            <X className="w-4 h-4" />
            <span>{errors.confirmPassword.message}</span>
          </motion.p>
        )}
        {passwordsMatch && (
          <motion.p
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-sm text-green-600 flex items-center space-x-1 space-x-reverse"
          >
            <Check className="w-4 h-4" />
            <span>كلمات المرور متطابقة</span>
          </motion.p>
        )}
      </motion.div>

      {/* Submit Button */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.5 }}
      >
        <Button
          type="submit"
          disabled={!isValid || isLoading}
          className="w-full bg-gradient-to-r from-primary to-secondary hover:opacity-90 text-primary-foreground py-3 text-lg font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? (
            <div className="flex items-center space-x-2 space-x-reverse">
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              <span>جاري المعالجة...</span>
            </div>
          ) : (
            'التالي'
          )}
        </Button>
      </motion.div>

      {/* Divider */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.6 }}
        className="relative"
      >
        <Separator />
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="bg-card px-2 text-sm text-muted-foreground">أو</span>
        </div>
      </motion.div>

      {/* Google OAuth Button */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.7 }}
      >
        <Button
          type="button"
          variant="outline"
          onClick={handleGoogleSignUp}
          disabled={isLoading}
          className="w-full border-2 border-border text-foreground hover:bg-muted py-3 rounded-xl transition-all duration-300 flex items-center justify-center space-x-2 space-x-reverse"
        >
          <Chrome className="w-5 h-5" />
          <span>التسجيل مع Google</span>
        </Button>
      </motion.div>

      {/* Login Link */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.8 }}
        className="text-center"
      >
        <p className="text-sm text-muted-foreground">
          لديك حساب بالفعل؟{' '}
          <Link
            to="/auth/login"
            className="text-primary hover:text-primary/80 font-medium transition-colors duration-200 underline-offset-4 hover:underline"
          >
            تسجيل الدخول
          </Link>
        </p>
      </motion.div>
    </form>
  )
}
