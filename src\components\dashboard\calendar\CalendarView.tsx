import { useState, useRef, useEffect } from 'react'
import FullCalendar from '@fullcalendar/react'
import dayGridPlugin from '@fullcalendar/daygrid'
import timeGridPlugin from '@fullcalendar/timegrid'
import listPlugin from '@fullcalendar/list'
import interactionPlugin from '@fullcalendar/interaction'
import { EventModal } from './EventModal'
import { motion } from 'framer-motion'
import { useTheme } from '@/components/theme/ThemeProvider'
import './calendar-styles.css'

export interface CalendarEvent {
  id: string
  title: string
  content: string
  start: Date
  end: Date
  category: string
  priority: 'high' | 'medium' | 'low'
  color: string
  isCompleted: boolean
}

export interface CalendarViewProps {
  currentView?: 'month' | 'week' | 'day' | 'list'
  currentDate?: Date
  searchQuery?: string
  isLoading?: boolean
  filters?: {
    categories: string[]
    priorities: string[]
  }
}

export function CalendarView({
  currentView = 'month',
  currentDate = new Date(),
  searchQuery = '',
  isLoading = false,
  filters
}: CalendarViewProps) {
  const { theme } = useTheme()
  const calendarRef = useRef<FullCalendar>(null)
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(null)
  const [isEventModalOpen, setIsEventModalOpen] = useState(false)
  const [events, setEvents] = useState<CalendarEvent[]>([])

  // Mock data - replace with real data later
  useEffect(() => {
    const mockEvents: CalendarEvent[] = [
      {
        id: '1',
        title: 'اجتماع فريق العمل',
        content: 'مناقشة خطة المشروع الجديد والمهام المطلوبة',
        start: new Date(2024, 11, 15, 10, 0), // December 2024
        end: new Date(2024, 11, 15, 11, 30),
        category: 'عمل',
        priority: 'high',
        color: '#ef4444',
        isCompleted: false
      },
      {
        id: '2',
        title: 'موعد طبي',
        content: 'فحص دوري مع الطبيب',
        start: new Date(2024, 11, 18, 14, 0), // December 2024
        end: new Date(2024, 11, 18, 15, 0),
        category: 'صحة',
        priority: 'medium',
        color: '#22c55e',
        isCompleted: false
      },
      {
        id: '3',
        title: 'مراجعة الدروس',
        content: 'مراجعة مادة الرياضيات للامتحان',
        start: new Date(2024, 11, 20, 16, 0), // December 2024
        end: new Date(2024, 11, 20, 18, 0),
        category: 'دراسة',
        priority: 'high',
        color: '#3b82f6',
        isCompleted: false
      }
    ]
    setEvents(mockEvents)
  }, [])

  // Update calendar view when currentView changes
  useEffect(() => {
    if (calendarRef.current) {
      const calendarApi = calendarRef.current.getApi()
      let newView = 'dayGridMonth'

      switch (currentView) {
        case 'month':
          newView = 'dayGridMonth'
          break
        case 'week':
          newView = 'timeGridWeek'
          break
        case 'day':
          newView = 'timeGridDay'
          break
        case 'list':
          newView = 'listWeek'
          break
      }

      calendarApi.changeView(newView)
    }
  }, [currentView])

  // Update calendar date when currentDate changes
  useEffect(() => {
    if (calendarRef.current) {
      const calendarApi = calendarRef.current.getApi()
      calendarApi.gotoDate(currentDate)
    }
  }, [currentDate])

  const handleEventClick = (clickInfo: any) => {
    const event = events.find(e => e.id === clickInfo.event.id)
    if (event) {
      setSelectedEvent(event)
      setIsEventModalOpen(true)
    }
  }

  const handleDateClick = (dateClickInfo: any) => {
    const newEvent: CalendarEvent = {
      id: Date.now().toString(),
      title: 'حدث جديد',
      content: '',
      start: dateClickInfo.date,
      end: new Date(dateClickInfo.date.getTime() + 60 * 60 * 1000), // 1 hour later
      category: 'شخصي',
      priority: 'medium',
      color: '#6366f1',
      isCompleted: false
    }
    setSelectedEvent(newEvent)
    setIsEventModalOpen(true)
  }

  const handleEventDrop = (dropInfo: any) => {
    const eventId = dropInfo.event.id
    const newStart = dropInfo.event.start
    const newEnd = dropInfo.event.end

    setEvents(prevEvents =>
      prevEvents.map(event =>
        event.id === eventId
          ? { ...event, start: newStart, end: newEnd }
          : event
      )
    )
  }

  const handleEventSave = (eventData: CalendarEvent) => {
    if (events.find(e => e.id === eventData.id)) {
      // Update existing event
      setEvents(prevEvents =>
        prevEvents.map(event =>
          event.id === eventData.id ? eventData : event
        )
      )
    } else {
      // Add new event
      setEvents(prevEvents => [...prevEvents, eventData])
    }
    setIsEventModalOpen(false)
    setSelectedEvent(null)
  }

  const handleEventDelete = (eventId: string) => {
    setEvents(prevEvents => prevEvents.filter(event => event.id !== eventId))
    setIsEventModalOpen(false)
    setSelectedEvent(null)
  }

  const filteredEvents = events.filter(event => {
    // Search filter
    if (searchQuery && !event.title.toLowerCase().includes(searchQuery.toLowerCase()) &&
        !event.content.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false
    }

    // Category filter
    if (filters?.categories.length && !filters.categories.includes(event.category)) {
      return false
    }

    // Priority filter
    if (filters?.priorities.length && !filters.priorities.includes(event.priority)) {
      return false
    }

    return true
  })

  const calendarEvents = filteredEvents.map(event => ({
    id: event.id,
    title: event.title,
    start: event.start,
    end: event.end,
    backgroundColor: event.color,
    borderColor: event.color,
    textColor: '#ffffff',
    extendedProps: {
      category: event.category,
      priority: event.priority,
      content: event.content,
      isCompleted: event.isCompleted
    }
  }))

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="h-full flex flex-col"
    >
      <div className="flex-1 p-6">
        <FullCalendar
          ref={calendarRef}
          plugins={[dayGridPlugin, timeGridPlugin, listPlugin, interactionPlugin]}
          initialView={
            currentView === 'month' ? 'dayGridMonth' :
            currentView === 'week' ? 'timeGridWeek' :
            currentView === 'day' ? 'timeGridDay' :
            'listWeek'
          }
          initialDate={currentDate}
          headerToolbar={false}
          events={calendarEvents}
          eventClick={handleEventClick}
          dateClick={handleDateClick}
          eventDrop={handleEventDrop}
          editable={true}
          droppable={true}
          selectable={true}
          selectMirror={true}
          dayMaxEvents={3}
          weekends={true}
          locale="ar"
          direction="rtl"
          height="100%"
          eventDisplay="block"
          eventTextColor="#ffffff"
          eventBorderWidth={0}
          eventClassNames="rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer"
          dayHeaderClassNames="text-sm font-semibold text-foreground p-3 bg-muted/30"
          dayCellClassNames="border-border/20 hover:bg-muted/10 transition-colors min-h-[120px]"
          nowIndicator={true}
          slotMinTime="06:00:00"
          slotMaxTime="22:00:00"
          allDaySlot={true}
          expandRows={true}
          stickyHeaderDates={true}
          eventMouseEnter={(info) => {
            info.el.style.transform = 'scale(1.02)'
            info.el.style.zIndex = '10'
            info.el.style.transition = 'all 0.2s ease'
          }}
          eventMouseLeave={(info) => {
            info.el.style.transform = 'scale(1)'
            info.el.style.zIndex = '1'
            info.el.style.transition = 'all 0.2s ease'
          }}
          dayHeaderContent={(args) => {
            return {
              html: `<div class="text-center font-medium">${args.text}</div>`
            }
          }}
          eventContent={(args) => {
            return {
              html: `
                <div class="p-2 rounded-md text-white text-sm font-medium">
                  <div class="truncate">${args.event.title}</div>
                  ${args.event.extendedProps.category ? `<div class="text-xs opacity-80 mt-1">${args.event.extendedProps.category}</div>` : ''}
                </div>
              `
            }
          }}
        />
      </div>

      <EventModal
        isOpen={isEventModalOpen}
        onClose={() => {
          setIsEventModalOpen(false)
          setSelectedEvent(null)
        }}
        event={selectedEvent}
        onSave={handleEventSave}
        onDelete={handleEventDelete}
      />
    </motion.div>
  )
}
