import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { TiptapEditor } from './TiptapEditor'
import { TiptapToolbar } from './TiptapToolbar'
import { ImageUploadCard } from './ImageUploadCard'
import { LinkInsertCard } from './LinkInsertCard'
import { useEditor } from '@tiptap/react'
import { StarterKit } from '@tiptap/starter-kit'
import { Bold } from '@tiptap/extension-bold'
import { Italic } from '@tiptap/extension-italic'
import { Underline } from '@tiptap/extension-underline'
import { Code } from '@tiptap/extension-code'
import { Link } from '@tiptap/extension-link'
import { Image } from '@tiptap/extension-image'
import { Table } from '@tiptap/extension-table'
import { TableRow } from '@tiptap/extension-table-row'
import { TableCell } from '@tiptap/extension-table-cell'
import { TableHeader } from '@tiptap/extension-table-header'
import { TaskList } from '@tiptap/extension-task-list'
import { TaskItem } from '@tiptap/extension-task-item'
import { Highlight } from '@tiptap/extension-highlight'
import { Blockquote } from '@tiptap/extension-blockquote'
import { BulletList } from '@tiptap/extension-bullet-list'
import { OrderedList } from '@tiptap/extension-ordered-list'
import { Heading } from '@tiptap/extension-heading'
import { Placeholder } from '@tiptap/extension-placeholder'
import { TextAlign } from '@tiptap/extension-text-align'

// Import Editor.js slash command components
import EditorJS from '@editorjs/editorjs'
import Header from '@editorjs/header'
import List from '@editorjs/list'
import Quote from '@editorjs/quote'
import Code as EditorJSCode from '@editorjs/code'
import Table as EditorJSTable from '@editorjs/table'
import Delimiter from '@editorjs/delimiter'
import Warning from '@editorjs/warning'
import Checklist from '@editorjs/checklist'
import Embed from '@editorjs/embed'
import Marker from '@editorjs/marker'

interface HybridEditorProps {
  content: string
  onContentChange: (content: string) => void
  isRTL?: boolean
  placeholder?: string
}

interface SlashMenuPosition {
  x: number
  y: number
}

export function HybridEditor({ 
  content, 
  onContentChange, 
  isRTL = true,
  placeholder = 'ابدأ الكتابة هنا... (اكتب / لفتح قائمة الأوامر)'
}: HybridEditorProps) {
  const [showImageUpload, setShowImageUpload] = useState(false)
  const [showLinkInsert, setShowLinkInsert] = useState(false)
  const [showSlashMenu, setShowSlashMenu] = useState(false)
  const [slashMenuPosition, setSlashMenuPosition] = useState<SlashMenuPosition>({ x: 0, y: 0 })
  const [selectedText, setSelectedText] = useState('')
  
  const editorRef = useRef<HTMLDivElement>(null)
  const slashMenuRef = useRef<HTMLDivElement>(null)

  // Create Tiptap editor instance
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        bold: false,
        italic: false,
        code: false,
        blockquote: false,
        bulletList: false,
        orderedList: false,
        heading: false,
      }),
      Bold,
      Italic,
      Underline,
      Code,
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-primary hover:text-primary/80 underline cursor-pointer',
        },
      }),
      Image.configure({
        HTMLAttributes: {
          class: 'max-w-full h-auto rounded-lg shadow-sm',
        },
      }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableHeader,
      TableCell,
      TaskList,
      TaskItem.configure({
        nested: true,
      }),
      Highlight,
      Blockquote,
      BulletList,
      OrderedList,
      Heading.configure({
        levels: [1, 2, 3, 4, 5, 6],
      }),
      Placeholder.configure({
        placeholder,
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
        alignments: ['left', 'center', 'right'],
        defaultAlignment: isRTL ? 'right' : 'left',
      }),
    ],
    content,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML()
      onContentChange(html)
    },
    onSelectionUpdate: ({ editor }) => {
      const { from, to } = editor.state.selection
      const text = editor.state.doc.textBetween(from, to, '')
      setSelectedText(text)
    },
    editorProps: {
      handleKeyDown: (view, event) => {
        // Handle slash command trigger
        if (event.key === '/' && !showSlashMenu) {
          const { selection } = view.state
          const { $from } = selection
          
          // Get cursor position
          const coords = view.coordsAtPos($from.pos)
          const editorRect = editorRef.current?.getBoundingClientRect()
          
          if (editorRect) {
            setSlashMenuPosition({
              x: coords.left - editorRect.left + 300, // Position to the right
              y: coords.top - editorRect.top
            })
            setShowSlashMenu(true)
          }
          
          return false
        }
        
        // Hide slash menu on escape
        if (event.key === 'Escape' && showSlashMenu) {
          setShowSlashMenu(false)
          return true
        }
        
        return false
      },
    },
  })

  // Handle clicking outside slash menu
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (slashMenuRef.current && !slashMenuRef.current.contains(event.target as Node)) {
        setShowSlashMenu(false)
      }
    }

    if (showSlashMenu) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showSlashMenu])

  // Slash command handlers
  const handleSlashCommand = (command: string) => {
    if (!editor) return

    // Remove the slash character first
    const { selection } = editor.state
    const { $from } = selection
    const textBefore = $from.nodeBefore?.textContent || ''
    
    if (textBefore.endsWith('/')) {
      editor.chain().focus().deleteRange({ from: $from.pos - 1, to: $from.pos }).run()
    }

    switch (command) {
      case 'heading1':
        editor.chain().focus().toggleHeading({ level: 1 }).run()
        break
      case 'heading2':
        editor.chain().focus().toggleHeading({ level: 2 }).run()
        break
      case 'heading3':
        editor.chain().focus().toggleHeading({ level: 3 }).run()
        break
      case 'bulletList':
        editor.chain().focus().toggleBulletList().run()
        break
      case 'orderedList':
        editor.chain().focus().toggleOrderedList().run()
        break
      case 'taskList':
        editor.chain().focus().toggleTaskList().run()
        break
      case 'blockquote':
        editor.chain().focus().toggleBlockquote().run()
        break
      case 'codeBlock':
        editor.chain().focus().toggleCodeBlock().run()
        break
      case 'table':
        editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run()
        break
      case 'horizontalRule':
        editor.chain().focus().setHorizontalRule().run()
        break
      case 'image':
        setShowImageUpload(true)
        break
      case 'link':
        setShowLinkInsert(true)
        break
    }
    
    setShowSlashMenu(false)
  }

  // Image upload handler
  const handleImageUpload = (file: File) => {
    // Create a temporary URL for the image
    const imageUrl = URL.createObjectURL(file)
    
    if (editor) {
      editor.chain().focus().setImage({ src: imageUrl, alt: file.name }).run()
    }
    
    setShowImageUpload(false)
  }

  // Link insert handler
  const handleLinkInsert = (href: string, text?: string) => {
    if (!editor) return

    if (text) {
      editor.chain().focus().insertContent(`<a href="${href}">${text}</a>`).run()
    } else if (selectedText) {
      editor.chain().focus().setLink({ href }).run()
    } else {
      editor.chain().focus().insertContent(`<a href="${href}">${href}</a>`).run()
    }
    
    setShowLinkInsert(false)
  }

  return (
    <div className="relative" ref={editorRef}>
      {/* Tiptap Toolbar */}
      <TiptapToolbar
        editor={editor}
        onImageClick={() => setShowImageUpload(true)}
        onLinkClick={() => setShowLinkInsert(true)}
      />

      {/* Main Editor */}
      <div className="relative">
        <TiptapEditor
          content={content}
          onContentChange={onContentChange}
          isRTL={isRTL}
          placeholder={placeholder}
        />

        {/* Slash Command Menu */}
        <AnimatePresence>
          {showSlashMenu && (
            <motion.div
              ref={slashMenuRef}
              initial={{ opacity: 0, scale: 0.95, x: -10 }}
              animate={{ opacity: 1, scale: 1, x: 0 }}
              exit={{ opacity: 0, scale: 0.95, x: -10 }}
              transition={{ duration: 0.15 }}
              className="absolute z-50 bg-background border border-border rounded-lg shadow-lg p-2 min-w-[250px]"
              style={{
                left: slashMenuPosition.x,
                top: slashMenuPosition.y,
              }}
            >
              <div className="space-y-1">
                <div className="px-2 py-1 text-xs font-medium text-muted-foreground border-b border-border mb-2">
                  أوامر التنسيق
                </div>
                
                {/* Headings */}
                <button
                  onClick={() => handleSlashCommand('heading1')}
                  className="w-full text-right px-2 py-2 text-sm hover:bg-muted rounded-md transition-colors"
                >
                  <span className="font-bold text-lg">عنوان رئيسي</span>
                </button>
                <button
                  onClick={() => handleSlashCommand('heading2')}
                  className="w-full text-right px-2 py-2 text-sm hover:bg-muted rounded-md transition-colors"
                >
                  <span className="font-semibold text-base">عنوان فرعي</span>
                </button>
                <button
                  onClick={() => handleSlashCommand('heading3')}
                  className="w-full text-right px-2 py-2 text-sm hover:bg-muted rounded-md transition-colors"
                >
                  <span className="font-medium">عنوان صغير</span>
                </button>

                {/* Lists */}
                <button
                  onClick={() => handleSlashCommand('bulletList')}
                  className="w-full text-right px-2 py-2 text-sm hover:bg-muted rounded-md transition-colors"
                >
                  • قائمة نقطية
                </button>
                <button
                  onClick={() => handleSlashCommand('orderedList')}
                  className="w-full text-right px-2 py-2 text-sm hover:bg-muted rounded-md transition-colors"
                >
                  1. قائمة مرقمة
                </button>
                <button
                  onClick={() => handleSlashCommand('taskList')}
                  className="w-full text-right px-2 py-2 text-sm hover:bg-muted rounded-md transition-colors"
                >
                  ☐ قائمة مهام
                </button>

                {/* Other elements */}
                <button
                  onClick={() => handleSlashCommand('blockquote')}
                  className="w-full text-right px-2 py-2 text-sm hover:bg-muted rounded-md transition-colors"
                >
                  " اقتباس
                </button>
                <button
                  onClick={() => handleSlashCommand('codeBlock')}
                  className="w-full text-right px-2 py-2 text-sm hover:bg-muted rounded-md transition-colors"
                >
                  &lt;/&gt; كود
                </button>
                <button
                  onClick={() => handleSlashCommand('table')}
                  className="w-full text-right px-2 py-2 text-sm hover:bg-muted rounded-md transition-colors"
                >
                  ⊞ جدول
                </button>
                <button
                  onClick={() => handleSlashCommand('horizontalRule')}
                  className="w-full text-right px-2 py-2 text-sm hover:bg-muted rounded-md transition-colors"
                >
                  ─ خط فاصل
                </button>
                <button
                  onClick={() => handleSlashCommand('image')}
                  className="w-full text-right px-2 py-2 text-sm hover:bg-muted rounded-md transition-colors"
                >
                  🖼️ صورة
                </button>
                <button
                  onClick={() => handleSlashCommand('link')}
                  className="w-full text-right px-2 py-2 text-sm hover:bg-muted rounded-md transition-colors"
                >
                  🔗 رابط
                </button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Image Upload Modal */}
      <ImageUploadCard
        isOpen={showImageUpload}
        onClose={() => setShowImageUpload(false)}
        onImageUpload={handleImageUpload}
      />

      {/* Link Insert Modal */}
      <LinkInsertCard
        isOpen={showLinkInsert}
        onClose={() => setShowLinkInsert(false)}
        onLinkInsert={handleLinkInsert}
        selectedText={selectedText}
      />
    </div>
  )
}
