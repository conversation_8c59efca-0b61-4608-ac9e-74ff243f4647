import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { TiptapToolbar } from './TiptapToolbar'
import { ImageUploadCard } from './ImageUploadCard'
import { LinkInsertCard } from './LinkInsertCard'
import { useEditor, EditorContent } from '@tiptap/react'
import { StarterKit } from '@tiptap/starter-kit'
import { Bold } from '@tiptap/extension-bold'
import { Italic } from '@tiptap/extension-italic'
import { Underline } from '@tiptap/extension-underline'
import { Code } from '@tiptap/extension-code'
import { Link } from '@tiptap/extension-link'
import { Image } from '@tiptap/extension-image'
import { Table } from '@tiptap/extension-table'
import { TableRow } from '@tiptap/extension-table-row'
import { TableCell } from '@tiptap/extension-table-cell'
import { TableHeader } from '@tiptap/extension-table-header'
import { TaskList } from '@tiptap/extension-task-list'
import { TaskItem } from '@tiptap/extension-task-item'
import { Highlight } from '@tiptap/extension-highlight'
import { Blockquote } from '@tiptap/extension-blockquote'
import { BulletList } from '@tiptap/extension-bullet-list'
import { OrderedList } from '@tiptap/extension-ordered-list'
import { Heading } from '@tiptap/extension-heading'
import { Placeholder } from '@tiptap/extension-placeholder'
import { TextAlign } from '@tiptap/extension-text-align'

interface HybridEditorProps {
  content: string
  onContentChange: (content: string) => void
  isRTL?: boolean
  placeholder?: string
}

interface SlashMenuPosition {
  x: number
  y: number
}

export function HybridEditor({ 
  content, 
  onContentChange, 
  isRTL = true,
  placeholder = 'ابدأ الكتابة هنا... (اكتب / لفتح قائمة الأوامر)'
}: HybridEditorProps) {
  const [showImageUpload, setShowImageUpload] = useState(false)
  const [showLinkInsert, setShowLinkInsert] = useState(false)
  const [showSlashMenu, setShowSlashMenu] = useState(false)
  const [slashMenuPosition, setSlashMenuPosition] = useState<SlashMenuPosition>({ x: 0, y: 0 })
  const [selectedText, setSelectedText] = useState('')
  
  const editorRef = useRef<HTMLDivElement>(null)
  const slashMenuRef = useRef<HTMLDivElement>(null)

  // Create Tiptap editor instance
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        bold: false,
        italic: false,
        code: false,
        blockquote: false,
        bulletList: false,
        orderedList: false,
        heading: false,
      }),
      Bold,
      Italic,
      Underline,
      Code,
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-primary hover:text-primary/80 underline cursor-pointer',
        },
      }),
      Image.configure({
        HTMLAttributes: {
          class: 'max-w-full h-auto rounded-lg shadow-sm',
        },
      }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableHeader,
      TableCell,
      TaskList,
      TaskItem.configure({
        nested: true,
      }),
      Highlight,
      Blockquote,
      BulletList,
      OrderedList,
      Heading.configure({
        levels: [1, 2, 3, 4, 5, 6],
      }),
      Placeholder.configure({
        placeholder,
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
        alignments: ['left', 'center', 'right'],
        defaultAlignment: isRTL ? 'right' : 'left',
      }),
    ],
    content,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML()
      onContentChange(html)
    },
    onSelectionUpdate: ({ editor }) => {
      const { from, to } = editor.state.selection
      const text = editor.state.doc.textBetween(from, to, '')
      setSelectedText(text)
    },
    editorProps: {
      handleKeyDown: (view, event) => {
        // Handle slash command trigger
        if (event.key === '/' && !showSlashMenu) {
          const { selection } = view.state
          const { $from } = selection
          
          // Get cursor position
          const coords = view.coordsAtPos($from.pos)
          const editorRect = editorRef.current?.getBoundingClientRect()
          
          if (editorRect) {
            setSlashMenuPosition({
              x: coords.left - editorRect.left + 300, // Position to the right
              y: coords.top - editorRect.top
            })
            setShowSlashMenu(true)
          }
          
          return false
        }
        
        // Hide slash menu on escape
        if (event.key === 'Escape' && showSlashMenu) {
          setShowSlashMenu(false)
          return true
        }
        
        return false
      },
    },
  })

  // Handle clicking outside slash menu
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (slashMenuRef.current && !slashMenuRef.current.contains(event.target as Node)) {
        setShowSlashMenu(false)
      }
    }

    if (showSlashMenu) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showSlashMenu])

  // Slash command handlers
  const handleSlashCommand = (command: string) => {
    if (!editor) return

    // Remove the slash character first
    const { selection } = editor.state
    const { $from } = selection
    const textBefore = $from.nodeBefore?.textContent || ''
    
    if (textBefore.endsWith('/')) {
      editor.chain().focus().deleteRange({ from: $from.pos - 1, to: $from.pos }).run()
    }

    switch (command) {
      case 'heading1':
        editor.chain().focus().toggleHeading({ level: 1 }).run()
        break
      case 'heading2':
        editor.chain().focus().toggleHeading({ level: 2 }).run()
        break
      case 'heading3':
        editor.chain().focus().toggleHeading({ level: 3 }).run()
        break
      case 'bulletList':
        editor.chain().focus().toggleBulletList().run()
        break
      case 'orderedList':
        editor.chain().focus().toggleOrderedList().run()
        break
      case 'taskList':
        editor.chain().focus().toggleTaskList().run()
        break
      case 'blockquote':
        editor.chain().focus().toggleBlockquote().run()
        break
      case 'codeBlock':
        editor.chain().focus().toggleCodeBlock().run()
        break
      case 'table':
        editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run()
        break
      case 'horizontalRule':
        editor.chain().focus().setHorizontalRule().run()
        break
      case 'image':
        setShowImageUpload(true)
        break
      case 'link':
        setShowLinkInsert(true)
        break
    }
    
    setShowSlashMenu(false)
  }

  // Image insert handler
  const handleImageInsert = (src: string, alt?: string) => {
    if (editor) {
      editor.chain().focus().setImage({ src, alt }).run()
    }

    setShowImageUpload(false)
  }

  // Link insert handler
  const handleLinkInsert = (href: string, text?: string) => {
    if (!editor) return

    if (text) {
      editor.chain().focus().insertContent(`<a href="${href}">${text}</a>`).run()
    } else if (selectedText) {
      editor.chain().focus().setLink({ href }).run()
    } else {
      editor.chain().focus().insertContent(`<a href="${href}">${href}</a>`).run()
    }
    
    setShowLinkInsert(false)
  }

  return (
    <div className="relative" ref={editorRef}>
      {/* Tiptap Toolbar */}
      <TiptapToolbar
        editor={editor}
        onImageClick={() => setShowImageUpload(true)}
        onLinkClick={() => setShowLinkInsert(true)}
      />

      {/* Main Editor */}
      <div className="relative">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="tiptap-editor-container"
          ref={editorRef}
        >
          <div className="relative bg-background rounded-xl border border-border/50 shadow-sm overflow-hidden">
            {/* Background Elements */}
            <div className="absolute inset-0 bg-gradient-to-br from-muted/10 via-accent/5 to-transparent pointer-events-none" />

            {/* Animated Background Shapes */}
            <motion.div
              animate={{
                scale: [1, 1.1, 1],
                opacity: [0.05, 0.1, 0.05]
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                ease: "easeInOut",
              }}
              className="absolute top-10 right-10 w-32 h-32 rounded-full bg-primary/10 blur-xl pointer-events-none"
            />

            <div className="relative z-10 overflow-hidden">
              <div className="p-6 focus-within:ring-2 focus-within:ring-primary/20 focus-within:border-primary/30 transition-all duration-200">
                <EditorContent editor={editor} />
              </div>
            </div>
          </div>
        </motion.div>

        {/* Slash Command Menu */}
        <AnimatePresence>
          {showSlashMenu && (
            <motion.div
              ref={slashMenuRef}
              initial={{ opacity: 0, scale: 0.95, x: -10 }}
              animate={{ opacity: 1, scale: 1, x: 0 }}
              exit={{ opacity: 0, scale: 0.95, x: -10 }}
              transition={{ duration: 0.15 }}
              className="absolute z-50 bg-background border border-border rounded-lg shadow-lg p-2 min-w-[250px]"
              style={{
                left: slashMenuPosition.x,
                top: slashMenuPosition.y,
              }}
            >
              <div className="space-y-1">
                <div className="px-2 py-1 text-xs font-medium text-muted-foreground border-b border-border mb-2">
                  أوامر التنسيق
                </div>
                
                {/* Headings */}
                <button
                  onClick={() => handleSlashCommand('heading1')}
                  className="w-full text-right px-2 py-2 text-sm hover:bg-muted rounded-md transition-colors"
                >
                  <span className="font-bold text-lg">عنوان رئيسي</span>
                </button>
                <button
                  onClick={() => handleSlashCommand('heading2')}
                  className="w-full text-right px-2 py-2 text-sm hover:bg-muted rounded-md transition-colors"
                >
                  <span className="font-semibold text-base">عنوان فرعي</span>
                </button>
                <button
                  onClick={() => handleSlashCommand('heading3')}
                  className="w-full text-right px-2 py-2 text-sm hover:bg-muted rounded-md transition-colors"
                >
                  <span className="font-medium">عنوان صغير</span>
                </button>

                {/* Lists */}
                <button
                  onClick={() => handleSlashCommand('bulletList')}
                  className="w-full text-right px-2 py-2 text-sm hover:bg-muted rounded-md transition-colors"
                >
                  • قائمة نقطية
                </button>
                <button
                  onClick={() => handleSlashCommand('orderedList')}
                  className="w-full text-right px-2 py-2 text-sm hover:bg-muted rounded-md transition-colors"
                >
                  1. قائمة مرقمة
                </button>
                <button
                  onClick={() => handleSlashCommand('taskList')}
                  className="w-full text-right px-2 py-2 text-sm hover:bg-muted rounded-md transition-colors"
                >
                  ☐ قائمة مهام
                </button>

                {/* Other elements */}
                <button
                  onClick={() => handleSlashCommand('blockquote')}
                  className="w-full text-right px-2 py-2 text-sm hover:bg-muted rounded-md transition-colors"
                >
                  " اقتباس
                </button>
                <button
                  onClick={() => handleSlashCommand('codeBlock')}
                  className="w-full text-right px-2 py-2 text-sm hover:bg-muted rounded-md transition-colors"
                >
                  &lt;/&gt; كود
                </button>
                <button
                  onClick={() => handleSlashCommand('table')}
                  className="w-full text-right px-2 py-2 text-sm hover:bg-muted rounded-md transition-colors"
                >
                  ⊞ جدول
                </button>
                <button
                  onClick={() => handleSlashCommand('horizontalRule')}
                  className="w-full text-right px-2 py-2 text-sm hover:bg-muted rounded-md transition-colors"
                >
                  ─ خط فاصل
                </button>
                <button
                  onClick={() => handleSlashCommand('image')}
                  className="w-full text-right px-2 py-2 text-sm hover:bg-muted rounded-md transition-colors"
                >
                  🖼️ صورة
                </button>
                <button
                  onClick={() => handleSlashCommand('link')}
                  className="w-full text-right px-2 py-2 text-sm hover:bg-muted rounded-md transition-colors"
                >
                  🔗 رابط
                </button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Image Upload Modal */}
      <ImageUploadCard
        isOpen={showImageUpload}
        onClose={() => setShowImageUpload(false)}
        onImageInsert={handleImageInsert}
      />

      {/* Link Insert Modal */}
      <LinkInsertCard
        isOpen={showLinkInsert}
        onClose={() => setShowLinkInsert(false)}
        onLinkInsert={handleLinkInsert}
        selectedText={selectedText}
      />

      {/* Tiptap Styles */}
      <style jsx>{`
        .tiptap-editor-container .ProseMirror {
          direction: ${isRTL ? 'rtl' : 'ltr'};
          text-align: ${isRTL ? 'right' : 'left'};
          font-family: "Tajawal", sans-serif;
          line-height: 1.6;
          color: hsl(var(--foreground));
          min-height: 500px;
          outline: none;
        }

        .tiptap-editor-container .ProseMirror.is-editor-empty:first-child::before {
          content: attr(data-placeholder);
          float: left;
          color: hsl(var(--muted-foreground));
          pointer-events: none;
          height: 0;
        }

        .tiptap-editor-container .ProseMirror h1 {
          font-size: 2rem;
          font-weight: 700;
          margin: 1.5rem 0 1rem 0;
          color: hsl(var(--foreground));
        }

        .tiptap-editor-container .ProseMirror h2 {
          font-size: 1.5rem;
          font-weight: 600;
          margin: 1.25rem 0 0.75rem 0;
          color: hsl(var(--foreground));
        }

        .tiptap-editor-container .ProseMirror h3 {
          font-size: 1.25rem;
          font-weight: 600;
          margin: 1rem 0 0.5rem 0;
          color: hsl(var(--foreground));
        }

        .tiptap-editor-container .ProseMirror p {
          margin: 0.75rem 0;
          line-height: 1.6;
        }

        .tiptap-editor-container .ProseMirror ul,
        .tiptap-editor-container .ProseMirror ol {
          padding-${isRTL ? 'right' : 'left'}: 1.5rem;
          margin: 0.75rem 0;
        }

        .tiptap-editor-container .ProseMirror li {
          margin: 0.25rem 0;
        }

        .tiptap-editor-container .ProseMirror blockquote {
          border-${isRTL ? 'right' : 'left'}: 4px solid hsl(var(--primary));
          padding-${isRTL ? 'right' : 'left'}: 1rem;
          margin: 1rem 0;
          font-style: italic;
          color: hsl(var(--muted-foreground));
        }

        .tiptap-editor-container .ProseMirror code {
          background: hsl(var(--muted));
          padding: 0.25rem 0.5rem;
          border-radius: 0.25rem;
          font-family: 'Courier New', monospace;
          font-size: 0.875rem;
        }

        .tiptap-editor-container .ProseMirror pre {
          background: hsl(var(--muted));
          border-radius: 0.5rem;
          padding: 1rem;
          margin: 1rem 0;
          overflow-x: auto;
          border: 1px solid hsl(var(--border));
        }

        .tiptap-editor-container .ProseMirror pre code {
          background: none;
          padding: 0;
          font-family: 'Courier New', monospace;
        }

        .tiptap-editor-container .ProseMirror table {
          border-collapse: collapse;
          margin: 1rem 0;
          width: 100%;
        }

        .tiptap-editor-container .ProseMirror table td,
        .tiptap-editor-container .ProseMirror table th {
          border: 1px solid hsl(var(--border));
          padding: 0.5rem;
          text-align: ${isRTL ? 'right' : 'left'};
        }

        .tiptap-editor-container .ProseMirror table th {
          background: hsl(var(--muted));
          font-weight: 600;
        }

        .tiptap-editor-container .task-list {
          list-style: none;
          padding: 0;
        }

        .tiptap-editor-container .task-item {
          display: flex;
          align-items: flex-start;
          margin: 0.25rem 0;
        }

        .tiptap-editor-container .task-item input[type="checkbox"] {
          margin-${isRTL ? 'left' : 'right'}: 0.5rem;
          margin-top: 0.125rem;
        }
      `}</style>
    </div>
  )
}
