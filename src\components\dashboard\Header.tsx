import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { <PERSON> } from "react-router-dom"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ThemeToggle } from "@/components/theme/ThemeToggle"
import { useAuthStore } from "@/stores/authStore"
import {
  Search,
  Bell,
  Settings,
  LogOut,
  User,
  Crown,
  Globe,
  ChevronDown,
  LayoutDashboard
} from "lucide-react"

interface HeaderProps {
  sidebarCollapsed: boolean
}

export function Header({ sidebarCollapsed }: HeaderProps) {
  const [showUserMenu, setShowUserMenu] = useState(false)
  const [showNotifications, setShowNotifications] = useState(false)
  const { user, logout } = useAuthStore()

  const handleLogout = async () => {
    await logout()
    setShowUserMenu(false)
  }

  const notifications = [
    {
      id: 1,
      title: "تم تلخيص ملاحظة جديدة",
      message: "ملاحظة 'اجتماع الفريق' تم تلخيصها بنجاح",
      time: "منذ 5 دقائق",
      type: "summary"
    },
    {
      id: 2,
      title: "تذكير مراجعة",
      message: "حان وقت مراجعة ملاحظات الأسبوع الماضي",
      time: "منذ ساعة",
      type: "reminder"
    },
    {
      id: 3,
      title: "ميزة جديدة متاحة",
      message: "تم إضافة ميزة البحث الصوتي الذكي",
      time: "منذ يومين",
      type: "feature"
    }
  ]

  return (
    <header className="fixed top-0 left-0 right-0 z-40 bg-background/95 backdrop-blur-md border-b border-border/50">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-r from-muted/20 via-accent/5 to-background"></div>
      
      <div 
        className="relative z-10 h-16 px-4 flex items-center justify-between transition-all duration-300"
        style={{ 
          paddingRight: sidebarCollapsed ? '100px' : '300px' 
        }}
      >
        {/* Search Bar */}
        <div className="flex-1 max-w-md">
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <Input
              placeholder="البحث في الملاحظات..."
              className="pr-10 bg-muted/30 border-border/50 focus:border-primary/50 rounded-xl"
            />
          </div>
        </div>

        {/* Right Section */}
        <div className="flex items-center space-x-4 space-x-reverse">
          {/* Language Toggle */}
          <Button
            variant="ghost"
            size="icon"
            className="hover:bg-muted/50 rounded-xl"
          >
            <Globe className="w-4 h-4" />
          </Button>

          {/* Theme Toggle */}
          <ThemeToggle />

          {/* Notifications */}
          <div className="relative">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setShowNotifications(!showNotifications)}
              className="hover:bg-muted/50 rounded-xl relative"
            >
              <Bell className="w-4 h-4" />
              <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full text-xs flex items-center justify-center text-white">
                3
              </span>
            </Button>

            <AnimatePresence>
              {showNotifications && (
                <motion.div
                  initial={{ opacity: 0, y: 10, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: 10, scale: 0.95 }}
                  className="absolute top-full right-0 mt-2 w-80 bg-background/95 backdrop-blur-md border border-border/50 rounded-xl shadow-lg py-2"
                >
                  <div className="px-4 py-3 border-b border-border/30">
                    <h3 className="font-semibold text-foreground">الإشعارات</h3>
                  </div>
                  
                  <div className="max-h-80 overflow-y-auto">
                    {notifications.map((notification) => (
                      <div
                        key={notification.id}
                        className="px-4 py-3 hover:bg-muted/30 transition-colors cursor-pointer"
                      >
                        <div className="flex items-start space-x-3 space-x-reverse">
                          <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                          <div className="flex-1">
                            <p className="font-medium text-sm text-foreground">
                              {notification.title}
                            </p>
                            <p className="text-xs text-muted-foreground mt-1">
                              {notification.message}
                            </p>
                            <p className="text-xs text-muted-foreground mt-1">
                              {notification.time}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  <div className="px-4 py-3 border-t border-border/30">
                    <Button variant="ghost" size="sm" className="w-full">
                      عرض جميع الإشعارات
                    </Button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* User Menu */}
          <div className="relative">
            <Button
              variant="ghost"
              onClick={() => setShowUserMenu(!showUserMenu)}
              className="flex items-center space-x-2 space-x-reverse p-2 hover:bg-muted/50 rounded-xl"
            >
              <div className="w-8 h-8 rounded-full bg-gradient-to-r from-primary to-secondary flex items-center justify-center text-white text-sm font-medium">
                {user?.fullName?.charAt(0) || 'U'}
              </div>
              <div className="hidden md:block text-right">
                <p className="text-sm font-medium text-foreground">
                  {user?.fullName || 'مستخدم'}
                </p>
                <p className="text-xs text-muted-foreground">
                  {user?.plan === 'free' ? 'خطة مجانية' : 'خطة مميزة'}
                </p>
              </div>
              <ChevronDown className="w-3 h-3" />
            </Button>

            <AnimatePresence>
              {showUserMenu && (
                <motion.div
                  initial={{ opacity: 0, y: 10, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: 10, scale: 0.95 }}
                  className="absolute top-full right-0 mt-2 w-56 bg-background/95 backdrop-blur-md border border-border/50 rounded-xl shadow-lg py-2"
                >
                  <div className="px-4 py-3 border-b border-border/30">
                    <p className="font-medium text-foreground">{user?.fullName}</p>
                    <p className="text-sm text-muted-foreground">{user?.email}</p>
                    <div className="flex items-center space-x-1 space-x-reverse mt-2">
                      {user?.plan === 'free' ? (
                        <User className="w-3 h-3 text-muted-foreground" />
                      ) : (
                        <Crown className="w-3 h-3 text-yellow-500" />
                      )}
                      <span className="text-xs text-primary">
                        {user?.plan === 'free' ? 'خطة مجانية' : 'خطة مميزة'}
                      </span>
                    </div>
                  </div>
                  
                  <div className="py-1">
                    <Link
                      to="/dashboard"
                      className="flex items-center space-x-2 space-x-reverse px-4 py-2 text-sm hover:bg-muted/50 transition-colors"
                      onClick={() => setShowUserMenu(false)}
                    >
                      <LayoutDashboard className="w-4 h-4" />
                      <span>لوحة التحكم</span>
                    </Link>
                    
                    <Link
                      to="/dashboard/settings"
                      className="flex items-center space-x-2 space-x-reverse px-4 py-2 text-sm hover:bg-muted/50 transition-colors"
                      onClick={() => setShowUserMenu(false)}
                    >
                      <Settings className="w-4 h-4" />
                      <span>الإعدادات</span>
                    </Link>

                    {user?.plan === 'free' && (
                      <Link
                        to="/dashboard/upgrade"
                        className="flex items-center space-x-2 space-x-reverse px-4 py-2 text-sm hover:bg-muted/50 transition-colors text-yellow-600"
                        onClick={() => setShowUserMenu(false)}
                      >
                        <Crown className="w-4 h-4" />
                        <span>ترقية الحساب</span>
                      </Link>
                    )}
                  </div>
                  
                  <div className="border-t border-border/30 pt-1">
                    <button
                      onClick={handleLogout}
                      className="w-full flex items-center space-x-2 space-x-reverse px-4 py-2 text-sm text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
                    >
                      <LogOut className="w-4 h-4" />
                      <span>تسجيل الخروج</span>
                    </button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>
    </header>
  )
}
