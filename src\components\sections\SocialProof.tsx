import { motion } from "framer-motion"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Star, Quote } from "lucide-react"

const testimonials = [
  {
    id: 1,
    name: "أحمد محمد",
    role: "طالب جامعي",
    avatar: "/avatars/ahmed.jpg",
    content: "Nots غيّر طريقة دراستي تماماً. التلخيص التلقائي يوفر عليّ ساعات من الوقت.",
    rating: 5
  },
  {
    id: 2,
    name: "فاطمة العلي",
    role: "مديرة مشاريع",
    avatar: "/avatars/fatima.jpg",
    content: "أفضل تطبيق ملاحظات استخدمته. الذكاء الاصطناعي يفهم السياق العربي بشكل مثالي.",
    rating: 5
  },
  {
    id: 3,
    name: "محم<PERSON> الخالدي",
    role: "كاتب ومدون",
    avatar: "/avatars/mohammed.jpg",
    content: "ميزة توسيع الأفكار ساعدتني في كتابة مقالات أكثر تفصيلاً وإبداعاً.",
    rating: 5
  },
  {
    id: 4,
    name: "سارة أحمد",
    role: "طبيبة",
    avatar: "/avatars/sara.jpg",
    content: "تحويل الصوت إلى نص باللغة العربية دقيق جداً. يساعدني في توثيق الحالات الطبية.",
    rating: 5
  },
  {
    id: 5,
    name: "عبدالله الشمري",
    role: "مطور برمجيات",
    avatar: "/avatars/abdullah.jpg",
    content: "التنظيم التلقائي للملاحظات والبحث الذكي يجعل العمل أكثر كفاءة.",
    rating: 5
  }
]

export function SocialProof() {
  return (
    <section className="py-20 bg-nots-bg-light/50" id="social-proof">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold nots-heading mb-4">
            <span className="text-primary">20,000+</span> مستخدم يثق بنا
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            انضم إلى آلاف المستخدمين الذين يعتمدون على Nots لتنظيم أفكارهم وملاحظاتهم
          </p>
        </motion.div>

        {/* Desktop Testimonials Grid */}
        <div className="hidden md:grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={testimonial.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full nots-shadow hover:nots-shadow-lg transition-all duration-300 border-border/50">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    <Avatar className="w-12 h-12 ml-3">
                      <AvatarImage src={testimonial.avatar} alt={testimonial.name} />
                      <AvatarFallback className="bg-gradient-purple text-white">
                        {testimonial.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h4 className="font-semibold text-foreground">{testimonial.name}</h4>
                      <p className="text-sm text-muted-foreground">{testimonial.role}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center mb-3">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 fill-nots-orange text-nots-orange" />
                    ))}
                  </div>
                  
                  <div className="relative">
                    <Quote className="absolute -top-2 -right-2 w-6 h-6 text-primary/20" />
                    <p className="text-muted-foreground leading-relaxed pr-4">
                      "{testimonial.content}"
                    </p>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Mobile Testimonials Carousel */}
        <div className="md:hidden">
          <div className="flex overflow-x-auto gap-4 pb-4 scrollbar-hide">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={testimonial.id}
                initial={{ opacity: 0, x: 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="flex-shrink-0 w-80"
              >
                <Card className="nots-shadow hover:nots-shadow-lg transition-all duration-300 border-border/50">
                  <CardContent className="p-6">
                    <div className="flex items-center mb-4">
                      <Avatar className="w-12 h-12 ml-3">
                        <AvatarImage src={testimonial.avatar} alt={testimonial.name} />
                        <AvatarFallback className="bg-gradient-purple text-white">
                          {testimonial.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <h4 className="font-semibold text-foreground">{testimonial.name}</h4>
                        <p className="text-sm text-muted-foreground">{testimonial.role}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center mb-3">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="w-4 h-4 fill-nots-orange text-nots-orange" />
                      ))}
                    </div>
                    
                    <div className="relative">
                      <Quote className="absolute -top-2 -right-2 w-6 h-6 text-primary/20" />
                      <p className="text-muted-foreground leading-relaxed pr-4">
                        "{testimonial.content}"
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Trust Badges */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
          className="flex flex-wrap justify-center items-center gap-8 mt-16 pt-8 border-t border-border/50"
        >
          <div className="text-center">
            <div className="text-2xl font-bold text-primary mb-1">4.9/5</div>
            <div className="text-sm text-muted-foreground">تقييم المستخدمين</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-primary mb-1">99.9%</div>
            <div className="text-sm text-muted-foreground">وقت التشغيل</div>
          </div>
          <div className="text-center">
            <div className="text-xl font-bold text-primary mb-1">أقل من ثانيتين</div>
            <div className="text-sm text-muted-foreground">سرعة الاستجابة</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-primary mb-1">256 بت</div>
            <div className="text-sm text-muted-foreground">تشفير البيانات</div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
