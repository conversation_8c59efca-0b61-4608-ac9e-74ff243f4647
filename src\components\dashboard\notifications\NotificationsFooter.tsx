'use client'

import { motion } from 'framer-motion'
import { 
  BookOpen, 
  Calendar, 
  Settings, 
  Bell,
  ArrowRight,
  ExternalLink,
  Zap,
  FileText
} from 'lucide-react'
import { Button } from '@/components/ui/button'

interface QuickLink {
  id: string
  title: string
  description: string
  icon: React.ReactNode
  href: string
  color: string
  bgColor: string
}

export function NotificationsFooter() {
  const quickLinks: QuickLink[] = [
    {
      id: 'notebook',
      title: 'عرض دفتر الملاحظات',
      description: 'تصفح جميع ملاحظاتك وإنشاء ملاحظات جديدة',
      icon: <BookOpen className="w-5 h-5" />,
      href: '/dashboard/notes',
      color: 'text-blue-600',
      bgColor: 'bg-blue-100 dark:bg-blue-900/30 hover:bg-blue-200 dark:hover:bg-blue-900/50'
    },
    {
      id: 'calendar',
      title: 'التقويم الكامل',
      description: 'إدارة أحداثك ومواعيدك المجدولة',
      icon: <Calendar className="w-5 h-5" />,
      href: '/dashboard/calendar',
      color: 'text-purple-600',
      bgColor: 'bg-purple-100 dark:bg-purple-900/30 hover:bg-purple-200 dark:hover:bg-purple-900/50'
    },
    {
      id: 'notifications',
      title: 'إدارة التنبيهات',
      description: 'تخصيص إعدادات التنبيهات والإشعارات',
      icon: <Bell className="w-5 h-5" />,
      href: '/dashboard/settings/notifications',
      color: 'text-green-600',
      bgColor: 'bg-green-100 dark:bg-green-900/30 hover:bg-green-200 dark:hover:bg-green-900/50'
    },
    {
      id: 'settings',
      title: 'الإعدادات',
      description: 'تخصيص تجربتك وإعدادات الحساب',
      icon: <Settings className="w-5 h-5" />,
      href: '/dashboard/settings',
      color: 'text-orange-600',
      bgColor: 'bg-orange-100 dark:bg-orange-900/30 hover:bg-orange-200 dark:hover:bg-orange-900/50'
    }
  ]

  const handleLinkClick = (href: string) => {
    // Handle navigation
    console.log(`Navigate to: ${href}`)
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 40 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.6 }}
      className="space-y-6"
    >
      {/* Section Header */}
      <div className="text-center">
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.7 }}
          className="text-2xl font-bold text-foreground mb-2"
        >
          روابط سريعة
        </motion.h2>
        <motion.p
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.8 }}
          className="text-muted-foreground"
        >
          انتقل بسرعة إلى الأقسام المختلفة في التطبيق
        </motion.p>
      </div>

      {/* Quick Links Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {quickLinks.map((link, index) => (
          <motion.button
            key={link.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.9 + index * 0.1 }}
            onClick={() => handleLinkClick(link.href)}
            className={`
              group p-6 rounded-xl border border-border/50 shadow-sm hover:shadow-md transition-all duration-300 text-right
              ${link.bgColor}
            `}
          >
            <div className="flex items-start justify-between">
              <div className="flex-1 space-y-2">
                <h3 className={`font-semibold text-lg group-hover:${link.color} transition-colors`}>
                  {link.title}
                </h3>
                <p className="text-sm text-muted-foreground group-hover:text-foreground transition-colors">
                  {link.description}
                </p>
              </div>
              
              <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${link.color} bg-white/50 dark:bg-black/20 group-hover:scale-110 transition-transform duration-300`}>
                {link.icon}
              </div>
            </div>

            <div className="flex items-center justify-end mt-4 pt-4 border-t border-border/30">
              <span className={`text-sm font-medium ${link.color} group-hover:translate-x-1 transition-transform duration-300 flex items-center gap-2`}>
                انتقال
                <ArrowRight className="w-4 h-4" />
              </span>
            </div>
          </motion.button>
        ))}
      </div>

      {/* Additional Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 1.3 }}
        className="bg-background rounded-xl border border-border/50 shadow-sm p-6"
      >
        <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
          <div className="text-center sm:text-right">
            <h3 className="font-semibold text-foreground mb-1">
              هل تحتاج مساعدة؟
            </h3>
            <p className="text-sm text-muted-foreground">
              تصفح مركز المساعدة أو تواصل مع فريق الدعم
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
              onClick={() => console.log('Open help center')}
            >
              <FileText className="w-4 h-4" />
              <span>مركز المساعدة</span>
              <ExternalLink className="w-3 h-3" />
            </Button>
            
            <Button
              size="sm"
              className="flex items-center gap-2"
              onClick={() => console.log('Contact support')}
            >
              <Zap className="w-4 h-4" />
              <span>تواصل معنا</span>
            </Button>
          </div>
        </div>
      </motion.div>

      {/* Footer Info */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 1.4 }}
        className="text-center py-6 border-t border-border/30"
      >
        <div className="flex flex-col sm:flex-row items-center justify-between gap-4 text-sm text-muted-foreground">
          <div className="flex items-center gap-4">
            <span>© ٢٠٢٤ نوتس. جميع الحقوق محفوظة.</span>
          </div>
          
          <div className="flex items-center gap-4">
            <button 
              className="hover:text-foreground transition-colors"
              onClick={() => console.log('Privacy policy')}
            >
              سياسة الخصوصية
            </button>
            <span>•</span>
            <button 
              className="hover:text-foreground transition-colors"
              onClick={() => console.log('Terms of service')}
            >
              شروط الاستخدام
            </button>
            <span>•</span>
            <button 
              className="hover:text-foreground transition-colors"
              onClick={() => console.log('Contact')}
            >
              اتصل بنا
            </button>
          </div>
        </div>
      </motion.div>
    </motion.div>
  )
}
