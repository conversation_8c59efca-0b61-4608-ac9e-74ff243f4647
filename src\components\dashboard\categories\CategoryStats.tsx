import { motion } from "framer-motion"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>
} from "lucide-react"
import type { Category } from "@/lib/supabase"

interface CategoryStatsProps {
  categories: Category[]
}

export function CategoryStats({ categories }: CategoryStatsProps) {
  // Calculate statistics
  const totalCategories = categories.length
  const totalNotes = categories.reduce((sum, cat) => sum + cat.notes_count, 0)
  const inactiveCategories = categories.filter(cat => cat.notes_count === 0).length
  const pinnedCategories = categories.filter(cat => cat.is_pinned).length
  const favoriteCategories = categories.filter(cat => cat.is_favorite).length

  // Find most used category
  const mostUsedCategory = categories.reduce((prev, current) => 
    (prev.notes_count > current.notes_count) ? prev : current, 
    categories[0] || { name: 'لا توجد فئات', notes_count: 0 }
  )

  const mostUsedPercentage = totalNotes > 0 ? Math.round((mostUsedCategory.notes_count / totalNotes) * 100) : 0

  // Get top 3 categories for chart
  const topCategories = [...categories]
    .sort((a, b) => b.notes_count - a.notes_count)
    .slice(0, 3)

  if (totalCategories === 0) {
    return null
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.2 }}
    >
      <Card className="border-border/50 bg-gradient-to-r from-muted/20 to-accent/10">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center space-x-2 space-x-reverse">
            <BarChart3 className="w-5 h-5 text-primary" />
            <span>إحصائيات الفئات</span>
          </CardTitle>
          <Button variant="ghost" size="sm" className="text-primary hover:text-primary/80">
            عرض كل الإحصائيات
            <ArrowLeft className="w-4 h-4 mr-2" />
          </Button>
        </CardHeader>
        
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Overview Stats */}
            <div className="space-y-4">
              <h4 className="font-semibold text-foreground flex items-center space-x-2 space-x-reverse">
                <PieChart className="w-4 h-4 text-primary" />
                <span>نظرة عامة</span>
              </h4>
              
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">إجمالي الفئات</span>
                  <span className="font-bold text-foreground">{totalCategories}</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">إجمالي الملاحظات</span>
                  <span className="font-bold text-foreground">{totalNotes}</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">فئات مثبتة</span>
                  <span className="font-bold text-primary">{pinnedCategories}</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">فئات مفضلة</span>
                  <span className="font-bold text-yellow-600">{favoriteCategories}</span>
                </div>
              </div>
            </div>

            {/* Most Used Category */}
            <div className="space-y-4">
              <h4 className="font-semibold text-foreground flex items-center space-x-2 space-x-reverse">
                <TrendingUp className="w-4 h-4 text-green-500" />
                <span>الأكثر استخداماً</span>
              </h4>
              
              <div className="space-y-3">
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium text-foreground">{mostUsedCategory.name}</span>
                    <span className="text-sm font-bold text-green-600">{mostUsedPercentage}%</span>
                  </div>
                  <Progress value={mostUsedPercentage} className="h-2" />
                </div>
                
                <div className="text-xs text-muted-foreground">
                  {mostUsedCategory.notes_count} ملاحظة من أصل {totalNotes}
                </div>
              </div>
            </div>

            {/* Top Categories Distribution */}
            <div className="space-y-4">
              <h4 className="font-semibold text-foreground">توزيع الفئات الرئيسية</h4>
              
              <div className="space-y-3">
                {topCategories.map((category, index) => {
                  const percentage = totalNotes > 0 ? Math.round((category.notes_count / totalNotes) * 100) : 0
                  const colors = ['bg-primary', 'bg-secondary', 'bg-accent']
                  
                  return (
                    <div key={category.id} className="space-y-1">
                      <div className="flex justify-between items-center">
                        <span className="text-xs text-muted-foreground truncate">
                          {category.name}
                        </span>
                        <span className="text-xs font-medium">{percentage}%</span>
                      </div>
                      <div className="w-full bg-muted/30 rounded-full h-1.5">
                        <div
                          className={`h-1.5 rounded-full ${colors[index] || 'bg-muted'} transition-all duration-500`}
                          style={{ width: `${percentage}%` }}
                        />
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>

            {/* Warnings */}
            {inactiveCategories > 0 && (
              <div className="space-y-4">
                <h4 className="font-semibold text-foreground flex items-center space-x-2 space-x-reverse">
                  <AlertTriangle className="w-4 h-4 text-orange-500" />
                  <span>تنبيهات</span>
                </h4>
                
                <div className="p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg">
                  <div className="flex items-start space-x-2 space-x-reverse">
                    <AlertTriangle className="w-4 h-4 text-orange-600 mt-0.5 flex-shrink-0" />
                    <div className="text-sm">
                      <p className="font-medium text-orange-800 dark:text-orange-200">
                        {inactiveCategories} فئات غير نشطة
                      </p>
                      <p className="text-orange-600 dark:text-orange-300 mt-1">
                        لا تحتوي على أي ملاحظات
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
