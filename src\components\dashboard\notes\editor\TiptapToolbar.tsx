import { Editor } from '@tiptap/react'
import { motion } from 'framer-motion'
import {
  Bold,
  Italic,
  Underline,
  Code,
  Link,
  Image,
  Quote,
  List,
  ListOrdered,
  Heading1,
  Heading2,
  Heading3,
  Table,
  CheckSquare,
  Highlighter,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Undo,
  Redo,
  Type
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface TiptapToolbarProps {
  editor: Editor | null
  onImageClick: () => void
  onLinkClick: () => void
}

interface ToolbarButtonProps {
  onClick: () => void
  isActive?: boolean
  disabled?: boolean
  tooltip: string
  children: React.ReactNode
}

function ToolbarButton({ onClick, isActive, disabled, tooltip, children }: ToolbarButtonProps) {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          variant={isActive ? "default" : "ghost"}
          size="sm"
          onClick={onClick}
          disabled={disabled}
          className={`h-8 w-8 p-0 transition-all duration-200 ${
            isActive 
              ? 'bg-primary text-primary-foreground shadow-sm' 
              : 'hover:bg-muted hover:text-foreground'
          }`}
        >
          {children}
        </Button>
      </TooltipTrigger>
      <TooltipContent side="bottom" className="bg-popover text-popover-foreground border border-border">
        <p className="text-sm font-medium">{tooltip}</p>
      </TooltipContent>
    </Tooltip>
  )
}

export function TiptapToolbar({ editor, onImageClick, onLinkClick }: TiptapToolbarProps) {
  if (!editor) {
    return null
  }

  const insertTable = () => {
    editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run()
  }

  const toggleTaskList = () => {
    editor.chain().focus().toggleTaskList().run()
  }

  return (
    <TooltipProvider>
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="sticky top-0 z-20 bg-background/95 backdrop-blur-sm border-b border-border/50 p-3"
      >
        <div className="flex items-center gap-1 flex-wrap">
          {/* History Controls */}
          <div className="flex items-center gap-1">
            <ToolbarButton
              onClick={() => editor.chain().focus().undo().run()}
              disabled={!editor.can().undo()}
              tooltip="تراجع: التراجع عن آخر إجراء"
            >
              <Undo className="h-4 w-4" />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => editor.chain().focus().redo().run()}
              disabled={!editor.can().redo()}
              tooltip="إعادة: إعادة الإجراء المتراجع عنه"
            >
              <Redo className="h-4 w-4" />
            </ToolbarButton>
          </div>

          <Separator orientation="vertical" className="h-6 mx-1" />

          {/* Text Formatting */}
          <div className="flex items-center gap-1">
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleBold().run()}
              isActive={editor.isActive('bold')}
              tooltip="عريض: يجعل النص المحدد عريضاً"
            >
              <Bold className="h-4 w-4" />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleItalic().run()}
              isActive={editor.isActive('italic')}
              tooltip="مائل: يجعل النص المحدد مائلاً"
            >
              <Italic className="h-4 w-4" />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleUnderline().run()}
              isActive={editor.isActive('underline')}
              tooltip="تحته خط: يضع خطاً تحت النص المحدد"
            >
              <Underline className="h-4 w-4" />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleCode().run()}
              isActive={editor.isActive('code')}
              tooltip="كود: تنسيق النص كرمز برمجي"
            >
              <Code className="h-4 w-4" />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleHighlight().run()}
              isActive={editor.isActive('highlight')}
              tooltip="تمييز: تمييز النص بلون أصفر"
            >
              <Highlighter className="h-4 w-4" />
            </ToolbarButton>
          </div>

          <Separator orientation="vertical" className="h-6 mx-1" />

          {/* Headings */}
          <div className="flex items-center gap-1">
            <DropdownMenu>
              <Tooltip>
                <TooltipTrigger asChild>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 px-2 gap-1"
                    >
                      <Type className="h-4 w-4" />
                      <span className="text-xs">عنوان</span>
                    </Button>
                  </DropdownMenuTrigger>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p className="text-sm font-medium">عناوين: اختيار حجم العنوان</p>
                </TooltipContent>
              </Tooltip>
              <DropdownMenuContent align="start">
                <DropdownMenuItem
                  onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
                  className={editor.isActive('heading', { level: 1 }) ? 'bg-accent' : ''}
                >
                  <Heading1 className="h-4 w-4 ml-2" />
                  عنوان رئيسي
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
                  className={editor.isActive('heading', { level: 2 }) ? 'bg-accent' : ''}
                >
                  <Heading2 className="h-4 w-4 ml-2" />
                  عنوان فرعي
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
                  className={editor.isActive('heading', { level: 3 }) ? 'bg-accent' : ''}
                >
                  <Heading3 className="h-4 w-4 ml-2" />
                  عنوان صغير
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <Separator orientation="vertical" className="h-6 mx-1" />

          {/* Lists and Structure */}
          <div className="flex items-center gap-1">
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleBulletList().run()}
              isActive={editor.isActive('bulletList')}
              tooltip="قائمة نقطية: إنشاء قائمة بنقاط"
            >
              <List className="h-4 w-4" />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleOrderedList().run()}
              isActive={editor.isActive('orderedList')}
              tooltip="قائمة مرقمة: إنشاء قائمة مرقمة"
            >
              <ListOrdered className="h-4 w-4" />
            </ToolbarButton>
            <ToolbarButton
              onClick={toggleTaskList}
              isActive={editor.isActive('taskList')}
              tooltip="قائمة مهام: إنشاء قائمة مهام قابلة للتحديد"
            >
              <CheckSquare className="h-4 w-4" />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleBlockquote().run()}
              isActive={editor.isActive('blockquote')}
              tooltip="اقتباس: تنسيق النص كاقتباس"
            >
              <Quote className="h-4 w-4" />
            </ToolbarButton>
          </div>

          <Separator orientation="vertical" className="h-6 mx-1" />

          {/* Media and Links */}
          <div className="flex items-center gap-1">
            <ToolbarButton
              onClick={onLinkClick}
              isActive={editor.isActive('link')}
              tooltip="رابط: إدراج أو تعديل رابط"
            >
              <Link className="h-4 w-4" />
            </ToolbarButton>
            <ToolbarButton
              onClick={onImageClick}
              tooltip="صورة: إدراج صورة في المحرر"
            >
              <Image className="h-4 w-4" />
            </ToolbarButton>
            <ToolbarButton
              onClick={insertTable}
              tooltip="جدول: إدراج جدول 3×3"
            >
              <Table className="h-4 w-4" />
            </ToolbarButton>
          </div>

          <Separator orientation="vertical" className="h-6 mx-1" />

          {/* Text Alignment */}
          <div className="flex items-center gap-1">
            <ToolbarButton
              onClick={() => editor.chain().focus().setTextAlign('right').run()}
              isActive={editor.isActive({ textAlign: 'right' })}
              tooltip="محاذاة يمين: محاذاة النص إلى اليمين"
            >
              <AlignRight className="h-4 w-4" />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => editor.chain().focus().setTextAlign('center').run()}
              isActive={editor.isActive({ textAlign: 'center' })}
              tooltip="محاذاة وسط: محاذاة النص في الوسط"
            >
              <AlignCenter className="h-4 w-4" />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => editor.chain().focus().setTextAlign('left').run()}
              isActive={editor.isActive({ textAlign: 'left' })}
              tooltip="محاذاة يسار: محاذاة النص إلى اليسار"
            >
              <AlignLeft className="h-4 w-4" />
            </ToolbarButton>
          </div>
        </div>
      </motion.div>
    </TooltipProvider>
  )
}
