'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Clock, 
  CheckCircle2, 
  AlarmClock, 
  Edit3, 
  Trash2, 
  ChevronDown,
  FileText,
  Calendar,
  AlertCircle,
  Inbox
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ReminderItem, SnoozeOption } from '@/pages/dashboard/Notifications'

interface UpcomingRemindersProps {
  reminders: ReminderItem[]
  onAction: (reminderId: string, action: string, data?: any) => void
  isLoading: boolean
}

interface ReminderCardProps {
  reminder: ReminderItem
  onAction: (action: string, data?: any) => void
  index: number
}

function ReminderCard({ reminder, onAction, index }: ReminderCardProps) {
  const [showSnoozeOptions, setShowSnoozeOptions] = useState(false)

  const formatTime = (date: Date) => {
    return new Intl.DateTimeFormat('ar-SA', {
      hour: '2-digit',
      minute: '2-digit',
      day: 'numeric',
      month: 'short'
    }).format(date)
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-700 border-red-200 dark:bg-red-900/30 dark:text-red-400'
      case 'medium': return 'bg-yellow-100 text-yellow-700 border-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-400'
      case 'low': return 'bg-green-100 text-green-700 border-green-200 dark:bg-green-900/30 dark:text-green-400'
      default: return 'bg-gray-100 text-gray-700 border-gray-200 dark:bg-gray-900/30 dark:text-gray-400'
    }
  }

  const getPriorityLabel = (priority: string) => {
    switch (priority) {
      case 'high': return 'عالية'
      case 'medium': return 'متوسطة'
      case 'low': return 'منخفضة'
      default: return 'عادية'
    }
  }

  const handleSnooze = (option: SnoozeOption) => {
    onAction('snooze', { minutes: option.value })
    setShowSnoozeOptions(false)
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className={`
        bg-background rounded-xl border border-border/50 shadow-sm hover:shadow-md transition-all duration-300 p-6 group
        ${!reminder.isRead ? 'ring-2 ring-primary/20' : ''}
      `}
    >
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-start justify-between">
          <div className="flex-1 space-y-2">
            <div className="flex items-center gap-3">
              <div 
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: reminder.color }}
              />
              <h3 className="font-semibold text-foreground group-hover:text-primary transition-colors">
                {reminder.title}
              </h3>
              {!reminder.isRead && (
                <div className="w-2 h-2 bg-primary rounded-full"></div>
              )}
            </div>
            
            <p className="text-sm text-muted-foreground line-clamp-2">
              {reminder.description}
            </p>
          </div>

          <div className="flex items-center gap-2">
            <Badge 
              variant="outline" 
              className={`text-xs ${getPriorityColor(reminder.priority)}`}
            >
              {getPriorityLabel(reminder.priority)}
            </Badge>
          </div>
        </div>

        {/* Note Preview */}
        <div className="bg-muted/30 rounded-lg p-4 border border-border/30">
          <div className="flex items-center gap-2 mb-2">
            <FileText className="w-4 h-4 text-muted-foreground" />
            <span className="text-sm font-medium text-foreground">
              {reminder.noteTitle}
            </span>
          </div>
          <p className="text-sm text-muted-foreground line-clamp-2">
            {reminder.notePreview}
          </p>
        </div>

        {/* Time and Category */}
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2 text-muted-foreground">
              <Clock className="w-4 h-4" />
              <span>{formatTime(reminder.reminderAt)}</span>
            </div>
            <Badge variant="secondary" className="text-xs">
              {reminder.category}
            </Badge>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-2 pt-2 border-t border-border/30">
          {/* Done Button */}
          <Button
            size="sm"
            onClick={() => onAction('done')}
            className="flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white"
          >
            <CheckCircle2 className="w-4 h-4" />
            <span>تم</span>
          </Button>

          {/* Snooze Button with Dropdown */}
          <div className="relative">
            <Button
              size="sm"
              variant="outline"
              onClick={() => setShowSnoozeOptions(!showSnoozeOptions)}
              className="flex items-center gap-2"
            >
              <AlarmClock className="w-4 h-4" />
              <span>تأجيل</span>
              <ChevronDown className={`w-3 h-3 transition-transform ${
                showSnoozeOptions ? 'rotate-180' : ''
              }`} />
            </Button>

            <AnimatePresence>
              {showSnoozeOptions && (
                <motion.div
                  initial={{ opacity: 0, y: -10, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: -10, scale: 0.95 }}
                  transition={{ duration: 0.2 }}
                  className="absolute top-full left-0 mt-2 w-40 bg-background rounded-lg border border-border/50 shadow-lg z-50"
                >
                  <div className="p-2">
                    {reminder.snoozeOptions.map((option) => (
                      <button
                        key={option.value}
                        onClick={() => handleSnooze(option)}
                        className="w-full text-right px-3 py-2 rounded-md text-sm text-foreground hover:bg-muted/50 transition-colors"
                      >
                        {option.label}
                      </button>
                    ))}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Edit Button */}
          <Button
            size="sm"
            variant="outline"
            onClick={() => onAction('edit')}
            className="flex items-center gap-2"
          >
            <Edit3 className="w-4 h-4" />
            <span>تعديل</span>
          </Button>

          {/* Delete Button */}
          <Button
            size="sm"
            variant="outline"
            onClick={() => onAction('delete')}
            className="flex items-center gap-2 text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
          >
            <Trash2 className="w-4 h-4" />
            <span>حذف</span>
          </Button>
        </div>
      </div>
    </motion.div>
  )
}

function EmptyState() {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
      className="text-center py-12"
    >
      <div className="w-24 h-24 mx-auto mb-6 bg-muted/30 rounded-full flex items-center justify-center">
        <Inbox className="w-12 h-12 text-muted-foreground" />
      </div>
      <h3 className="text-lg font-semibold text-foreground mb-2">
        لا توجد تذكيرات قادمة
      </h3>
      <p className="text-muted-foreground max-w-md mx-auto">
        عندما تقوم بإنشاء تذكيرات لملاحظاتك، ستظهر هنا لتساعدك على البقاء منظماً.
      </p>
    </motion.div>
  )
}

function LoadingSkeleton() {
  return (
    <div className="space-y-4">
      {Array.from({ length: 3 }).map((_, i) => (
        <motion.div
          key={i}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: i * 0.1 }}
          className="bg-background rounded-xl border border-border/50 shadow-sm p-6"
        >
          <div className="space-y-4">
            <div className="flex items-start justify-between">
              <div className="flex-1 space-y-2">
                <div className="h-5 bg-muted/50 rounded animate-pulse w-3/4"></div>
                <div className="h-4 bg-muted/50 rounded animate-pulse w-1/2"></div>
              </div>
              <div className="h-6 bg-muted/50 rounded animate-pulse w-16"></div>
            </div>
            <div className="h-20 bg-muted/50 rounded-lg animate-pulse"></div>
            <div className="flex gap-2">
              {Array.from({ length: 4 }).map((_, j) => (
                <div key={j} className="h-8 bg-muted/50 rounded animate-pulse w-16"></div>
              ))}
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  )
}

export function UpcomingReminders({ reminders, onAction, isLoading }: UpcomingRemindersProps) {
  const handleReminderAction = (reminderId: string, action: string, data?: any) => {
    onAction(reminderId, action, data)
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.2 }}
      className="space-y-6"
    >
      {/* Section Header */}
      <div className="flex items-center gap-3">
        <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center">
          <Clock className="w-4 h-4 text-white" />
        </div>
        <div>
          <h2 className="text-xl font-bold text-foreground">التذكيرات القادمة</h2>
          <p className="text-sm text-muted-foreground">
            تذكيرات ملاحظاتك المجدولة
          </p>
        </div>
      </div>

      {/* Content */}
      {isLoading ? (
        <LoadingSkeleton />
      ) : reminders.length === 0 ? (
        <EmptyState />
      ) : (
        <div className="space-y-4">
          {reminders.map((reminder, index) => (
            <ReminderCard
              key={reminder.id}
              reminder={reminder}
              onAction={(action, data) => handleReminderAction(reminder.id, action, data)}
              index={index}
            />
          ))}
        </div>
      )}
    </motion.div>
  )
}
