import { motion } from "framer-motion"
import { Card, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { 
  GraduationCap, 
  Briefcase, 
  Lightbulb, 
  User, 
  ArrowRight,
  CheckCircle,
  Target
} from "lucide-react"

const audiences = [
  {
    id: 1,
    icon: GraduationCap,
    title: "الطلاب",
    subtitle: "تفوق في دراستك",
    description: "حول محاضراتك وكتبك إلى ملاحظات منظمة ومفهومة",
    benefits: [
      "تلخيص المحاضرات تلقائياً",
      "تنظيم المواد الدراسية",
      "تحويل التسجيلات الصوتية لنصوص",
      "مراجعة سريعة قبل الامتحانات"
    ],
    color: "text-nots-blue",
    bgColor: "bg-nots-blue/10",
    borderColor: "border-nots-blue/20",
    illustration: "🎓"
  },
  {
    id: 2,
    icon: Briefcase,
    title: "الموظفون",
    subtitle: "كفاءة أكبر في العمل",
    description: "نظم اجتماعاتك ومشاريعك وأفكارك المهنية بذكاء",
    benefits: [
      "تسجيل محاضر الاجتماعات",
      "تنظيم خطط المشاريع",
      "متابعة المهام والأهداف",
      "مشاركة الملاحظات مع الفريق"
    ],
    color: "text-primary",
    bgColor: "bg-primary/10",
    borderColor: "border-primary/20",
    illustration: "💼"
  },
  {
    id: 3,
    icon: Lightbulb,
    title: "المبدعون",
    subtitle: "أطلق إبداعك",
    description: "احتفظ بأفكارك الإبداعية وطورها لمشاريع ناجحة",
    benefits: [
      "تسجيل الأفكار الإبداعية",
      "تطوير المفاهيم والقصص",
      "تنظيم مشاريع الكتابة",
      "إلهام من الذكاء الاصطناعي"
    ],
    color: "text-nots-orange",
    bgColor: "bg-nots-orange/10",
    borderColor: "border-nots-orange/20",
    illustration: "🎨"
  },
  {
    id: 4,
    icon: User,
    title: "الاستخدام الشخصي",
    subtitle: "نظم حياتك اليومية",
    description: "من قوائم المهام إلى الذكريات الشخصية، نظم كل شيء",
    benefits: [
      "يوميات ومذكرات شخصية",
      "قوائم المهام والتذكيرات",
      "تخطيط الأهداف الشخصية",
      "حفظ الوصفات والنصائح"
    ],
    color: "text-nots-green",
    bgColor: "bg-nots-green/10",
    borderColor: "border-nots-green/20",
    illustration: "👤"
  }
]

export function TargetAudience() {
  return (
    <section className="py-20 bg-nots-bg-light/30" id="use-cases">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-purple/10 text-primary font-medium mb-6">
            <Target className="w-5 h-5" />
            <span>🎯 حالات الاستخدام</span>
          </div>
          
          <h2 className="text-3xl md:text-5xl font-bold nots-heading mb-6">
            <span className="bg-gradient-purple bg-clip-text text-transparent">Nots</span> مصمم
            <br />لكل احتياجاتك
          </h2>
          
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            سواء كنت طالباً أو موظفاً أو مبدعاً، Nots يتكيف مع احتياجاتك 
            ويقدم حلولاً ذكية لكل نوع من أنواع التدوين والتنظيم.
          </p>
        </motion.div>

        {/* Audience Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {audiences.map((audience, index) => {
            const Icon = audience.icon
            return (
              <motion.div
                key={audience.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="group"
              >
                <Card className={`h-full nots-shadow hover:nots-shadow-lg transition-all duration-300 border-2 ${audience.borderColor} hover:border-opacity-40 overflow-hidden relative`}>
                  {/* Background Illustration */}
                  <div className="absolute top-4 left-4 text-6xl opacity-10">
                    {audience.illustration}
                  </div>
                  
                  <CardHeader className="relative z-10">
                    <div className="flex items-center gap-4 mb-4">
                      <div className={`w-16 h-16 ${audience.bgColor} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                        <Icon className={`w-8 h-8 ${audience.color}`} />
                      </div>
                      <div>
                        <CardTitle className="text-xl font-bold nots-heading mb-1">
                          {audience.title}
                        </CardTitle>
                        <p className={`text-sm font-medium ${audience.color}`}>
                          {audience.subtitle}
                        </p>
                      </div>
                    </div>
                    
                    <p className="text-muted-foreground leading-relaxed">
                      {audience.description}
                    </p>
                  </CardHeader>
                  
                  <CardContent className="relative z-10">
                    <div className="space-y-3 mb-6">
                      {audience.benefits.map((benefit, benefitIndex) => (
                        <motion.div
                          key={benefitIndex}
                          initial={{ opacity: 0, x: -20 }}
                          whileInView={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.5, delay: (index * 0.1) + (benefitIndex * 0.05) }}
                          viewport={{ once: true }}
                          className="flex items-center gap-3"
                        >
                          <CheckCircle className={`w-5 h-5 ${audience.color} flex-shrink-0`} />
                          <span className="text-sm text-muted-foreground">{benefit}</span>
                        </motion.div>
                      ))}
                    </div>
                    
                    <Button 
                      variant="ghost" 
                      className={`${audience.color} hover:${audience.bgColor} p-0 h-auto font-medium group-hover:translate-x-1 transition-transform duration-300`}
                    >
                      ابدأ كـ{audience.title.slice(0, -1)}
                      <ArrowRight className="w-4 h-4 mr-1" />
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            )
          })}
        </div>

        {/* Success Stories */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="bg-gradient-purple rounded-2xl p-8 md:p-12 text-white text-center relative overflow-hidden"
        >
          {/* Background Elements */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-8 right-8">
              <Target className="w-24 h-24" />
            </div>
            <div className="absolute bottom-8 left-8">
              <Lightbulb className="w-20 h-20" />
            </div>
          </div>
          
          <div className="relative z-10 max-w-4xl mx-auto">
            <h3 className="text-2xl md:text-4xl font-bold mb-6">
              انضم إلى مجتمع المستخدمين الناجحين
            </h3>
            
            <p className="text-lg text-white/90 mb-8 leading-relaxed">
              آلاف المستخدمين حول العالم يستخدمون Nots لتحسين إنتاجيتهم وتنظيم أفكارهم. 
              كن جزءاً من هذا المجتمع المتنامي.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <div className="text-center">
                <div className="text-3xl font-bold mb-2">15,000+</div>
                <div className="text-white/80">طالب</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold mb-2">3,500+</div>
                <div className="text-white/80">موظف</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold mb-2">1,200+</div>
                <div className="text-white/80">مبدع</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold mb-2">300+</div>
                <div className="text-white/80">شركة</div>
              </div>
            </div>
            
            <Button 
              size="lg"
              className="bg-white text-primary hover:bg-white/90 px-8 py-4 text-lg font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
            >
              ابدأ رحلتك مع Nots
              <ArrowRight className="w-5 h-5 mr-2" />
            </Button>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
