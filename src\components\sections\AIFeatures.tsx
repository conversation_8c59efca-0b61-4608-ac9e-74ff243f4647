import { motion } from "framer-motion"
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { 
  FileText, 
  Lightbulb, 
  Mic, 
  Folder<PERSON>ree, 
  Sparkles, 
  ArrowRight,
  Zap,
  Brain
} from "lucide-react"

const features = [
  {
    id: 1,
    icon: FileText,
    title: "تلخيص تلقائي",
    description: "احصل على ملخصات ذكية لملاحظاتك الطويلة في ثوانٍ معدودة",
    details: "يستخدم GPT-4 لفهم السياق العربي وإنتاج ملخصات دقيقة ومفيدة",
    color: "text-nots-blue",
    bgColor: "bg-nots-blue/10",
    borderColor: "border-nots-blue/20"
  },
  {
    id: 2,
    icon: Lightbulb,
    title: "توسيع الأفكار",
    description: "حوّل أفكارك البسيطة إلى محتوى مفصل وغني بالمعلومات",
    details: "يساعدك في تطوير أفكارك وإضافة تفاصيل مهمة لم تفكر بها",
    color: "text-nots-orange",
    bgColor: "bg-nots-orange/10",
    borderColor: "border-nots-orange/20"
  },
  {
    id: 3,
    icon: Mic,
    title: "تحويل الصوت لنص",
    description: "سجّل أفكارك صوتياً واحصل على نص عربي دقيق فوراً",
    details: "تقنية متقدمة للتعرف على الصوت العربي بدقة عالية",
    color: "text-nots-green",
    bgColor: "bg-nots-green/10",
    borderColor: "border-nots-green/20"
  },
  {
    id: 4,
    icon: FolderTree,
    title: "تصنيف ذكي",
    description: "تنظيم تلقائي لملاحظاتك في مجلدات وعلامات مناسبة",
    details: "يفهم محتوى ملاحظاتك ويصنفها تلقائياً حسب الموضوع",
    color: "text-primary",
    bgColor: "bg-primary/10",
    borderColor: "border-primary/20"
  }
]

export function AIFeatures() {
  return (
    <section className="py-20 bg-background" id="ai-features">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-purple/10 text-primary font-medium mb-6">
            <Brain className="w-5 h-5" />
            <span>🧠 الميزات الذكية</span>
          </div>
          
          <h2 className="text-3xl md:text-5xl font-bold nots-heading mb-6">
            قوة <span className="bg-gradient-purple bg-clip-text text-transparent">الذكاء الاصطناعي</span>
            <br />في خدمة ملاحظاتك
          </h2>
          
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            اكتشف كيف يمكن للذكاء الاصطناعي أن يحول طريقة تدوينك وتنظيم أفكارك. 
            ميزات متطورة مصممة خصيصاً للمحتوى العربي.
          </p>
        </motion.div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {features.map((feature, index) => {
            const Icon = feature.icon
            return (
              <motion.div
                key={feature.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="group"
              >
                <Card className={`h-full nots-shadow hover:nots-shadow-lg transition-all duration-300 border-2 ${feature.borderColor} hover:border-opacity-40 overflow-hidden relative`}>
                  {/* Background Pattern */}
                  <div className="absolute inset-0 opacity-5">
                    <div className="absolute top-4 right-4">
                      <Icon className="w-32 h-32 text-current" />
                    </div>
                  </div>
                  
                  <CardHeader className="relative z-10">
                    <div className={`w-16 h-16 rounded-xl ${feature.bgColor} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                      <Icon className={`w-8 h-8 ${feature.color}`} />
                    </div>
                    
                    <CardTitle className="text-xl font-bold nots-heading mb-2">
                      {feature.title}
                    </CardTitle>
                  </CardHeader>
                  
                  <CardContent className="relative z-10">
                    <p className="text-muted-foreground mb-4 leading-relaxed">
                      {feature.description}
                    </p>
                    
                    <p className="text-sm text-muted-foreground/80 mb-6">
                      {feature.details}
                    </p>
                    
                    <Button 
                      variant="ghost" 
                      className={`${feature.color} hover:${feature.bgColor} p-0 h-auto font-medium group-hover:translate-x-1 transition-transform duration-300`}
                    >
                      تعرف على المزيد
                      <ArrowRight className="w-4 h-4 mr-1" />
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            )
          })}
        </div>

        {/* AI Power Showcase */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="bg-gradient-purple rounded-2xl p-8 md:p-12 text-white text-center relative overflow-hidden"
        >
          {/* Background Elements */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-8 right-8">
              <Sparkles className="w-24 h-24" />
            </div>
            <div className="absolute bottom-8 left-8">
              <Zap className="w-20 h-20" />
            </div>
          </div>
          
          <div className="relative z-10 max-w-3xl mx-auto">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-white/20 backdrop-blur-sm font-medium mb-6">
              <Sparkles className="w-5 h-5" />
              <span>مدعوم بـ GPT-4</span>
            </div>
            
            <h3 className="text-2xl md:text-4xl font-bold mb-6">
              ذكاء اصطناعي متقدم يفهم العربية
            </h3>
            
            <p className="text-lg text-white/90 mb-8 leading-relaxed">
              نستخدم أحدث تقنيات الذكاء الاصطناعي المدربة خصيصاً على المحتوى العربي 
              لنقدم لك تجربة فريدة في تدوين وتنظيم الملاحظات.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="text-center">
                <div className="text-3xl font-bold mb-2">99.5%</div>
                <div className="text-white/80">دقة التعرف على النص العربي</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold mb-2">{"< 3s"}</div>
                <div className="text-white/80">سرعة المعالجة</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold mb-2">24/7</div>
                <div className="text-white/80">متاح دائماً</div>
              </div>
            </div>
            
            <Button 
              size="lg"
              className="bg-white text-primary hover:bg-white/90 px-8 py-4 text-lg font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
            >
              جرب الذكاء الاصطناعي الآن
              <Sparkles className="w-5 h-5 mr-2" />
            </Button>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
