import { useState, useRef, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Link } from "react-router-dom"
import { Button } from "@/components/ui/button"
import { ThemeToggle } from "../theme/ThemeToggle"
import { useAuthStore } from "@/stores/authStore"
import {
  Menu,
  X,
  Sparkles,
  Brain,
  Wrench,
  HelpCircle,
  MessageSquare,
  Globe,
  ChevronDown,
  DollarSign,
  Zap,
  Shield,
  Users,
  Star,
  UserPlus,
  LogOut,
  Settings,
  LayoutDashboard
} from "lucide-react"

const navItems = [
  {
    name: "الميزات",
    href: "#features",
    icon: Brain,
    dropdown: [
      { name: "الميزات الذكية", href: "#ai-features", icon: Brain, description: "ذكاء اصطناعي متقدم" },
      { name: "الأدوات", href: "#tools", icon: Wrench, description: "أدوات قوية ومتنوعة" },
      { name: "الحماية", href: "#security", icon: Shield, description: "أمان وخصوصية عالية" }
    ]
  },
  { name: "كيف يعمل؟", href: "#how-it-works", icon: HelpCircle },
  { name: "الأسعار", href: "#pricing", icon: DollarSign },
  {
    name: "المجتمع",
    href: "#community",
    icon: Users,
    dropdown: [
      { name: "آراء المستخدمين", href: "#reviews", icon: MessageSquare, description: "تجارب حقيقية" },
      { name: "قصص النجاح", href: "#success-stories", icon: Star, description: "إنجازات ملهمة" },
      { name: "المطورين", href: "#developers", icon: Zap, description: "موارد للمطورين" }
    ]
  },
  { name: "الأسئلة الشائعة", href: "#faq", icon: HelpCircle },
]

export function Navigation() {
  const [isOpen, setIsOpen] = useState(false)
  const [language, setLanguage] = useState<'ar' | 'en'>('ar')
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)
  const [showUserMenu, setShowUserMenu] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const userMenuRef = useRef<HTMLDivElement>(null)
  const { isAuthenticated, user, logout } = useAuthStore()

  const toggleLanguage = () => {
    setLanguage(prev => prev === 'ar' ? 'en' : 'ar')
  }

  const handleLogout = async () => {
    await logout()
    setIsOpen(false)
    setShowUserMenu(false)
  }

  const handleDropdownToggle = (itemName: string) => {
    setActiveDropdown(activeDropdown === itemName ? null : itemName)
  }

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setActiveDropdown(null)
      }
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  return (
    <motion.nav
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      className="fixed top-0 left-0 right-0 z-50 bg-background/95 backdrop-blur-md border-b border-border/50 nots-shadow-lg"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16 md:h-18">
          {/* Logo */}
          <Link to="/">
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex items-center space-x-2 space-x-reverse cursor-pointer"
            >
              <div className="w-9 h-9 bg-gradient-to-r from-primary to-secondary rounded-xl flex items-center justify-center shadow-lg">
                <Sparkles className="w-5 h-5 text-primary-foreground" />
              </div>
              <h1 className="text-2xl md:text-3xl font-bold text-primary nots-heading">Nots</h1>
            </motion.div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-8 space-x-reverse" ref={dropdownRef}>
            {navItems.map((item, index) => {
              const Icon = item.icon
              const hasDropdown = item.dropdown && item.dropdown.length > 0
              
              return (
                <div key={item.name} className="relative">
                  {hasDropdown ? (
                    <motion.button
                      onClick={() => handleDropdownToggle(item.name)}
                      className="text-foreground hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 flex items-center space-x-1 space-x-reverse hover:bg-muted/50"
                      whileHover={{ scale: 1.05 }}
                      initial={{ opacity: 0, y: -20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                    >
                      <Icon className="w-4 h-4" />
                      <span>{item.name}</span>
                      <ChevronDown className={`w-3 h-3 transition-transform duration-200 ${
                        activeDropdown === item.name ? 'rotate-180' : ''
                      }`} />
                    </motion.button>
                  ) : (
                    <motion.a
                      href={item.href}
                      className="text-foreground hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 flex items-center space-x-1 space-x-reverse hover:bg-muted/50"
                      whileHover={{ scale: 1.05 }}
                      initial={{ opacity: 0, y: -20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                    >
                      <Icon className="w-4 h-4" />
                      <span>{item.name}</span>
                    </motion.a>
                  )}
                  
                  {/* Desktop Dropdown */}
                  <AnimatePresence>
                    {hasDropdown && activeDropdown === item.name && (
                      <motion.div
                        initial={{ opacity: 0, y: 10, scale: 0.95 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        exit={{ opacity: 0, y: 10, scale: 0.95 }}
                        transition={{ duration: 0.2 }}
                        className="absolute top-full right-0 mt-2 w-64 bg-background/95 backdrop-blur-md border border-border/50 rounded-xl shadow-lg py-2"
                      >
                        {item.dropdown?.map((dropdownItem) => {
                          const DropdownIcon = dropdownItem.icon
                          return (
                            <motion.a
                              key={dropdownItem.name}
                              href={dropdownItem.href}
                              className="flex items-start space-x-3 space-x-reverse px-4 py-3 text-sm hover:bg-muted/50 transition-colors"
                              onClick={() => setActiveDropdown(null)}
                              whileHover={{ x: -4 }}
                            >
                              <DropdownIcon className="w-5 h-5 mt-0.5 text-primary" />
                              <div>
                                <div className="font-medium text-foreground">{dropdownItem.name}</div>
                                <div className="text-xs text-muted-foreground mt-1">{dropdownItem.description}</div>
                              </div>
                            </motion.a>
                          )
                        })}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              )
            })}
          </div>

          {/* Language Toggle, Theme Toggle & Auth */}
          <div className="hidden md:flex items-center space-x-4 space-x-reverse">
            <button
              onClick={toggleLanguage}
              className="flex items-center space-x-1 space-x-reverse px-3 py-2 rounded-md text-sm font-medium text-foreground hover:text-primary hover:bg-muted/50 transition-all duration-200"
            >
              <Globe className="w-4 h-4" />
              <span>{language === 'ar' ? 'English' : 'العربية'}</span>
            </button>
            <ThemeToggle />
            
            {isAuthenticated ? (
              <div className="relative" ref={userMenuRef}>
                <motion.button
                  onClick={() => setShowUserMenu(!showUserMenu)}
                  className="flex items-center space-x-2 space-x-reverse p-2 rounded-lg hover:bg-muted/50 transition-all duration-200"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className="w-8 h-8 rounded-full bg-gradient-to-r from-primary to-secondary flex items-center justify-center text-white text-sm font-medium">
                    {user?.fullName?.charAt(0) || 'U'}
                  </div>
                  <span className="text-sm font-medium text-foreground hidden lg:block">
                    {user?.fullName || 'مستخدم'}
                  </span>
                  <ChevronDown className={`w-3 h-3 transition-transform duration-200 ${
                    showUserMenu ? 'rotate-180' : ''
                  }`} />
                </motion.button>
                
                {/* User Dropdown Menu */}
                <AnimatePresence>
                  {showUserMenu && (
                    <motion.div
                      initial={{ opacity: 0, y: 10, scale: 0.95 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      exit={{ opacity: 0, y: 10, scale: 0.95 }}
                      transition={{ duration: 0.2 }}
                      className="absolute top-full right-0 mt-2 w-56 bg-background/95 backdrop-blur-md border border-border/50 rounded-xl shadow-lg py-2"
                    >
                      <div className="px-4 py-3 border-b border-border/30">
                        <p className="font-medium text-foreground">{user?.fullName}</p>
                        <p className="text-sm text-muted-foreground">{user?.email}</p>
                        <p className="text-xs text-primary mt-1">خطة {user?.plan === 'free' ? 'مجانية' : 'مميزة'}</p>
                      </div>
                      
                      <div className="py-1">
                        <Link
                          to="/dashboard"
                          className="flex items-center space-x-2 space-x-reverse px-4 py-2 text-sm hover:bg-muted/50 transition-colors"
                          onClick={() => setShowUserMenu(false)}
                        >
                          <LayoutDashboard className="w-4 h-4" />
                          <span>لوحة التحكم</span>
                        </Link>
                        
                        <Link
                          to="/settings"
                          className="flex items-center space-x-2 space-x-reverse px-4 py-2 text-sm hover:bg-muted/50 transition-colors"
                          onClick={() => setShowUserMenu(false)}
                        >
                          <Settings className="w-4 h-4" />
                          <span>الإعدادات</span>
                        </Link>
                      </div>
                      
                      <div className="border-t border-border/30 pt-1">
                        <button
                          onClick={handleLogout}
                          className="w-full flex items-center space-x-2 space-x-reverse px-4 py-2 text-sm text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
                        >
                          <LogOut className="w-4 h-4" />
                          <span>تسجيل الخروج</span>
                        </button>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            ) : (
              <Link to="/auth/signup">
                <Button className="bg-gradient-to-r from-primary to-secondary hover:opacity-90 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 flex items-center space-x-1 space-x-reverse">
                  <Sparkles className="w-4 h-4" />
                  <span>✨ ابدأ مجاناً</span>
                </Button>
              </Link>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <motion.button
              onClick={() => setIsOpen(!isOpen)}
              className="text-foreground hover:text-primary p-2 rounded-md transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {isOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </motion.button>
          </div>
        </div>

        {/* Mobile Navigation */}
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="md:hidden bg-background/95 backdrop-blur-md border-t border-border/50"
            >
              <div className="px-4 py-6 space-y-4">
                {/* Mobile Navigation Items */}
                {navItems.map((item, index) => {
                  const Icon = item.icon
                  const hasDropdown = item.dropdown && item.dropdown.length > 0

                  return (
                    <div key={item.name}>
                      {hasDropdown ? (
                        <div>
                          <motion.button
                            onClick={() => handleDropdownToggle(item.name)}
                            className="w-full flex items-center justify-between space-x-2 space-x-reverse px-3 py-2 text-foreground hover:text-primary hover:bg-muted/50 rounded-lg transition-all duration-200"
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.3, delay: index * 0.1 }}
                          >
                            <div className="flex items-center space-x-2 space-x-reverse">
                              <Icon className="w-5 h-5" />
                              <span className="font-medium">{item.name}</span>
                            </div>
                            <ChevronDown className={`w-4 h-4 transition-transform duration-200 ${
                              activeDropdown === item.name ? 'rotate-180' : ''
                            }`} />
                          </motion.button>

                          <AnimatePresence>
                            {activeDropdown === item.name && (
                              <motion.div
                                initial={{ opacity: 0, height: 0 }}
                                animate={{ opacity: 1, height: "auto" }}
                                exit={{ opacity: 0, height: 0 }}
                                transition={{ duration: 0.2 }}
                                className="mt-2 mr-6 space-y-1"
                              >
                                {item.dropdown?.map((dropdownItem) => {
                                  const DropdownIcon = dropdownItem.icon
                                  return (
                                    <motion.a
                                      key={dropdownItem.name}
                                      href={dropdownItem.href}
                                      className="flex items-center space-x-2 space-x-reverse px-3 py-2 text-sm text-muted-foreground hover:text-foreground hover:bg-muted/30 rounded-lg transition-colors"
                                      onClick={() => {
                                        setActiveDropdown(null)
                                        setIsOpen(false)
                                      }}
                                      whileHover={{ x: -4 }}
                                    >
                                      <DropdownIcon className="w-4 h-4" />
                                      <span>{dropdownItem.name}</span>
                                    </motion.a>
                                  )
                                })}
                              </motion.div>
                            )}
                          </AnimatePresence>
                        </div>
                      ) : (
                        <motion.a
                          href={item.href}
                          className="flex items-center space-x-2 space-x-reverse px-3 py-2 text-foreground hover:text-primary hover:bg-muted/50 rounded-lg transition-all duration-200"
                          onClick={() => setIsOpen(false)}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.3, delay: index * 0.1 }}
                          whileHover={{ x: -4 }}
                        >
                          <Icon className="w-5 h-5" />
                          <span className="font-medium">{item.name}</span>
                        </motion.a>
                      )}
                    </div>
                  )
                })}

                {/* Mobile Language Toggle */}
                <motion.button
                  onClick={toggleLanguage}
                  className="flex items-center space-x-2 space-x-reverse px-3 py-2 text-foreground hover:text-primary hover:bg-muted/50 rounded-lg transition-all duration-200 w-full"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: navItems.length * 0.1 }}
                >
                  <Globe className="w-5 h-5" />
                  <span className="font-medium">{language === 'ar' ? 'English' : 'العربية'}</span>
                </motion.button>

                {/* Mobile Auth Section */}
                <div className="pt-4 border-t border-border/30">
                  {isAuthenticated ? (
                    <div className="space-y-3">
                      <div className="flex items-center space-x-3 space-x-reverse p-3 bg-muted/30 rounded-lg">
                        <div className="w-10 h-10 rounded-full bg-gradient-to-r from-primary to-secondary flex items-center justify-center text-white font-medium">
                          {user?.fullName?.charAt(0) || 'U'}
                        </div>
                        <div className="flex-1">
                          <p className="font-medium text-foreground">{user?.fullName || 'مستخدم'}</p>
                          <p className="text-sm text-muted-foreground">{user?.email}</p>
                        </div>
                      </div>

                      <Link
                        to="/dashboard"
                        className="flex items-center space-x-2 space-x-reverse px-3 py-2 text-foreground hover:text-primary hover:bg-muted/50 rounded-lg transition-all duration-200"
                        onClick={() => setIsOpen(false)}
                      >
                        <LayoutDashboard className="w-5 h-5" />
                        <span className="font-medium">لوحة التحكم</span>
                      </Link>

                      <button
                        onClick={handleLogout}
                        className="w-full flex items-center space-x-2 space-x-reverse px-3 py-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-all duration-200"
                      >
                        <LogOut className="w-5 h-5" />
                        <span className="font-medium">تسجيل الخروج</span>
                      </button>
                    </div>
                  ) : (
                    <Link to="/auth/signup">
                      <Button
                        className="w-full bg-gradient-to-r from-primary to-secondary hover:opacity-90 text-primary-foreground rounded-xl shadow-lg flex items-center justify-center space-x-1 space-x-reverse"
                        onClick={() => setIsOpen(false)}
                      >
                        <UserPlus className="w-4 h-4" />
                        <span>✨ ابدأ مجاناً</span>
                      </Button>
                    </Link>
                  )}
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.nav>
  )
}
