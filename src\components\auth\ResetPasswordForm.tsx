import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { useNavigate, useSearchParams } from "react-router-dom"
import { Eye, EyeOff, Lock, Check, X } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { AuthLayout } from "./shared/AuthLayout"
import { PasswordStrengthMeter } from "./shared/PasswordStrengthMeter"
import { resetPasswordSchema, type ResetPasswordFormData } from "@/lib/validations/auth"
import { useAuthStore } from "@/stores/authStore"
import { toast } from "sonner"

export function ResetPasswordForm() {
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const { resetPassword, isLoading } = useAuthStore()

  const token = searchParams.get('token') || ''

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isValid }
  } = useForm<ResetPasswordFormData>({
    resolver: zodResolver(resetPasswordSchema),
    mode: "onChange"
  })

  const newPassword = watch("newPassword")
  const confirmNewPassword = watch("confirmNewPassword")
  const passwordsMatch = newPassword && confirmNewPassword && newPassword === confirmNewPassword

  // Redirect if no token
  useEffect(() => {
    if (!token) {
      toast.error("رابط غير صالح", {
        description: "يرجى طلب رابط إعادة تعيين جديد"
      })
      navigate('/auth/forgot')
    }
  }, [token, navigate])

  const onSubmit = async (data: ResetPasswordFormData) => {
    const success = await resetPassword(token, data.newPassword)
    
    if (success) {
      toast.success("✅ تم تغيير كلمة المرور بنجاح!", {
        description: "يمكنك الآن تسجيل الدخول بكلمة المرور الجديدة"
      })
      navigate('/auth/login')
    } else {
      toast.error("فشل في تغيير كلمة المرور", {
        description: "الرابط قد يكون منتهي الصلاحية. يرجى طلب رابط جديد"
      })
    }
  }

  if (!token) {
    return null
  }

  return (
    <AuthLayout
      title="إعادة تعيين كلمة المرور"
      description="أدخل كلمة المرور الجديدة لحسابك"
    >
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* New Password Field */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="space-y-2"
        >
          <Label htmlFor="newPassword" className="text-sm font-medium text-foreground">
            كلمة المرور الجديدة
          </Label>
          <div className="relative">
            <Input
              id="newPassword"
              type={showNewPassword ? "text" : "password"}
              placeholder="أدخل كلمة مرور قوية"
              className={`px-10 transition-all duration-200 ${
                errors.newPassword ? 'border-red-500 focus:border-red-500' : ''
              }`}
              {...register("newPassword")}
            />
            <Lock className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <button
              type="button"
              onClick={() => setShowNewPassword(!showNewPassword)}
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
            >
              {showNewPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </button>
          </div>
          {errors.newPassword && (
            <motion.p
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-sm text-red-500 flex items-center space-x-1 space-x-reverse"
            >
              <X className="w-4 h-4" />
              <span>{errors.newPassword.message}</span>
            </motion.p>
          )}
          <PasswordStrengthMeter password={newPassword || ""} />
        </motion.div>

        {/* Confirm New Password Field */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="space-y-2"
        >
          <Label htmlFor="confirmNewPassword" className="text-sm font-medium text-foreground">
            تأكيد كلمة المرور الجديدة
          </Label>
          <div className="relative">
            <Input
              id="confirmNewPassword"
              type={showConfirmPassword ? "text" : "password"}
              placeholder="أعد إدخال كلمة المرور"
              className={`px-10 transition-all duration-200 ${
                errors.confirmNewPassword ? 'border-red-500 focus:border-red-500' : 
                passwordsMatch ? 'border-green-500 focus:border-green-500' : ''
              }`}
              {...register("confirmNewPassword")}
            />
            <Lock className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
            >
              {showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </button>
            {confirmNewPassword && (
              <div className="absolute left-10 top-1/2 transform -translate-y-1/2">
                {passwordsMatch ? (
                  <Check className="w-4 h-4 text-green-500" />
                ) : (
                  <X className="w-4 h-4 text-red-500" />
                )}
              </div>
            )}
          </div>
          {errors.confirmNewPassword && (
            <motion.p
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-sm text-red-500 flex items-center space-x-1 space-x-reverse"
            >
              <X className="w-4 h-4" />
              <span>{errors.confirmNewPassword.message}</span>
            </motion.p>
          )}
          {passwordsMatch && (
            <motion.p
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-sm text-green-600 flex items-center space-x-1 space-x-reverse"
            >
              <Check className="w-4 h-4" />
              <span>كلمات المرور متطابقة</span>
            </motion.p>
          )}
        </motion.div>

        {/* Submit Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <Button
            type="submit"
            disabled={!isValid || isLoading}
            className="w-full bg-gradient-to-r from-primary to-secondary hover:opacity-90 text-primary-foreground py-3 text-lg font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <div className="flex items-center space-x-2 space-x-reverse">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                <span>جاري التحديث...</span>
              </div>
            ) : (
              'تحديث كلمة المرور'
            )}
          </Button>
        </motion.div>

        {/* Security Notice */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl"
        >
          <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">ملاحظة أمنية</h4>
          <p className="text-sm text-blue-700 dark:text-blue-300">
            بعد تغيير كلمة المرور، ستحتاج لتسجيل الدخول مرة أخرى في جميع الأجهزة
          </p>
        </motion.div>
      </form>
    </AuthLayout>
  )
}
