import { useEffect } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { GlobalThemeProvider } from "@/components/theme/GlobalThemeProvider";
import Index from "./pages/Index";
import Dashboard from "./pages/Dashboard";
import Categories from "./pages/dashboard/Categories";
import Notes from "./pages/dashboard/Notes";
import NoteEditor from "./pages/dashboard/NoteEditor";
import Calendar from "./pages/dashboard/Calendar";
import { Notifications } from "./pages/dashboard/Notifications";
import Settings from "./pages/Settings";
import NotFound from "./pages/NotFound";
import { SignUpFlow } from "./components/auth/SignUpFlow";
import { LoginForm } from "./components/auth/LoginForm";
import { ForgotPasswordForm } from "./components/auth/ForgotPasswordForm";
import { ResetPasswordForm } from "./components/auth/ResetPasswordForm";
import { OAuthCallback } from "./components/auth/OAuthCallback";
import { AuthTest } from "./components/auth/AuthTest";
import { useAuthStore } from "./stores/authStore";

const queryClient = new QueryClient();

const App = () => {
  const { initializeAuth } = useAuthStore();

  useEffect(() => {
    initializeAuth();
  }, [initializeAuth]);

  return (
  <QueryClientProvider client={queryClient}>
    <GlobalThemeProvider defaultTheme="system" storageKey="nots-global-theme">
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/dashboard/categories" element={<Categories />} />
          <Route path="/dashboard/notes" element={<Notes />} />
          <Route path="/dashboard/notes/:id" element={<NoteEditor />} />
          <Route path="/dashboard/notifications" element={<Notifications />} />
          <Route path="/dashboard/calendar" element={<Calendar />} />
          <Route path="/settings" element={<Settings />} />
          <Route path="/auth/signup" element={<SignUpFlow />} />
          <Route path="/auth/login" element={<LoginForm />} />
          <Route path="/auth/forgot" element={<ForgotPasswordForm />} />
          <Route path="/auth/reset" element={<ResetPasswordForm />} />
          <Route path="/auth/callback" element={<OAuthCallback />} />
          <Route path="/auth/test" element={<AuthTest />} />
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </GlobalThemeProvider>
  </QueryClientProvider>
  );
};

export default App;
