import { useState } from "react"
import { motion } from "framer-motion"
import {
  Folder,
  Briefcase,
  Book,
  Brain,
  Edit3,
  Target,
  BarChart3,
  Home,
  Heart,
  Star,
  Lightbulb,
  Camera,
  Music,
  Plane,
  ShoppingCart,
  Coffee,
  Gamepad2,
  Dumbbell,
  Car,
  Smartphone,
  Laptop,
  Palette,
  Clock,
  Calendar,
  Lock,
  Crown,
  Eye,
  EyeOff
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { useAuthStore } from "@/stores/authStore"

const icons = [
  // Free icons (10 icons)
  { name: "مجلد", value: "folder", icon: Folder, isFree: true },
  { name: "عمل", value: "briefcase", icon: Briefcase, isFree: true },
  { name: "كتاب", value: "book", icon: Book, isFree: true },
  { name: "دماغ", value: "brain", icon: Brain, isFree: true },
  { name: "تحرير", value: "edit", icon: Edit3, isFree: true },
  { name: "هد<PERSON>", value: "target", icon: Target, isFree: true },
  { name: "منزل", value: "home", icon: Home, isFree: true },
  { name: "قلب", value: "heart", icon: Heart, isFree: true },
  { name: "نجمة", value: "star", icon: Star, isFree: true },
  { name: "فكرة", value: "lightbulb", icon: Lightbulb, isFree: true },

  // Premium icons (14 icons)
  { name: "إحصائيات", value: "bar-chart", icon: BarChart3, isFree: false },
  { name: "كاميرا", value: "camera", icon: Camera, isFree: false },
  { name: "موسيقى", value: "music", icon: Music, isFree: false },
  { name: "طائرة", value: "plane", icon: Plane, isFree: false },
  { name: "تسوق", value: "shopping-cart", icon: ShoppingCart, isFree: false },
  { name: "قهوة", value: "coffee", icon: Coffee, isFree: false },
  { name: "ألعاب", value: "gamepad", icon: Gamepad2, isFree: false },
  { name: "رياضة", value: "dumbbell", icon: Dumbbell, isFree: false },
  { name: "سيارة", value: "car", icon: Car, isFree: false },
  { name: "هاتف", value: "smartphone", icon: Smartphone, isFree: false },
  { name: "لابتوب", value: "laptop", icon: Laptop, isFree: false },
  { name: "فن", value: "palette", icon: Palette, isFree: false },
  { name: "وقت", value: "clock", icon: Clock, isFree: false },
  { name: "تقويم", value: "calendar", icon: Calendar, isFree: false }
]

interface IconSelectorProps {
  value: string
  onChange: (icon: string) => void
}

export function IconSelector({ value, onChange }: IconSelectorProps) {
  const { user } = useAuthStore()
  const [showAllIcons, setShowAllIcons] = useState(false)

  const isPro = user?.plan_choice === 'upgrade'
  const freeIcons = icons.filter(icon => icon.isFree)
  const premiumIcons = icons.filter(icon => !icon.isFree)
  const displayIcons = showAllIcons ? icons : (isPro ? icons : freeIcons)

  const handleIconClick = (icon: typeof icons[0]) => {
    if (icon.isFree || isPro) {
      onChange(icon.value)
    }
  }

  return (
    <div className="space-y-4">
      {/* Icon Grid */}
      <div className="grid grid-cols-6 gap-3 max-h-64 overflow-y-auto p-3 border border-border/30 rounded-xl bg-gradient-to-br from-muted/10 to-accent/5">
        {displayIcons.map((iconItem, index) => {
          const IconComponent = iconItem.icon
          const isSelected = value === iconItem.value
          const isLocked = !iconItem.isFree && !isPro

          return (
            <motion.div
              key={iconItem.value}
              className="relative"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.2, delay: index * 0.02 }}
            >
              <motion.button
                type="button"
                onClick={() => handleIconClick(iconItem)}
                disabled={isLocked}
                className={`
                  relative w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300 group border-2
                  ${isSelected
                    ? 'bg-gradient-to-r from-primary to-secondary text-white shadow-lg scale-110 border-primary/50'
                    : isLocked
                      ? 'bg-muted/30 text-muted-foreground/50 cursor-not-allowed border-border/20 opacity-60'
                      : 'bg-background hover:bg-muted/50 text-muted-foreground hover:text-foreground hover:scale-105 border-border/30 hover:border-border shadow-sm hover:shadow-md'
                  }
                `}
                whileHover={!isLocked ? { scale: isSelected ? 1.1 : 1.05 } : {}}
                whileTap={!isLocked ? { scale: 0.95 } : {}}
                title={isLocked ? `${iconItem.name} - يتطلب الترقية للخطة المدفوعة` : iconItem.name}
              >
                <IconComponent className="w-5 h-5" />

                {/* Lock Overlay for Premium Icons */}
                {isLocked && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="absolute inset-0 flex items-center justify-center bg-black/40 rounded-xl backdrop-blur-sm"
                  >
                    <Lock className="w-4 h-4 text-white" />
                  </motion.div>
                )}

                {/* Premium Badge */}
                {!iconItem.isFree && (
                  <div className="absolute -top-1 -right-1">
                    <Crown className="w-3 h-3 text-yellow-500 drop-shadow-sm" />
                  </div>
                )}

                {/* Selection Ring */}
                {isSelected && (
                  <motion.div
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    className="absolute inset-0 rounded-xl ring-2 ring-white/50 ring-offset-2 ring-offset-primary/20"
                  />
                )}
              </motion.button>
            </motion.div>
          )
        })}
      </div>

      {/* Controls */}
      <div className="flex items-center justify-between">
        {/* Show All Icons Toggle */}
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => setShowAllIcons(!showAllIcons)}
          className="text-xs text-muted-foreground hover:text-foreground transition-colors"
        >
          {showAllIcons ? (
            <>
              <EyeOff className="w-3 h-3 mr-1" />
              إخفاء الأيقونات المدفوعة
            </>
          ) : (
            <>
              <Eye className="w-3 h-3 mr-1" />
              عرض جميع الأيقونات ({premiumIcons.length} مدفوعة)
            </>
          )}
        </Button>

        {/* Upgrade Prompt for Free Users */}
        {!isPro && (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="text-xs text-muted-foreground"
          >
            <span className="flex items-center space-x-1 space-x-reverse">
              <Crown className="w-3 h-3 text-yellow-500" />
              <span>{premiumIcons.length} أيقونة إضافية في النسخة المدفوعة</span>
            </span>
          </motion.div>
        )}
      </div>

      {/* Free vs Pro Info */}
      <div className="grid grid-cols-2 gap-3 text-xs">
        <div className="flex items-center space-x-2 space-x-reverse p-2 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <span className="text-green-700 dark:text-green-300">
            {freeIcons.length} أيقونات مجانية
          </span>
        </div>
        <div className="flex items-center space-x-2 space-x-reverse p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
          <Crown className="w-3 h-3 text-yellow-600" />
          <span className="text-yellow-700 dark:text-yellow-300">
            {premiumIcons.length} أيقونة مدفوعة
          </span>
        </div>
      </div>
    </div>
  )
}
