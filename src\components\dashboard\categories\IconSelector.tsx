import { motion } from "framer-motion"
import { 
  Folder,
  Briefcase,
  Book,
  Brain,
  Edit,
  Target,
  BarChart3,
  Home,
  Heart,
  Star,
  Lightbulb,
  Camera,
  Music,
  Plane,
  ShoppingCart,
  Coffee,
  Gamepad2,
  Dumbbell,
  Car,
  Smartphone,
  Laptop,
  Palette,
  Clock,
  Calendar
} from "lucide-react"

const icons = [
  { name: "مجلد", value: "folder", icon: Folder },
  { name: "عمل", value: "briefcase", icon: Briefcase },
  { name: "كتاب", value: "book", icon: Book },
  { name: "دماغ", value: "brain", icon: Brain },
  { name: "تحرير", value: "edit", icon: Edit },
  { name: "هدف", value: "target", icon: Target },
  { name: "إحصائيات", value: "bar-chart", icon: BarChart3 },
  { name: "منزل", value: "home", icon: Home },
  { name: "قلب", value: "heart", icon: Heart },
  { name: "نجمة", value: "star", icon: Star },
  { name: "فكرة", value: "lightbulb", icon: Lightbulb },
  { name: "كاميرا", value: "camera", icon: Camera },
  { name: "موسيقى", value: "music", icon: Music },
  { name: "طائرة", value: "plane", icon: Plane },
  { name: "تسوق", value: "shopping-cart", icon: ShoppingCart },
  { name: "قهوة", value: "coffee", icon: Coffee },
  { name: "ألعاب", value: "gamepad", icon: Gamepad2 },
  { name: "رياضة", value: "dumbbell", icon: Dumbbell },
  { name: "سيارة", value: "car", icon: Car },
  { name: "هاتف", value: "smartphone", icon: Smartphone },
  { name: "لابتوب", value: "laptop", icon: Laptop },
  { name: "فن", value: "palette", icon: Palette },
  { name: "وقت", value: "clock", icon: Clock },
  { name: "تقويم", value: "calendar", icon: Calendar }
]

interface IconSelectorProps {
  value: string
  onChange: (icon: string) => void
}

export function IconSelector({ value, onChange }: IconSelectorProps) {
  return (
    <div className="grid grid-cols-6 gap-2 max-h-48 overflow-y-auto p-2 border border-border/30 rounded-xl bg-muted/10">
      {icons.map((iconItem, index) => {
        const IconComponent = iconItem.icon
        const isSelected = value === iconItem.value
        
        return (
          <motion.button
            key={iconItem.value}
            type="button"
            onClick={() => onChange(iconItem.value)}
            className={`
              relative w-10 h-10 rounded-lg flex items-center justify-center transition-all duration-200
              ${isSelected 
                ? 'bg-primary text-white shadow-lg scale-110' 
                : 'bg-background hover:bg-muted/50 text-muted-foreground hover:text-foreground hover:scale-105'
              }
            `}
            whileHover={{ scale: isSelected ? 1.1 : 1.05 }}
            whileTap={{ scale: 0.95 }}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: isSelected ? 1.1 : 1 }}
            transition={{ duration: 0.2, delay: index * 0.02 }}
            title={iconItem.name}
          >
            <IconComponent className="w-5 h-5" />
          </motion.button>
        )
      })}
    </div>
  )
}
