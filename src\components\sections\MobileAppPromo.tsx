import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Smartphone,
  Download,
  Star,
  QrCode,
  Monitor,
  Play,
  Zap,
  Shield,
  RefreshCw,
  WifiOff
} from "lucide-react"

const appFeatures = [
  {
    icon: Zap,
    title: "سرعة فائقة",
    description: "أداء محسن للهواتف الذكية"
  },
  {
    icon: Shield,
    title: "أمان متقدم",
    description: "حماية بيانات بتقنية البلوك تشين"
  },
  {
    icon: RefreshCw,
    title: "مزامنة فورية",
    description: "تحديث تلقائي عبر جميع الأجهزة"
  },
  {
    icon: WifiOff,
    title: "عمل بدون إنترنت",
    description: "استخدم التطبيق حتى بدون اتصال"
  }
]

const screenshots = [
  {
    id: 1,
    title: "الشاشة الرئيسية",
    description: "واجهة بديهية وسهلة الاستخدام",
    image: "/screenshots/home.jpg"
  },
  {
    id: 2,
    title: "محرر الملاحظات",
    description: "كتابة وتحرير متقدم",
    image: "/screenshots/editor.jpg"
  },
  {
    id: 3,
    title: "الذكاء الاصطناعي",
    description: "ميزات ذكية متطورة",
    image: "/screenshots/ai.jpg"
  },
  {
    id: 4,
    title: "التنظيم",
    description: "تصنيف وترتيب ذكي",
    image: "/screenshots/organize.jpg"
  }
]

export function MobileAppPromo() {
  return (
    <section className="py-20 bg-nots-bg-light/30" id="mobile-app">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-purple/10 text-primary font-medium mb-6">
            <Smartphone className="w-5 h-5" />
            <span>📲 تطبيق الهاتف</span>
          </div>
          
          <h2 className="text-3xl md:text-5xl font-bold nots-heading mb-6">
            <span className="bg-gradient-purple bg-clip-text text-transparent">Nots</span> في جيبك
            <br />أينما كنت
          </h2>
          
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            حمّل تطبيق Nots على هاتفك واستمتع بجميع الميزات الذكية أثناء التنقل. 
            متاح الآن على iOS و Android مع تقييم 4.9 نجوم.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
          {/* App Info */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            {/* Download Buttons */}
            <div className="space-y-4">
              <h3 className="text-2xl font-bold nots-heading mb-6">
                حمّل التطبيق الآن
              </h3>
              
              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  size="lg"
                  className="bg-app-store hover:bg-app-store/90 text-primary-foreground px-6 py-4 rounded-xl flex items-center gap-3 flex-1"
                >
                  <Monitor className="w-6 h-6" />
                  <div className="text-right">
                    <div className="text-xs">حمّل من</div>
                    <div className="font-semibold">App Store</div>
                  </div>
                </Button>

                <Button
                  size="lg"
                  className="bg-google-play hover:bg-google-play/90 text-primary-foreground px-6 py-4 rounded-xl flex items-center gap-3 flex-1"
                >
                  <Play className="w-6 h-6" />
                  <div className="text-right">
                    <div className="text-xs">حمّل من</div>
                    <div className="font-semibold">Google Play</div>
                  </div>
                </Button>
              </div>
            </div>

            {/* QR Code */}
            <div className="bg-card border border-border/50 rounded-xl p-6 text-center">
              <div className="w-32 h-32 bg-gradient-purple/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                <QrCode className="w-16 h-16 text-primary" />
              </div>
              <h4 className="font-semibold mb-2">مسح سريع للتحميل</h4>
              <p className="text-sm text-muted-foreground">
                امسح الكود للوصول المباشر لصفحة التحميل
              </p>
            </div>

            {/* App Stats */}
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 bg-card border border-border/50 rounded-xl">
                <div className="flex items-center justify-center gap-1 mb-2">
                  <Star className="w-5 h-5 fill-nots-orange text-nots-orange" />
                  <span className="text-2xl font-bold text-primary">4.9</span>
                </div>
                <div className="text-sm text-muted-foreground">تقييم المستخدمين</div>
              </div>
              
              <div className="text-center p-4 bg-card border border-border/50 rounded-xl">
                <div className="text-2xl font-bold text-primary mb-2">50K+</div>
                <div className="text-sm text-muted-foreground">تحميل</div>
              </div>
            </div>

            {/* App Features */}
            <div className="space-y-4">
              <h4 className="font-semibold">ميزات التطبيق المحمول:</h4>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {appFeatures.map((feature, index) => {
                  const Icon = feature.icon
                  return (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      viewport={{ once: true }}
                      className="flex items-center gap-3 p-3 bg-card border border-border/50 rounded-lg"
                    >
                      <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                        <Icon className="w-5 h-5 text-primary" />
                      </div>
                      <div>
                        <div className="font-medium text-sm">{feature.title}</div>
                        <div className="text-xs text-muted-foreground">{feature.description}</div>
                      </div>
                    </motion.div>
                  )
                })}
              </div>
            </div>
          </motion.div>

          {/* Screenshots Carousel */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            viewport={{ once: true }}
            className="relative"
          >
            {/* Phone Frame */}
            <div className="relative mx-auto w-80 h-[600px] bg-phone-frame rounded-[3rem] p-4 shadow-2xl">
              {/* Phone Screen */}
              <div className="w-full h-full bg-phone-screen rounded-[2.5rem] overflow-hidden relative">
                {/* Status Bar */}
                <div className="bg-gradient-purple h-8 flex items-center justify-between px-6 text-white text-xs">
                  <span>9:41</span>
                  <span>Nots</span>
                  <span>100%</span>
                </div>
                
                {/* Screenshots Carousel */}
                <div className="h-full overflow-hidden">
                  <div className="flex h-full animate-slide-carousel">
                    {screenshots.map((screenshot, index) => (
                      <div key={screenshot.id} className="w-full h-full flex-shrink-0 relative">
                        {/* Mock Screenshot Content */}
                        <div className="h-full bg-gradient-to-b from-nots-bg-light to-white p-4 flex flex-col">
                          <div className="text-center mb-6">
                            <h4 className="font-bold text-lg text-primary mb-2">{screenshot.title}</h4>
                            <p className="text-sm text-muted-foreground">{screenshot.description}</p>
                          </div>
                          
                          {/* Mock Content based on screenshot type */}
                          {index === 0 && (
                            <div className="space-y-4">
                              <div className="bg-white rounded-lg p-4 shadow-sm border">
                                <div className="h-4 bg-primary/20 rounded mb-2"></div>
                                <div className="h-3 bg-gray-200 rounded mb-1"></div>
                                <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                              </div>
                              <div className="bg-white rounded-lg p-4 shadow-sm border">
                                <div className="h-4 bg-nots-orange/20 rounded mb-2"></div>
                                <div className="h-3 bg-gray-200 rounded mb-1"></div>
                                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                              </div>
                            </div>
                          )}
                          
                          {index === 1 && (
                            <div className="space-y-3">
                              <div className="h-8 bg-gray-100 rounded"></div>
                              <div className="space-y-2">
                                <div className="h-3 bg-gray-200 rounded"></div>
                                <div className="h-3 bg-gray-200 rounded w-5/6"></div>
                                <div className="h-3 bg-gray-200 rounded w-4/5"></div>
                              </div>
                            </div>
                          )}
                          
                          {index === 2 && (
                            <div className="text-center space-y-4">
                              <div className="w-16 h-16 bg-gradient-purple rounded-full mx-auto flex items-center justify-center">
                                <span className="text-white text-2xl">🧠</span>
                              </div>
                              <div className="space-y-2">
                                <div className="h-3 bg-primary/20 rounded"></div>
                                <div className="h-3 bg-primary/20 rounded w-4/5 mx-auto"></div>
                              </div>
                            </div>
                          )}
                          
                          {index === 3 && (
                            <div className="space-y-3">
                              <div className="flex items-center gap-2">
                                <div className="w-6 h-6 bg-nots-blue/20 rounded"></div>
                                <div className="h-3 bg-gray-200 rounded flex-1"></div>
                              </div>
                              <div className="flex items-center gap-2">
                                <div className="w-6 h-6 bg-nots-green/20 rounded"></div>
                                <div className="h-3 bg-gray-200 rounded flex-1"></div>
                              </div>
                              <div className="flex items-center gap-2">
                                <div className="w-6 h-6 bg-nots-orange/20 rounded"></div>
                                <div className="h-3 bg-gray-200 rounded flex-1"></div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
              
              {/* Home Indicator */}
              <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-white/30 rounded-full"></div>
            </div>
            
            {/* Floating Elements */}
            <div className="absolute -top-4 -right-4">
              <Badge className="bg-nots-green text-white">جديد</Badge>
            </div>
            <div className="absolute -bottom-4 -left-4">
              <Badge className="bg-nots-orange text-white">مجاني</Badge>
            </div>
          </motion.div>
        </div>

        {/* Final CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <div className="bg-gradient-purple rounded-2xl p-8 md:p-12 text-white">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              ابدأ رحلتك مع Nots اليوم
            </h3>
            <p className="text-lg text-white/90 mb-8">
              انضم إلى آلاف المستخدمين الذين يستخدمون Nots لتنظيم أفكارهم وزيادة إنتاجيتهم
            </p>
            <Button 
              size="lg"
              className="bg-white text-primary hover:bg-white/90 px-8 py-4 text-lg font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <Download className="w-5 h-5 mr-2" />
              حمّل التطبيق مجاناً
            </Button>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
