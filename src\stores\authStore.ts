import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { supabase, profileHelpers, getAuthErrorMessage, type Profile } from '@/lib/supabase'
import type { User } from '@supabase/supabase-js'

export interface SignUpFormData {
  // Step 1: Basic Info
  fullName: string
  email: string
  password: string
  confirmPassword: string

  // Step 2: Usage Intent
  usageIntent: 'student' | 'employee' | 'creator' | 'personal' | ''

  // Step 3: Smart Features
  smartFeatures: {
    autoSummarization: boolean
    speechToText: boolean
    ideaExpansion: boolean
    enableAllFeatures: boolean
  }

  // Step 4: Upgrade
  planChoice: 'free' | 'upgrade' | ''

  // Step 5: Initial Setup
  selectedFolders: string[]
  customFolders: string[]

  // OAuth flag
  isOAuthUser?: boolean
}

export interface AuthState {
  // Authentication state
  isAuthenticated: boolean
  user: {
    id: string
    fullName: string
    email: string
    plan: 'free' | 'upgrade'
    profile?: Profile
  } | null

  // Multi-step signup state
  currentStep: number
  signUpData: SignUpFormData
  isLoading: boolean
  error: string | null

  // Actions
  setCurrentStep: (step: number) => void
  updateSignUpData: (data: Partial<SignUpFormData>) => void
  resetSignUpData: () => void
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>
  signInWithGoogle: () => Promise<{ success: boolean; error?: string }>
  logout: () => Promise<void>
  completeSignUp: () => Promise<{ success: boolean; error?: string }>
  forgotPassword: (email: string) => Promise<{ success: boolean; error?: string }>
  resetPassword: (token: string, newPassword: string) => Promise<{ success: boolean; error?: string }>
  initializeAuth: () => Promise<void>
  clearError: () => void
}

const initialSignUpData: SignUpFormData = {
  fullName: '',
  email: '',
  password: '',
  confirmPassword: '',
  usageIntent: '',
  smartFeatures: {
    autoSummarization: false,
    speechToText: false,
    ideaExpansion: false,
    enableAllFeatures: false,
  },
  planChoice: '',
  selectedFolders: [],
  customFolders: [],
  isOAuthUser: false,
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      isAuthenticated: false,
      user: null,
      currentStep: 1,
      signUpData: initialSignUpData,
      isLoading: false,
      error: null,

      setCurrentStep: (step: number) => {
        set({ currentStep: step })
      },

      updateSignUpData: (data: Partial<SignUpFormData>) => {
        set((state) => ({
          signUpData: { ...state.signUpData, ...data }
        }))
      },

      resetSignUpData: () => {
        set({
          signUpData: initialSignUpData,
          currentStep: 1
        })
      },

      clearError: () => {
        set({ error: null })
      },

      login: async (email: string, password: string) => {
        set({ isLoading: true, error: null })

        try {
          const { data, error } = await supabase.auth.signInWithPassword({
            email,
            password,
          })

          if (error) throw error

          // Fetch user profile
          const profile = await profileHelpers.getProfile(data.user.id)

          if (!profile) {
            throw new Error('Profile not found')
          }

          // Update last login
          await profileHelpers.updateLastLogin(data.user.id)

          const user = {
            id: data.user.id,
            fullName: profile.full_name,
            email: profile.email,
            plan: profile.plan_choice,
            profile
          }

          set({
            isAuthenticated: true,
            user,
            isLoading: false,
            error: null
          })

          return { success: true }
        } catch (error: any) {
          const errorMessage = getAuthErrorMessage(error)
          set({
            isLoading: false,
            error: errorMessage,
            isAuthenticated: false,
            user: null
          })
          return { success: false, error: errorMessage }
        }
      },

      signInWithGoogle: async () => {
        set({ isLoading: true, error: null })

        try {
          const { data, error } = await supabase.auth.signInWithOAuth({
            provider: 'google',
            options: {
              redirectTo: `${window.location.origin}/auth/callback`
            }
          })

          if (error) throw error

          // OAuth redirect will handle the rest
          return { success: true }
        } catch (error: any) {
          const errorMessage = getAuthErrorMessage(error)
          set({
            isLoading: false,
            error: errorMessage
          })
          return { success: false, error: errorMessage }
        }
      },

      logout: async () => {
        set({ isLoading: true })

        try {
          await supabase.auth.signOut()

          set({
            isAuthenticated: false,
            user: null,
            signUpData: initialSignUpData,
            currentStep: 1,
            isLoading: false,
            error: null
          })
        } catch (error: any) {
          console.error('Logout error:', error)
          // Force logout even if there's an error
          set({
            isAuthenticated: false,
            user: null,
            signUpData: initialSignUpData,
            currentStep: 1,
            isLoading: false,
            error: null
          })
        }
      },

      completeSignUp: async () => {
        set({ isLoading: true, error: null })

        try {
          const { signUpData } = get()
          let userId: string
          let userEmail: string

          if (signUpData.isOAuthUser) {
            // For OAuth users, get the current session
            const { data: { session } } = await supabase.auth.getSession()
            if (!session?.user) {
              throw new Error('OAuth session not found')
            }
            userId = session.user.id
            userEmail = session.user.email || signUpData.email

            // Update the existing profile with completed data
            const profile = await profileHelpers.updateProfile(userId, {
              usage_intent: signUpData.usageIntent || undefined,
              smart_features: signUpData.smartFeatures,
              plan_choice: signUpData.planChoice || 'free',
              selected_folders: signUpData.selectedFolders,
              custom_folders: signUpData.customFolders,
              profile_completed: true, // Mark as complete only after all steps
              subscription_status: signUpData.planChoice === 'upgrade' ? 'trial' : 'inactive',
              folders_count: signUpData.selectedFolders.length + signUpData.customFolders.length,
            })

            if (!profile) {
              throw new Error('Profile update failed')
            }

            const user = {
              id: userId,
              fullName: profile.full_name,
              email: profile.email,
              plan: profile.plan_choice,
              profile
            }

            set({
              isAuthenticated: true,
              user,
              isLoading: false,
              signUpData: initialSignUpData,
              currentStep: 1,
              error: null
            })

          } else {
            // For email/password users, create new account
            const { data, error } = await supabase.auth.signUp({
              email: signUpData.email,
              password: signUpData.password,
            })

            if (error) throw error
            if (!data.user) throw new Error('User creation failed')

            userId = data.user.id
            userEmail = data.user.email || signUpData.email

            // Create profile
            const profile = await profileHelpers.createProfile({
              id: userId,
              full_name: signUpData.fullName,
              email: userEmail,
              usage_intent: signUpData.usageIntent || undefined,
              smart_features: signUpData.smartFeatures,
              plan_choice: signUpData.planChoice || 'free',
              selected_folders: signUpData.selectedFolders,
              custom_folders: signUpData.customFolders,
              profile_completed: true, // Mark as complete after all steps
              email_verified: true, // Since we disabled email confirmation
              language_preference: 'ar',
              theme_preference: 'system',
              country_code: 'SA',
              subscription_status: signUpData.planChoice === 'upgrade' ? 'trial' : 'inactive',
              notifications_enabled: true,
              failed_login_attempts: 0,
              notes_count: 0,
              folders_count: signUpData.selectedFolders.length + signUpData.customFolders.length,
              metadata: {}
            })

            if (!profile) {
              throw new Error('Profile creation failed')
            }

            const user = {
              id: userId,
              fullName: profile.full_name,
              email: profile.email,
              plan: profile.plan_choice,
              profile
            }

            set({
              isAuthenticated: true,
              user,
              isLoading: false,
              signUpData: initialSignUpData,
              currentStep: 1,
              error: null
            })
          }

          return { success: true }
        } catch (error: any) {
          const errorMessage = getAuthErrorMessage(error)
          set({
            isLoading: false,
            error: errorMessage
          })
          return { success: false, error: errorMessage }
        }
      },

      forgotPassword: async (email: string) => {
        set({ isLoading: true, error: null })

        try {
          const { error } = await supabase.auth.resetPasswordForEmail(email, {
            redirectTo: `${window.location.origin}/auth/reset`
          })

          if (error) throw error

          set({ isLoading: false })
          return { success: true }
        } catch (error: any) {
          const errorMessage = getAuthErrorMessage(error)
          set({
            isLoading: false,
            error: errorMessage
          })
          return { success: false, error: errorMessage }
        }
      },

      resetPassword: async (token: string, newPassword: string) => {
        set({ isLoading: true, error: null })

        try {
          const { error } = await supabase.auth.updateUser({
            password: newPassword
          })

          if (error) throw error

          set({ isLoading: false })
          return { success: true }
        } catch (error: any) {
          const errorMessage = getAuthErrorMessage(error)
          set({
            isLoading: false,
            error: errorMessage
          })
          return { success: false, error: errorMessage }
        }
      },

      initializeAuth: async () => {
        try {
          const { data: { session } } = await supabase.auth.getSession()

          if (session?.user) {
            const profile = await profileHelpers.getProfile(session.user.id)

            if (profile) {
              const user = {
                id: session.user.id,
                fullName: profile.full_name,
                email: profile.email,
                plan: profile.plan_choice,
                profile
              }

              set({
                isAuthenticated: true,
                user,
                error: null
              })
            }
          }

          // Listen for auth changes
          supabase.auth.onAuthStateChange(async (event, session) => {
            if (event === 'SIGNED_IN' && session?.user) {
              const profile = await profileHelpers.getProfile(session.user.id)

              if (profile) {
                const user = {
                  id: session.user.id,
                  fullName: profile.full_name,
                  email: profile.email,
                  plan: profile.plan_choice,
                  profile
                }

                set({
                  isAuthenticated: true,
                  user,
                  error: null
                })
              }
            } else if (event === 'SIGNED_OUT') {
              set({
                isAuthenticated: false,
                user: null,
                error: null
              })
            }
          })
        } catch (error) {
          console.error('Auth initialization error:', error)
        }
      },
    }),
    {
      name: 'nots-auth-storage',
      partialize: (state) => ({
        currentStep: state.currentStep,
        signUpData: state.signUpData,
      }),
    }
  )
)
