import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export interface SignUpFormData {
  // Step 1: Basic Info
  fullName: string
  email: string
  password: string
  confirmPassword: string
  
  // Step 2: Usage Intent
  usageIntent: 'student' | 'employee' | 'creator' | 'personal' | ''
  
  // Step 3: Smart Features
  smartFeatures: {
    autoSummarization: boolean
    speechToText: boolean
    ideaExpansion: boolean
    enableAllFeatures: boolean
  }
  
  // Step 4: Upgrade
  planChoice: 'free' | 'upgrade' | ''
  
  // Step 5: Initial Setup
  selectedFolders: string[]
  customFolders: string[]
}

export interface AuthState {
  // Authentication state
  isAuthenticated: boolean
  user: {
    id: string
    fullName: string
    email: string
    plan: 'free' | 'premium'
  } | null
  
  // Multi-step signup state
  currentStep: number
  signUpData: SignUpFormData
  isLoading: boolean
  
  // Actions
  setCurrentStep: (step: number) => void
  updateSignUpData: (data: Partial<SignUpFormData>) => void
  resetSignUpData: () => void
  login: (email: string, password: string) => Promise<boolean>
  logout: () => void
  completeSignUp: () => Promise<boolean>
  forgotPassword: (email: string) => Promise<boolean>
  resetPassword: (token: string, newPassword: string) => Promise<boolean>
}

const initialSignUpData: SignUpFormData = {
  fullName: '',
  email: '',
  password: '',
  confirmPassword: '',
  usageIntent: '',
  smartFeatures: {
    autoSummarization: false,
    speechToText: false,
    ideaExpansion: false,
    enableAllFeatures: false,
  },
  planChoice: '',
  selectedFolders: [],
  customFolders: [],
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      isAuthenticated: false,
      user: null,
      currentStep: 1,
      signUpData: initialSignUpData,
      isLoading: false,

      setCurrentStep: (step: number) => {
        set({ currentStep: step })
      },

      updateSignUpData: (data: Partial<SignUpFormData>) => {
        set((state) => ({
          signUpData: { ...state.signUpData, ...data }
        }))
      },

      resetSignUpData: () => {
        set({ 
          signUpData: initialSignUpData,
          currentStep: 1 
        })
      },

      login: async (email: string, password: string) => {
        set({ isLoading: true })
        
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1500))
        
        // Simulate login validation
        if (email && password.length >= 6) {
          const user = {
            id: '1',
            fullName: 'مستخدم تجريبي',
            email,
            plan: 'free' as const
          }
          
          set({ 
            isAuthenticated: true, 
            user,
            isLoading: false 
          })
          return true
        }
        
        set({ isLoading: false })
        return false
      },

      logout: () => {
        set({ 
          isAuthenticated: false, 
          user: null,
          signUpData: initialSignUpData,
          currentStep: 1
        })
      },

      completeSignUp: async () => {
        set({ isLoading: true })
        
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        const { signUpData } = get()
        const user = {
          id: Date.now().toString(),
          fullName: signUpData.fullName,
          email: signUpData.email,
          plan: signUpData.planChoice === 'upgrade' ? 'premium' as const : 'free' as const
        }
        
        set({ 
          isAuthenticated: true, 
          user,
          isLoading: false,
          signUpData: initialSignUpData,
          currentStep: 1
        })
        
        return true
      },

      forgotPassword: async (email: string) => {
        set({ isLoading: true })
        
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        set({ isLoading: false })
        
        // Simulate success for valid email format
        return email.includes('@')
      },

      resetPassword: async (token: string, newPassword: string) => {
        set({ isLoading: true })
        
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1500))
        
        set({ isLoading: false })
        
        // Simulate success for valid token and password
        return token.length > 0 && newPassword.length >= 8
      },
    }),
    {
      name: 'nots-auth-storage',
      partialize: (state) => ({
        isAuthenticated: state.isAuthenticated,
        user: state.user,
        currentStep: state.currentStep,
        signUpData: state.signUpData,
      }),
    }
  )
)
