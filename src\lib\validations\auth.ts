import { z } from 'zod'

// Arabic error messages
const errorMessages = {
  required: 'هذا الحقل مطلوب',
  email: 'يرجى إدخال بريد إلكتروني صحيح',
  minLength: (min: number) => `يجب أن يحتوي على ${min} أحرف على الأقل`,
  maxLength: (max: number) => `يجب ألا يتجاوز ${max} حرف`,
  passwordWeak: 'كلمة المرور ضعيفة. يجب أن تحتوي على حرف كبير وصغير ورقم ورمز خاص',
  passwordMismatch: 'كلمات المرور غير متطابقة',
  nameInvalid: 'الاسم يجب أن يحتوي على حروف فقط',
}

// Password strength validation
const passwordSchema = z
  .string()
  .min(8, errorMessages.minLength(8))
  .regex(/[A-Z]/, 'يجب أن تحتوي على حرف كبير واحد على الأقل')
  .regex(/[a-z]/, 'يجب أن تحتوي على حرف صغير واحد على الأقل')
  .regex(/[0-9]/, 'يجب أن تحتوي على رقم واحد على الأقل')
  .regex(/[!@#$%^&*]/, 'يجب أن تحتوي على رمز خاص واحد على الأقل (!@#$%^&*)')

// Step 1: Basic Info Schema
export const basicInfoSchema = z.object({
  fullName: z
    .string()
    .min(2, errorMessages.minLength(2))
    .max(50, errorMessages.maxLength(50))
    .regex(/^[\u0600-\u06FFa-zA-Z\s]+$/, errorMessages.nameInvalid),
  email: z
    .string()
    .email(errorMessages.email),
  password: passwordSchema,
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: errorMessages.passwordMismatch,
  path: ['confirmPassword'],
})

// Step 2: Usage Intent Schema
export const usageIntentSchema = z.object({
  usageIntent: z.enum(['student', 'employee', 'creator', 'personal', ''])
})

// Step 3: Smart Features Schema
export const smartFeaturesSchema = z.object({
  smartFeatures: z.object({
    autoSummarization: z.boolean(),
    speechToText: z.boolean(),
    ideaExpansion: z.boolean(),
    enableAllFeatures: z.boolean(),
  })
})

// Step 4: Upgrade Schema
export const upgradeSchema = z.object({
  planChoice: z.enum(['free', 'upgrade', ''])
})

// Step 5: Initial Setup Schema
export const initialSetupSchema = z.object({
  selectedFolders: z.array(z.string()),
  customFolders: z.array(z.string().min(1, 'اسم المجلد لا يمكن أن يكون فارغاً'))
})

// Login Schema
export const loginSchema = z.object({
  email: z.string().email(errorMessages.email),
  password: z.string().min(1, errorMessages.required),
})

// Forgot Password Schema
export const forgotPasswordSchema = z.object({
  email: z.string().email(errorMessages.email),
})

// Reset Password Schema
export const resetPasswordSchema = z.object({
  newPassword: passwordSchema,
  confirmNewPassword: z.string(),
}).refine((data) => data.newPassword === data.confirmNewPassword, {
  message: errorMessages.passwordMismatch,
  path: ['confirmNewPassword'],
})

// Type exports
export type BasicInfoFormData = z.infer<typeof basicInfoSchema>
export type UsageIntentFormData = z.infer<typeof usageIntentSchema>
export type SmartFeaturesFormData = z.infer<typeof smartFeaturesSchema>
export type UpgradeFormData = z.infer<typeof upgradeSchema>
export type InitialSetupFormData = z.infer<typeof initialSetupSchema>
export type LoginFormData = z.infer<typeof loginSchema>
export type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>
export type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>

// Password strength checker
export const checkPasswordStrength = (password: string): {
  score: number
  label: string
  color: string
} => {
  let score = 0
  
  if (password.length >= 8) score++
  if (/[A-Z]/.test(password)) score++
  if (/[a-z]/.test(password)) score++
  if (/[0-9]/.test(password)) score++
  if (/[!@#$%^&*]/.test(password)) score++
  
  if (score <= 2) {
    return { score, label: 'ضعيفة', color: 'bg-red-500' }
  } else if (score <= 3) {
    return { score, label: 'متوسطة', color: 'bg-yellow-500' }
  } else if (score <= 4) {
    return { score, label: 'جيدة', color: 'bg-blue-500' }
  } else {
    return { score, label: 'قوية جداً', color: 'bg-green-500' }
  }
}
