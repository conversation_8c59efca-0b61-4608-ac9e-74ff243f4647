import { motion } from "framer-motion"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Star, Quote, Play, CheckCircle, MessageSquare } from "lucide-react"

const testimonials = [
  {
    id: 1,
    name: "د. خالد الأحمد",
    role: "أستاذ جامعي",
    avatar: "/avatars/khalid.jpg",
    content: "Nots ثورة حقيقية في عالم التدوين الأكاديمي. الذكاء الاصطناعي يفهم المحتوى العلمي العربي بدقة مذهلة.",
    rating: 5,
    verified: true,
    type: "text",
    university: "جامعة الملك سعود"
  },
  {
    id: 2,
    name: "مريم العتيبي",
    role: "مديرة تسويق",
    avatar: "/avatars/mariam.jpg",
    content: "استخدم Nots في جميع اجتماعاتي. التلخيص التلقائي وتحويل الصوت لنص يوفر عليّ ساعات من العمل يومياً.",
    rating: 5,
    verified: true,
    type: "video",
    company: "شركة الاتصالات السعودية"
  },
  {
    id: 3,
    name: "أحمد الزهراني",
    role: "طالب طب",
    avatar: "/avatars/ahmed-z.jpg",
    content: "كطالب طب، أحتاج لتنظيم كمية هائلة من المعلومات. Nots يساعدني في تلخيص المحاضرات وتنظيم الملاحظات بطريقة مثالية.",
    rating: 5,
    verified: true,
    type: "text",
    university: "جامعة الملك عبدالعزيز"
  },
  {
    id: 4,
    name: "فاطمة الشهري",
    role: "كاتبة ومؤلفة",
    avatar: "/avatars/fatima-s.jpg",
    content: "ميزة توسيع الأفكار غيرت طريقة كتابتي. الآن أستطيع تطوير أفكاري البسيطة إلى مقالات وقصص كاملة.",
    rating: 5,
    verified: true,
    type: "video",
    books: "مؤلفة 3 كتب"
  },
  {
    id: 5,
    name: "محمد الغامدي",
    role: "مطور تطبيقات",
    avatar: "/avatars/mohammed-g.jpg",
    content: "أستخدم Nots لتوثيق أفكار المشاريع والكود. التنظيم التلقائي والبحث الذكي يجعل العمل أكثر كفاءة.",
    rating: 5,
    verified: true,
    type: "text",
    company: "مطور مستقل"
  },
  {
    id: 6,
    name: "نورا القحطاني",
    role: "طالبة هندسة",
    avatar: "/avatars/nora.jpg",
    content: "التطبيق سهل الاستخدام ومفيد جداً للطلاب. تحويل التسجيلات الصوتية للمحاضرات إلى نصوص منظمة ميزة رائعة.",
    rating: 5,
    verified: true,
    type: "text",
    university: "جامعة الملك فهد"
  }
]

export function CommunityTestimonials() {
  return (
    <section className="py-20 bg-background" id="reviews">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-purple/10 text-primary font-medium mb-6">
            <MessageSquare className="w-5 h-5" />
            <span>💬 آراء المستخدمين</span>
          </div>
          
          <h2 className="text-3xl md:text-5xl font-bold nots-heading mb-6">
            ما يقوله <span className="bg-gradient-purple bg-clip-text text-transparent">مجتمعنا</span>
            <br />عن Nots
          </h2>
          
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            تجارب حقيقية من مستخدمين حقيقيين. اكتشف كيف غيّر Nots طريقة عملهم ودراستهم.
          </p>
        </motion.div>

        {/* Masonry Layout for Testimonials */}
        <div className="columns-1 md:columns-2 lg:columns-3 gap-6 space-y-6">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={testimonial.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="break-inside-avoid"
            >
              <Card className="nots-shadow hover:nots-shadow-lg transition-all duration-300 border-border/50 overflow-hidden">
                <CardContent className="p-6">
                  {/* Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <Avatar className="w-12 h-12">
                        <AvatarImage src={testimonial.avatar} alt={testimonial.name} />
                        <AvatarFallback className="bg-gradient-purple text-white">
                          {testimonial.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="flex items-center gap-2">
                          <h4 className="font-semibold text-foreground">{testimonial.name}</h4>
                          {testimonial.verified && (
                            <CheckCircle className="w-4 h-4 text-nots-blue" />
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground">{testimonial.role}</p>
                        {(testimonial.university || testimonial.company || testimonial.books) && (
                          <p className="text-xs text-muted-foreground/80">
                            {testimonial.university || testimonial.company || testimonial.books}
                          </p>
                        )}
                      </div>
                    </div>
                    
                    {testimonial.type === 'video' && (
                      <Button size="sm" variant="ghost" className="text-primary hover:bg-primary/10">
                        <Play className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                  
                  {/* Rating */}
                  <div className="flex items-center gap-1 mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 fill-nots-orange text-nots-orange" />
                    ))}
                  </div>
                  
                  {/* Content */}
                  <div className="relative">
                    <Quote className="absolute -top-2 -right-2 w-6 h-6 text-primary/20" />
                    <p className="text-muted-foreground leading-relaxed pr-4">
                      "{testimonial.content}"
                    </p>
                  </div>
                  
                  {/* Type Badge */}
                  <div className="mt-4 flex justify-between items-center">
                    <Badge 
                      variant="secondary" 
                      className={`${testimonial.type === 'video' ? 'bg-nots-orange/10 text-nots-orange' : 'bg-nots-blue/10 text-nots-blue'}`}
                    >
                      {testimonial.type === 'video' ? 'شهادة فيديو' : 'شهادة مكتوبة'}
                    </Badge>
                    
                    {testimonial.verified && (
                      <Badge variant="secondary" className="bg-nots-green/10 text-nots-green">
                        موثق
                      </Badge>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Stats Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          viewport={{ once: true }}
          className="mt-16 bg-nots-bg-light/50 rounded-2xl p-8 md:p-12"
        >
          <div className="text-center mb-8">
            <h3 className="text-2xl md:text-3xl font-bold nots-heading mb-4">
              أرقام تتحدث عن نفسها
            </h3>
            <p className="text-muted-foreground">
              إحصائيات حقيقية من مجتمع Nots المتنامي
            </p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-primary mb-2">4.9/5</div>
              <div className="text-sm text-muted-foreground">متوسط التقييم</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary mb-2">2,500+</div>
              <div className="text-sm text-muted-foreground">مراجعة إيجابية</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary mb-2">95%</div>
              <div className="text-sm text-muted-foreground">معدل الرضا</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary mb-2">98%</div>
              <div className="text-sm text-muted-foreground">يوصون بالتطبيق</div>
            </div>
          </div>
        </motion.div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <h3 className="text-xl md:text-2xl font-bold nots-heading mb-4">
            انضم إلى آلاف المستخدمين الراضين
          </h3>
          <p className="text-muted-foreground mb-6">
            ابدأ رحلتك مع Nots واكتشف الفرق بنفسك
          </p>
          <Button 
            size="lg"
            className="bg-gradient-purple hover:opacity-90 text-white px-8 py-4 text-lg font-medium rounded-xl nots-shadow-lg hover:nots-shadow-xl transition-all duration-300"
          >
            جرب Nots مجاناً
          </Button>
        </motion.div>
      </div>
    </section>
  )
}
