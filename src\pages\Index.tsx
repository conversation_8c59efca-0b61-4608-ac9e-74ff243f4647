import { ThemeProvider } from "@/components/theme/ThemeProvider"
import { Navigation } from "@/components/layout/Navigation"
import { <PERSON> } from "@/components/sections/Hero"
import { SocialProof } from "@/components/sections/SocialProof"
import { AIFeatures } from "@/components/sections/AIFeatures"
import { PlatformPreview } from "@/components/sections/PlatformPreview"
import { HowItWorks } from "@/components/sections/HowItWorks"
import { TargetAudience } from "@/components/sections/TargetAudience"
import { CommunityTestimonials } from "@/components/sections/CommunityTestimonials"
import { InteractiveDemo } from "@/components/sections/InteractiveDemo"
import { FAQ } from "@/components/sections/FAQ"
import { MobileAppPromo } from "@/components/sections/MobileAppPromo"
import { Footer } from "@/components/layout/Footer"

const Index = () => {
  return (
    <ThemeProvider defaultTheme="system" storageKey="nots-app-theme">
      <div className="min-h-screen bg-background font-tajawal" dir="rtl">
        <Navigation />
        <main>
          <Hero />
          <SocialProof />
          <AIFeatures />
          <PlatformPreview />
          <HowItWorks />
          <TargetAudience />
          <CommunityTestimonials />
          <InteractiveDemo />
          <FAQ />
          <MobileAppPromo />
        </main>
        <Footer />
      </div>
    </ThemeProvider>
  )
}

export default Index