import { useState } from "react"
import { motion } from "framer-motion"
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { 
  FileText,
  Save,
  X,
  Tag,
  Folder,
  Eye
} from "lucide-react"
import type { Note } from "@/lib/supabase"

interface NoteModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (noteData: Partial<Note>) => void
  note?: Note | null
  isViewing?: boolean
}

export function NoteModal({ isOpen, onClose, onSave, note, isViewing = false }: NoteModalProps) {
  const [title, setTitle] = useState(note?.title || "")
  const [content, setContent] = useState(note?.content || "")
  const [category, setCategory] = useState(note?.category || "")
  const [tags, setTags] = useState<string[]>(note?.tags || [])
  const [newTag, setNewTag] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  const handleSave = async () => {
    if (!title.trim()) return
    
    setIsLoading(true)
    try {
      const wordCount = content.split(/\s+/).filter(word => word.length > 0).length
      const readingTime = Math.max(1, Math.ceil(wordCount / 200)) // 200 words per minute
      
      await onSave({
        title: title.trim(),
        content: content.trim(),
        category: category || undefined,
        tags: tags.length > 0 ? tags : undefined,
        wordCount,
        readingTime,
        lastEdited: new Date().toISOString()
      })
      onClose()
    } catch (error) {
      console.error('Error saving note:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()])
      setNewTag("")
    }
  }

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove))
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      handleSave()
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[95vh] overflow-hidden p-0 bg-gradient-to-br from-background via-muted/20 to-accent/10">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-secondary/5 to-transparent pointer-events-none" />
        
        {/* Animated Background Shapes */}
        <motion.div
          animate={{
            rotate: 360,
            scale: [1, 1.1, 1],
          }}
          transition={{
            rotate: { duration: 20, repeat: Infinity, ease: "linear" },
            scale: { duration: 8, repeat: Infinity, ease: "easeInOut" },
          }}
          className="absolute top-10 right-10 w-20 h-20 rounded-full bg-primary/10 blur-xl pointer-events-none"
        />

        <div className="relative z-10">
          {/* Header */}
          <DialogHeader className="p-6 pb-4 border-b border-border/30 bg-gradient-to-r from-muted/30 to-accent/20">
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="flex items-center space-x-4 space-x-reverse"
            >
              <div className="w-12 h-12 bg-gradient-to-r from-primary to-secondary rounded-xl flex items-center justify-center shadow-lg">
                {isViewing ? (
                  <Eye className="w-6 h-6 text-white" />
                ) : (
                  <FileText className="w-6 h-6 text-white" />
                )}
              </div>
              <div>
                <DialogTitle className="text-2xl font-bold text-foreground">
                  {isViewing ? 'عرض الملاحظة' : note ? 'تعديل الملاحظة' : 'إنشاء ملاحظة جديدة'}
                </DialogTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  {isViewing ? 'اقرأ محتوى الملاحظة' : note ? 'قم بتحديث محتوى الملاحظة' : 'اكتب أفكارك وملاحظاتك هنا'}
                </p>
              </div>
            </motion.div>
          </DialogHeader>

          {/* Content */}
          <div className="max-h-[calc(95vh-120px)] overflow-y-auto">
            <div className="p-6 space-y-6">
              {/* Title */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                className="space-y-2"
              >
                <Label htmlFor="title" className="text-sm font-semibold text-foreground">
                  عنوان الملاحظة *
                </Label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="اكتب عنوان الملاحظة..."
                  className="text-lg font-semibold rounded-xl border-border/50 bg-background/50 focus:border-primary/50"
                  onKeyPress={handleKeyPress}
                  readOnly={isViewing}
                />
              </motion.div>

              {/* Category and Tags Row */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Category */}
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                  className="space-y-2"
                >
                  <Label className="text-sm font-semibold text-foreground flex items-center space-x-2 space-x-reverse">
                    <Folder className="w-4 h-4" />
                    <span>الفئة</span>
                  </Label>
                  <Select value={category} onValueChange={setCategory} disabled={isViewing}>
                    <SelectTrigger className="rounded-xl border-border/50 bg-background/50">
                      <SelectValue placeholder="اختر فئة..." />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="work">العمل</SelectItem>
                      <SelectItem value="personal">شخصي</SelectItem>
                      <SelectItem value="study">الدراسة</SelectItem>
                      <SelectItem value="ideas">الأفكار</SelectItem>
                      <SelectItem value="travel">السفر</SelectItem>
                    </SelectContent>
                  </Select>
                </motion.div>

                {/* Tags */}
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  className="space-y-2"
                >
                  <Label className="text-sm font-semibold text-foreground flex items-center space-x-2 space-x-reverse">
                    <Tag className="w-4 h-4" />
                    <span>العلامات</span>
                  </Label>
                  {!isViewing && (
                    <div className="flex space-x-2 space-x-reverse">
                      <Input
                        value={newTag}
                        onChange={(e) => setNewTag(e.target.value)}
                        placeholder="أضف علامة..."
                        className="rounded-xl border-border/50 bg-background/50"
                        onKeyPress={(e) => e.key === 'Enter' && addTag()}
                      />
                      <Button onClick={addTag} variant="outline" className="rounded-xl">
                        إضافة
                      </Button>
                    </div>
                  )}
                  <div className="flex flex-wrap gap-2">
                    {tags.map((tag, index) => (
                      <Badge key={index} variant="secondary" className="flex items-center space-x-1 space-x-reverse">
                        <span>{tag}</span>
                        {!isViewing && (
                          <button onClick={() => removeTag(tag)} className="ml-1">
                            <X className="w-3 h-3" />
                          </button>
                        )}
                      </Badge>
                    ))}
                  </div>
                </motion.div>
              </div>

              {/* Content */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
                className="space-y-2"
              >
                <Label htmlFor="content" className="text-sm font-semibold text-foreground">
                  محتوى الملاحظة
                </Label>
                <Textarea
                  id="content"
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                  placeholder="اكتب محتوى الملاحظة هنا..."
                  className="min-h-[300px] rounded-xl border-border/50 bg-background/50 focus:border-primary/50 resize-none"
                  onKeyPress={handleKeyPress}
                  readOnly={isViewing}
                />
              </motion.div>
            </div>

            {/* Actions */}
            {!isViewing && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.5 }}
                className="flex flex-col sm:flex-row gap-4 justify-end p-6 border-t border-border/30"
              >
                <Button
                  variant="outline"
                  onClick={onClose}
                  disabled={isLoading}
                  className="rounded-xl border-border/50 hover:bg-muted/50"
                >
                  إلغاء
                </Button>
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Button
                    onClick={handleSave}
                    disabled={isLoading || !title.trim()}
                    className="bg-gradient-to-r from-primary to-secondary hover:opacity-90 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                  >
                    {isLoading ? (
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                        <span>جاري الحفظ...</span>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <Save className="w-4 h-4" />
                        <span>{note ? 'حفظ التعديلات' : 'إنشاء الملاحظة'}</span>
                      </div>
                    )}
                  </Button>
                </motion.div>
              </motion.div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
