
import { motion } from "framer-motion"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Heart,
  Sparkles,
  Mail,
  Twitter,
  Instagram,
  Linkedin,
  Youtube,
  MapPin,
  Phone,
  Send
} from "lucide-react"

export function Footer() {
  return (
    <motion.footer
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="bg-card border-t border-border/50 py-16"
    >
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {/* Logo and Description */}
          <div className="lg:col-span-2">
            <motion.div
              whileHover={{ scale: 1.05 }}
              className="flex items-center gap-2 mb-4"
            >
              <div className="w-8 h-8 bg-gradient-purple rounded-lg flex items-center justify-center">
                <Sparkles className="w-5 h-5 text-white" />
              </div>
              <h3 className="text-2xl font-bold nots-heading bg-gradient-purple bg-clip-text text-transparent">
                Nots
              </h3>
            </motion.div>
            <p className="text-muted-foreground nots-body leading-relaxed mb-6 max-w-md">
              تطبيق الملاحظات الذكي المدعوم بالذكاء الاصطناعي.
              حوّل أفكارك إلى ملاحظات منظمة ومفيدة مع دعم كامل للغة العربية.
            </p>

            {/* Newsletter Signup */}
            <div className="space-y-3">
              <h4 className="font-semibold nots-heading">اشترك في النشرة الإخبارية</h4>
              <div className="flex gap-2">
                <Input
                  type="email"
                  placeholder="بريدك الإلكتروني"
                  className="flex-1"
                />
                <Button className="bg-gradient-purple hover:opacity-90 text-white">
                  <Send className="w-4 h-4" />
                </Button>
              </div>
              <p className="text-xs text-muted-foreground">
                احصل على آخر التحديثات والميزات الجديدة
              </p>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="font-semibold nots-heading mb-4">روابط سريعة</h4>
            <ul className="space-y-3">
              {[
                { name: "الميزات الذكية", href: "#ai-features" },
                { name: "كيف يعمل؟", href: "#how-it-works" },
                { name: "حالات الاستخدام", href: "#use-cases" },
                { name: "آراء المستخدمين", href: "#reviews" },
                { name: "الأسئلة الشائعة", href: "#faq" },
                { name: "تحميل التطبيق", href: "#mobile-app" }
              ].map((link) => (
                <li key={link.name}>
                  <a
                    href={link.href}
                    className="text-muted-foreground hover:text-primary transition-colors nots-body"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Company & Support */}
          <div>
            <h4 className="font-semibold nots-heading mb-4">الشركة والدعم</h4>
            <ul className="space-y-3">
              {[
                { name: "من نحن", href: "/about" },
                { name: "سياسة الخصوصية", href: "/privacy" },
                { name: "شروط الاستخدام", href: "/terms" },
                { name: "مركز المساعدة", href: "/help" },
                { name: "تواصل معنا", href: "/contact" },
                { name: "وظائف", href: "/careers" }
              ].map((link) => (
                <li key={link.name}>
                  <a
                    href={link.href}
                    className="text-muted-foreground hover:text-primary transition-colors nots-body"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>

            {/* Contact Info */}
            <div className="mt-6 space-y-2">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <MapPin className="w-4 h-4" />
                <span>الرياض، المملكة العربية السعودية</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Phone className="w-4 h-4" />
                <span>+966 11 123 4567</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Mail className="w-4 h-4" />
                <span><EMAIL></span>
              </div>
            </div>
          </div>
        </div>

        {/* Social Media & Bottom Section */}
        <div className="border-t border-border/50 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center gap-6">
            {/* Social Media */}
            <div className="flex items-center gap-4">
              <span className="text-muted-foreground nots-body">تابعنا على:</span>
              <div className="flex gap-3">
                {[
                  { icon: Twitter, href: "#", color: "hover:text-blue-400" },
                  { icon: Instagram, href: "#", color: "hover:text-pink-500" },
                  { icon: Linkedin, href: "#", color: "hover:text-blue-600" },
                  { icon: Youtube, href: "#", color: "hover:text-red-500" }
                ].map((social, index) => {
                  const Icon = social.icon
                  return (
                    <a
                      key={index}
                      href={social.href}
                      className={`w-10 h-10 bg-muted/50 rounded-lg flex items-center justify-center text-muted-foreground ${social.color} transition-colors hover:bg-muted`}
                    >
                      <Icon className="w-5 h-5" />
                    </a>
                  )
                })}
              </div>
            </div>

            {/* Copyright */}
            <div className="flex flex-col md:flex-row items-center gap-4 text-muted-foreground nots-body">
              <div className="flex items-center gap-2">
                <span>صُنع بـ</span>
                <Heart className="h-4 w-4 text-red-500 fill-current" />
                <span>للمجتمع العربي</span>
              </div>
              <div className="text-center md:text-right">
                © 2024 Nots. جميع الحقوق محفوظة.
              </div>
            </div>
          </div>
        </div>
      </div>
    </motion.footer>
  )
}
