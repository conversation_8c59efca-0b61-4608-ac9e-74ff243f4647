import { Button } from '@/components/ui/button'
import { motion } from 'framer-motion'
import { 
  Calendar,
  CalendarDays,
  Clock,
  List
} from 'lucide-react'

export interface ViewToggleProps {
  currentView: 'month' | 'week' | 'day' | 'list'
  onViewChange?: (view: 'month' | 'week' | 'day' | 'list') => void
}

export function ViewToggle({ currentView, onViewChange }: ViewToggleProps) {
  const views = [
    {
      key: 'month' as const,
      label: 'شهر',
      icon: Calendar
    },
    {
      key: 'week' as const,
      label: 'أسبوع',
      icon: CalendarDays
    },
    {
      key: 'day' as const,
      label: 'يوم',
      icon: Clock
    },
    {
      key: 'list' as const,
      label: 'قائمة',
      icon: List
    }
  ]

  return (
    <div className="flex items-center bg-muted/30 rounded-lg p-1 border border-border/50">
      {views.map((view) => {
        const Icon = view.icon
        const isActive = currentView === view.key
        
        return (
          <motion.div
            key={view.key}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <Button
              variant={isActive ? "default" : "ghost"}
              size="sm"
              onClick={() => onViewChange?.(view.key)}
              className={`relative px-3 py-2 text-sm font-medium transition-all duration-200 ${
                isActive
                  ? 'bg-primary text-white shadow-sm'
                  : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
              }`}
            >
              <Icon className="w-4 h-4 ml-1" />
              {view.label}
              
              {isActive && (
                <motion.div
                  layoutId="activeViewIndicator"
                  className="absolute inset-0 bg-primary rounded-md -z-10"
                  initial={false}
                  transition={{
                    type: "spring",
                    stiffness: 500,
                    damping: 30
                  }}
                />
              )}
            </Button>
          </motion.div>
        )
      })}
    </div>
  )
}
