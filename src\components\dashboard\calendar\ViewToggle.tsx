import { Button } from '@/components/ui/button'
import { motion } from 'framer-motion'
import { 
  Calendar,
  CalendarDays,
  Clock,
  List
} from 'lucide-react'

export interface ViewToggleProps {
  currentView: 'month' | 'week' | 'day' | 'list'
  onViewChange?: (view: 'month' | 'week' | 'day' | 'list') => void
}

export function ViewToggle({ currentView, onViewChange }: ViewToggleProps) {
  const views = [
    {
      key: 'month' as const,
      label: 'شهر',
      icon: Calendar,
      description: 'عرض شهري'
    },
    {
      key: 'week' as const,
      label: 'أسبوع',
      icon: CalendarDays,
      description: 'عرض أسبوعي'
    },
    {
      key: 'day' as const,
      label: 'يوم',
      icon: Clock,
      description: 'عرض يومي'
    },
    {
      key: 'list' as const,
      label: 'قائمة',
      icon: List,
      description: 'عرض القائمة'
    }
  ]

  return (
    <div className="flex items-center bg-muted/20 rounded-xl p-1 border border-border/30 shadow-sm">
      {views.map((view) => {
        const Icon = view.icon
        const isActive = currentView === view.key

        return (
          <motion.div
            key={view.key}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="relative"
          >
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onViewChange?.(view.key)}
              title={view.description}
              className={`relative px-4 py-2 text-sm font-medium transition-all duration-300 rounded-lg ${
                isActive
                  ? 'bg-gradient-to-r from-primary to-secondary text-white shadow-lg'
                  : 'text-muted-foreground hover:text-foreground hover:bg-muted/40'
              }`}
            >
              <Icon className="w-4 h-4 ml-2" />
              {view.label}

              {isActive && (
                <motion.div
                  layoutId="activeViewIndicator"
                  className="absolute inset-0 bg-gradient-to-r from-primary to-secondary rounded-lg -z-10"
                  initial={false}
                  transition={{
                    type: "spring",
                    stiffness: 400,
                    damping: 25
                  }}
                />
              )}
            </Button>
          </motion.div>
        )
      })}
    </div>
  )
}
