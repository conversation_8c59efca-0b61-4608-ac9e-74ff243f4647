import { useState } from "react"
import { motion } from "framer-motion"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Upload, 
  FileText, 
  Mic, 
  Brain, 
  Sparkles, 
  Download,
  Play,
  Pause,
  RotateCcw,
  CheckCircle,
  Zap
} from "lucide-react"

const sampleFiles = [
  {
    id: 1,
    name: "محاضرة_الذكاء_الاصطناعي.pdf",
    type: "pdf",
    size: "2.3 MB",
    description: "محاضرة جامعية عن أساسيات الذكاء الاصطناعي"
  },
  {
    id: 2,
    name: "اجتماع_فريق_التسويق.docx",
    type: "docx", 
    size: "1.1 MB",
    description: "محضر اجتماع فريق التسويق الأسبوعي"
  },
  {
    id: 3,
    name: "أفكار_مشروع_جديد.txt",
    type: "txt",
    size: "0.5 MB",
    description: "مجموعة أفكار لمشروع تطبيق جديد"
  }
]

const demoSteps = [
  {
    id: 1,
    title: "رفع الملف",
    description: "اختر ملفاً أو اسحبه هنا",
    icon: Upload,
    status: "completed"
  },
  {
    id: 2,
    title: "تحليل المحتوى",
    description: "الذكاء الاصطناعي يحلل النص",
    icon: Brain,
    status: "processing"
  },
  {
    id: 3,
    title: "إنتاج الملاحظات",
    description: "إنشاء ملاحظات منظمة وذكية",
    icon: Sparkles,
    status: "pending"
  }
]

export function InteractiveDemo() {
  const [selectedFile, setSelectedFile] = useState<number | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [showResults, setShowResults] = useState(false)
  const [isRecording, setIsRecording] = useState(false)

  const handleFileSelect = (fileId: number) => {
    setSelectedFile(fileId)
    setShowResults(false)
  }

  const handleProcessDemo = () => {
    setIsProcessing(true)
    setShowResults(false)
    
    // Simulate AI processing
    setTimeout(() => {
      setIsProcessing(false)
      setShowResults(true)
    }, 3000)
  }

  const handleReset = () => {
    setSelectedFile(null)
    setIsProcessing(false)
    setShowResults(false)
  }

  const toggleRecording = () => {
    setIsRecording(!isRecording)
  }

  return (
    <section className="py-20 bg-nots-bg-light/30" id="interactive-demo">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-purple/10 text-primary font-medium mb-6">
            <Play className="w-5 h-5" />
            <span>🎮 تجربة تفاعلية</span>
          </div>
          
          <h2 className="text-3xl md:text-5xl font-bold nots-heading mb-6">
            <span className="bg-gradient-purple bg-clip-text text-transparent">جرب الآن</span>
            <br />واكتشف قوة الذكاء الاصطناعي
          </h2>
          
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            لا حاجة للتسجيل! جرب ميزات Nots مباشرة واكتشف كيف يمكن للذكاء الاصطناعي 
            أن يحول ملفاتك إلى ملاحظات منظمة ومفيدة.
          </p>
        </motion.div>

        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Input Section */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <Card className="nots-shadow border-border/50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 nots-heading">
                    <Upload className="w-6 h-6 text-primary" />
                    اختر طريقة الإدخال
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* File Upload Area */}
                  <div className="border-2 border-dashed border-border rounded-lg p-8 text-center hover:border-primary/50 transition-colors">
                    <Upload className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="font-semibold mb-2">ارفع ملفاً</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      PDF, DOCX, TXT - حتى 10 MB
                    </p>
                    <Button variant="outline" className="mb-4">
                      اختر ملف
                    </Button>
                  </div>

                  {/* Sample Files */}
                  <div>
                    <h4 className="font-semibold mb-3">أو جرب ملفات العينة:</h4>
                    <div className="space-y-2">
                      {sampleFiles.map((file) => (
                        <div
                          key={file.id}
                          onClick={() => handleFileSelect(file.id)}
                          className={`p-3 border rounded-lg cursor-pointer transition-all duration-200 ${
                            selectedFile === file.id
                              ? 'border-primary bg-primary/5'
                              : 'border-border hover:border-primary/30'
                          }`}
                        >
                          <div className="flex items-center gap-3">
                            <FileText className="w-5 h-5 text-primary" />
                            <div className="flex-1">
                              <div className="font-medium text-sm">{file.name}</div>
                              <div className="text-xs text-muted-foreground">{file.description}</div>
                            </div>
                            <Badge variant="secondary" className="text-xs">
                              {file.size}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Voice Recording */}
                  <div className="border border-border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-semibold flex items-center gap-2">
                        <Mic className="w-5 h-5 text-primary" />
                        تسجيل صوتي
                      </h4>
                      <Button
                        size="sm"
                        onClick={toggleRecording}
                        className={`${isRecording ? 'bg-red-500 hover:bg-red-600' : 'bg-primary hover:bg-primary/90'}`}
                      >
                        {isRecording ? <Pause className="w-4 h-4" /> : <Mic className="w-4 h-4" />}
                        {isRecording ? 'إيقاف' : 'تسجيل'}
                      </Button>
                    </div>
                    {isRecording && (
                      <div className="flex items-center gap-2 text-sm text-red-500">
                        <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                        جاري التسجيل...
                      </div>
                    )}
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-3">
                    <Button
                      onClick={handleProcessDemo}
                      disabled={!selectedFile || isProcessing}
                      className="flex-1 bg-gradient-purple hover:opacity-90 text-white"
                    >
                      {isProcessing ? (
                        <>
                          <Brain className="w-4 h-4 mr-2 animate-spin" />
                          جاري المعالجة...
                        </>
                      ) : (
                        <>
                          <Zap className="w-4 h-4 mr-2" />
                          جرب الآن
                        </>
                      )}
                    </Button>
                    <Button variant="outline" onClick={handleReset}>
                      <RotateCcw className="w-4 h-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Output Section */}
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
            >
              <Card className="nots-shadow border-border/50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 nots-heading">
                    <Sparkles className="w-6 h-6 text-primary" />
                    النتائج الذكية
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {!selectedFile && !isProcessing && !showResults && (
                    <div className="text-center py-12">
                      <Brain className="w-16 h-16 text-muted-foreground/50 mx-auto mb-4" />
                      <p className="text-muted-foreground">
                        اختر ملفاً أو سجل صوتاً لرؤية النتائج
                      </p>
                    </div>
                  )}

                  {isProcessing && (
                    <div className="space-y-4">
                      {demoSteps.map((step, index) => {
                        const Icon = step.icon
                        const isActive = index <= 1
                        const isCompleted = index === 0
                        
                        return (
                          <div
                            key={step.id}
                            className={`flex items-center gap-3 p-3 rounded-lg ${
                              isActive ? 'bg-primary/5' : 'bg-muted/30'
                            }`}
                          >
                            <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                              isCompleted ? 'bg-nots-green text-white' :
                              isActive ? 'bg-primary text-white' : 'bg-muted text-muted-foreground'
                            }`}>
                              {isCompleted ? <CheckCircle className="w-5 h-5" /> : 
                               isActive ? <Icon className="w-5 h-5 animate-spin" /> :
                               <Icon className="w-5 h-5" />}
                            </div>
                            <div>
                              <div className="font-medium">{step.title}</div>
                              <div className="text-sm text-muted-foreground">{step.description}</div>
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  )}

                  {showResults && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5 }}
                      className="space-y-4"
                    >
                      <div className="bg-nots-green/10 border border-nots-green/20 rounded-lg p-4">
                        <div className="flex items-center gap-2 mb-3">
                          <CheckCircle className="w-5 h-5 text-nots-green" />
                          <span className="font-semibold text-nots-green">تم بنجاح!</span>
                        </div>
                        
                        <div className="space-y-3">
                          <div>
                            <h4 className="font-semibold mb-2">الملخص التلقائي:</h4>
                            <p className="text-sm text-muted-foreground bg-card p-3 rounded border">
                              "تم تحليل المحتوى وإنتاج ملخص شامل يتضمن النقاط الرئيسية والأفكار المهمة. 
                              تم تنظيم المعلومات في فقرات واضحة مع إضافة عناوين فرعية مناسبة."
                            </p>
                          </div>
                          
                          <div>
                            <h4 className="font-semibold mb-2">العلامات المقترحة:</h4>
                            <div className="flex flex-wrap gap-2">
                              <Badge variant="secondary" className="bg-nots-blue/10 text-nots-blue">تعليم</Badge>
                              <Badge variant="secondary" className="bg-nots-orange/10 text-nots-orange">تكنولوجيا</Badge>
                              <Badge variant="secondary" className="bg-nots-green/10 text-nots-green">ذكاء اصطناعي</Badge>
                            </div>
                          </div>
                          
                          <div>
                            <h4 className="font-semibold mb-2">التصنيف المقترح:</h4>
                            <Badge variant="secondary" className="bg-primary/10 text-primary">
                              📚 التعليم والدراسة
                            </Badge>
                          </div>
                        </div>
                      </div>
                      
                      <Button className="w-full" variant="outline">
                        <Download className="w-4 h-4 mr-2" />
                        تحميل الملاحظة المنظمة
                      </Button>
                    </motion.div>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-purple rounded-2xl p-8 md:p-12 text-white">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              أعجبتك التجربة؟
            </h3>
            <p className="text-lg text-white/90 mb-6">
              هذه مجرد لمحة بسيطة عن قوة Nots. ابدأ الآن واكتشف المزيد من الميزات المذهلة.
            </p>
            <Button 
              size="lg"
              className="bg-white text-primary hover:bg-white/90 px-8 py-4 text-lg font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
            >
              ابدأ مع Nots مجاناً
            </Button>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
