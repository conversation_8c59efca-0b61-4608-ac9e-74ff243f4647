import { useEditor, EditorContent } from '@tiptap/react'
import { StarterKit } from '@tiptap/starter-kit'
import { Bold } from '@tiptap/extension-bold'
import { Italic } from '@tiptap/extension-italic'
import { Underline } from '@tiptap/extension-underline'
import { Code } from '@tiptap/extension-code'
import { Link } from '@tiptap/extension-link'
import { Image } from '@tiptap/extension-image'
import { Table } from '@tiptap/extension-table'
import { TableRow } from '@tiptap/extension-table-row'
import { TableCell } from '@tiptap/extension-table-cell'
import { TableHeader } from '@tiptap/extension-table-header'
import { TaskList } from '@tiptap/extension-task-list'
import { TaskItem } from '@tiptap/extension-task-item'
import { Highlight } from '@tiptap/extension-highlight'
import { Blockquote } from '@tiptap/extension-blockquote'
import { BulletList } from '@tiptap/extension-bullet-list'
import { OrderedList } from '@tiptap/extension-ordered-list'
import { Heading } from '@tiptap/extension-heading'
import { Placeholder } from '@tiptap/extension-placeholder'
import { TextAlign } from '@tiptap/extension-text-align'
import { useEffect, useRef } from 'react'
import { motion } from 'framer-motion'

interface TiptapEditorProps {
  content: string
  onContentChange: (content: string) => void
  isRTL?: boolean
  placeholder?: string
}

export function TiptapEditor({ 
  content, 
  onContentChange, 
  isRTL = true,
  placeholder = 'ابدأ الكتابة هنا...'
}: TiptapEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null)

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        // Disable default extensions we're replacing
        bold: false,
        italic: false,
        code: false,
        blockquote: false,
        bulletList: false,
        orderedList: false,
        heading: false,
      }),
      Bold,
      Italic,
      Underline,
      Code,
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-primary hover:text-primary/80 underline cursor-pointer',
        },
      }),
      Image.configure({
        HTMLAttributes: {
          class: 'max-w-full h-auto rounded-lg shadow-sm',
        },
      }),
      Table.configure({
        resizable: true,
        HTMLAttributes: {
          class: 'border-collapse border border-border rounded-lg overflow-hidden',
        },
      }),
      TableRow,
      TableHeader.configure({
        HTMLAttributes: {
          class: 'bg-muted font-semibold border border-border p-2',
        },
      }),
      TableCell.configure({
        HTMLAttributes: {
          class: 'border border-border p-2',
        },
      }),
      TaskList.configure({
        HTMLAttributes: {
          class: 'task-list',
        },
      }),
      TaskItem.configure({
        nested: true,
        HTMLAttributes: {
          class: 'task-item',
        },
      }),
      Highlight.configure({
        HTMLAttributes: {
          class: 'bg-yellow-200 dark:bg-yellow-800 px-1 rounded',
        },
      }),
      Blockquote.configure({
        HTMLAttributes: {
          class: 'border-r-4 border-primary pr-4 italic text-muted-foreground my-4',
        },
      }),
      BulletList.configure({
        HTMLAttributes: {
          class: 'list-disc list-inside space-y-1',
        },
      }),
      OrderedList.configure({
        HTMLAttributes: {
          class: 'list-decimal list-inside space-y-1',
        },
      }),
      Heading.configure({
        levels: [1, 2, 3, 4, 5, 6],
        HTMLAttributes: {
          class: 'font-bold text-foreground',
        },
      }),
      Placeholder.configure({
        placeholder,
        emptyEditorClass: 'is-editor-empty',
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
        alignments: ['left', 'center', 'right'],
        defaultAlignment: isRTL ? 'right' : 'left',
      }),
    ],
    content,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML()
      onContentChange(html)
    },
    editorProps: {
      attributes: {
        class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none min-h-[500px] max-w-none',
        dir: isRTL ? 'rtl' : 'ltr',
        style: 'font-family: "Tajawal", sans-serif;',
      },
    },
  })

  // Expose editor instance globally for toolbar integration
  useEffect(() => {
    if (editor) {
      ;(window as any).tiptapEditorInstance = editor
    }
    return () => {
      ;(window as any).tiptapEditorInstance = null
    }
  }, [editor])

  // Methods for external integration
  const insertImage = (src: string, alt?: string) => {
    if (editor) {
      editor.chain().focus().setImage({ src, alt }).run()
    }
  }

  const insertLink = (href: string, text?: string) => {
    if (editor) {
      if (text) {
        editor.chain().focus().insertContent(`<a href="${href}">${text}</a>`).run()
      } else {
        editor.chain().focus().setLink({ href }).run()
      }
    }
  }

  // Expose methods globally for component integration
  useEffect(() => {
    ;(window as any).tiptapInsertImage = insertImage
    ;(window as any).tiptapInsertLink = insertLink
  }, [editor])

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="tiptap-editor-container"
      ref={editorRef}
    >
      <div className="relative bg-background rounded-xl border border-border/50 shadow-sm overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-muted/10 via-accent/5 to-transparent pointer-events-none" />

        {/* Animated Background Shapes */}
        <motion.div
          animate={{
            scale: [1, 1.1, 1],
            opacity: [0.05, 0.1, 0.05]
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
          }}
          className="absolute top-10 right-10 w-32 h-32 rounded-full bg-primary/10 blur-xl pointer-events-none"
        />

        <div className="relative z-10 overflow-hidden">
          <div className="p-6 focus-within:ring-2 focus-within:ring-primary/20 focus-within:border-primary/30 transition-all duration-200">
            <EditorContent editor={editor} />
          </div>
        </div>
      </div>

      <style jsx>{`
        .tiptap-editor-container .ProseMirror {
          direction: ${isRTL ? 'rtl' : 'ltr'};
          text-align: ${isRTL ? 'right' : 'left'};
          font-family: "Tajawal", sans-serif;
          line-height: 1.6;
          color: hsl(var(--foreground));
        }

        .tiptap-editor-container .ProseMirror.is-editor-empty:first-child::before {
          content: attr(data-placeholder);
          float: left;
          color: hsl(var(--muted-foreground));
          pointer-events: none;
          height: 0;
        }

        .tiptap-editor-container .ProseMirror h1 {
          font-size: 2rem;
          font-weight: 700;
          margin: 1.5rem 0 1rem 0;
          color: hsl(var(--foreground));
        }

        .tiptap-editor-container .ProseMirror h2 {
          font-size: 1.5rem;
          font-weight: 600;
          margin: 1.25rem 0 0.75rem 0;
          color: hsl(var(--foreground));
        }

        .tiptap-editor-container .ProseMirror h3 {
          font-size: 1.25rem;
          font-weight: 600;
          margin: 1rem 0 0.5rem 0;
          color: hsl(var(--foreground));
        }

        .tiptap-editor-container .ProseMirror p {
          margin: 0.75rem 0;
          line-height: 1.6;
        }

        .tiptap-editor-container .ProseMirror ul,
        .tiptap-editor-container .ProseMirror ol {
          padding-${isRTL ? 'right' : 'left'}: 1.5rem;
          margin: 0.75rem 0;
        }

        .tiptap-editor-container .ProseMirror li {
          margin: 0.25rem 0;
        }

        .tiptap-editor-container .ProseMirror blockquote {
          border-${isRTL ? 'right' : 'left'}: 4px solid hsl(var(--primary));
          padding-${isRTL ? 'right' : 'left'}: 1rem;
          margin: 1rem 0;
          font-style: italic;
          color: hsl(var(--muted-foreground));
        }

        .tiptap-editor-container .ProseMirror code {
          background: hsl(var(--muted));
          padding: 0.25rem 0.5rem;
          border-radius: 0.25rem;
          font-family: 'Courier New', monospace;
          font-size: 0.875rem;
        }

        .tiptap-editor-container .ProseMirror pre {
          background: hsl(var(--muted));
          border-radius: 0.5rem;
          padding: 1rem;
          margin: 1rem 0;
          overflow-x: auto;
          border: 1px solid hsl(var(--border));
        }

        .tiptap-editor-container .ProseMirror pre code {
          background: none;
          padding: 0;
          font-family: 'Courier New', monospace;
        }

        .tiptap-editor-container .ProseMirror table {
          border-collapse: collapse;
          margin: 1rem 0;
          width: 100%;
        }

        .tiptap-editor-container .ProseMirror table td,
        .tiptap-editor-container .ProseMirror table th {
          border: 1px solid hsl(var(--border));
          padding: 0.5rem;
          text-align: ${isRTL ? 'right' : 'left'};
        }

        .tiptap-editor-container .ProseMirror table th {
          background: hsl(var(--muted));
          font-weight: 600;
        }

        .tiptap-editor-container .task-list {
          list-style: none;
          padding: 0;
        }

        .tiptap-editor-container .task-item {
          display: flex;
          align-items: flex-start;
          margin: 0.25rem 0;
        }

        .tiptap-editor-container .task-item input[type="checkbox"] {
          margin-${isRTL ? 'left' : 'right'}: 0.5rem;
          margin-top: 0.125rem;
        }
      `}</style>
    </motion.div>
  )
}
