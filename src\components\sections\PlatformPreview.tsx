import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Edit3,
  Filter,
  Moon,
  RefreshCw,
  Search,
  FileText,
  Folder,
  Settings,
  Play,
  Maximize2
} from "lucide-react"

const platformFeatures = [
  {
    id: 1,
    icon: Edit3,
    title: "محرر الملاحظات",
    description: "محرر نصوص متقدم مع دعم كامل للعربية والتنسيق الغني"
  },
  {
    id: 2,
    icon: Filter,
    title: "الفلترة الذكية",
    description: "ابحث وصنف ملاحظاتك بسهولة باستخدام الفلاتر المتقدمة"
  },
  {
    id: 3,
    icon: Moon,
    title: "الوضع المظلم",
    description: "واجهة مريحة للعينين مع إمكانية التبديل بين الأوضاع"
  },
  {
    id: 4,
    icon: RefreshCw,
    title: "المزامنة السحابية",
    description: "وصول لملاحظاتك من أي جهاز مع مزامنة فورية وآمنة"
  }
]

export function PlatformPreview() {
  return (
    <section className="py-20 bg-nots-bg-light/30" id="platform-preview">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-purple/10 text-primary font-medium mb-6">
            <Maximize2 className="w-5 h-5" />
            <span>📱 معاينة المنصة</span>
          </div>
          
          <h2 className="text-3xl md:text-5xl font-bold nots-heading mb-6">
            واجهة <span className="bg-gradient-purple bg-clip-text text-transparent">بديهية</span>
            <br />وتجربة استخدام مميزة
          </h2>
          
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            تصميم عصري ومدروس يجعل تدوين الملاحظات متعة حقيقية. 
            كل ميزة مصممة لتوفير أفضل تجربة للمستخدم العربي.
          </p>
        </motion.div>

        {/* Main Platform Preview */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
          className="relative mb-16"
        >
          {/* Mock App Interface */}
          <div className="bg-card rounded-2xl nots-shadow-lg border border-border/50 overflow-hidden max-w-6xl mx-auto">
            {/* App Header */}
            <div className="bg-gradient-purple p-4 flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                  <FileText className="w-5 h-5 text-white" />
                </div>
                <h3 className="text-white font-semibold">Nots - ملاحظاتي</h3>
              </div>
              <div className="flex items-center gap-2">
                <Button size="sm" variant="ghost" className="text-white hover:bg-white/20">
                  <Settings className="w-4 h-4" />
                </Button>
                <Button size="sm" variant="ghost" className="text-white hover:bg-white/20">
                  <Search className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* App Content */}
            <div className="flex">
              {/* Sidebar */}
              <div className="w-64 bg-muted/30 p-4 border-l border-border/50">
                <div className="space-y-2">
                  <div className="flex items-center gap-2 p-2 bg-primary/10 rounded-lg text-primary">
                    <Folder className="w-4 h-4" />
                    <span className="text-sm font-medium">جميع الملاحظات</span>
                  </div>
                  <div className="flex items-center gap-2 p-2 hover:bg-muted/50 rounded-lg text-muted-foreground">
                    <Folder className="w-4 h-4" />
                    <span className="text-sm">العمل</span>
                  </div>
                  <div className="flex items-center gap-2 p-2 hover:bg-muted/50 rounded-lg text-muted-foreground">
                    <Folder className="w-4 h-4" />
                    <span className="text-sm">الدراسة</span>
                  </div>
                  <div className="flex items-center gap-2 p-2 hover:bg-muted/50 rounded-lg text-muted-foreground">
                    <Folder className="w-4 h-4" />
                    <span className="text-sm">أفكار شخصية</span>
                  </div>
                </div>
              </div>

              {/* Main Content */}
              <div className="flex-1 p-6">
                <div className="space-y-4">
                  {/* Note Card 1 */}
                  <div className="bg-card border border-border/50 rounded-lg p-4 hover:nots-shadow transition-all duration-300">
                    <div className="flex items-start justify-between mb-3">
                      <h4 className="font-semibold text-foreground">خطة المشروع الجديد</h4>
                      <Badge variant="secondary" className="bg-nots-blue/10 text-nots-blue">عمل</Badge>
                    </div>
                    <p className="text-muted-foreground text-sm mb-3">
                      تطوير تطبيق جديد للتجارة الإلكترونية مع التركيز على تجربة المستخدم...
                    </p>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <span>تم التحديث منذ ساعتين</span>
                      <span>•</span>
                      <span>تم إنشاؤها بواسطة الذكاء الاصطناعي</span>
                    </div>
                  </div>

                  {/* Note Card 2 */}
                  <div className="bg-card border border-border/50 rounded-lg p-4 hover:nots-shadow transition-all duration-300">
                    <div className="flex items-start justify-between mb-3">
                      <h4 className="font-semibold text-foreground">ملاحظات المحاضرة - الذكاء الاصطناعي</h4>
                      <Badge variant="secondary" className="bg-nots-green/10 text-nots-green">دراسة</Badge>
                    </div>
                    <p className="text-muted-foreground text-sm mb-3">
                      مقدمة في التعلم الآلي والشبكات العصبية. التطبيقات العملية في الحياة اليومية...
                    </p>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <span>تم التحديث أمس</span>
                      <span>•</span>
                      <span>تم التلخيص تلقائياً</span>
                    </div>
                  </div>

                  {/* Note Card 3 */}
                  <div className="bg-card border border-border/50 rounded-lg p-4 hover:nots-shadow transition-all duration-300">
                    <div className="flex items-start justify-between mb-3">
                      <h4 className="font-semibold text-foreground">أفكار لتطوير المدونة</h4>
                      <Badge variant="secondary" className="bg-nots-orange/10 text-nots-orange">شخصي</Badge>
                    </div>
                    <p className="text-muted-foreground text-sm mb-3">
                      مواضيع جديدة للكتابة: التكنولوجيا في التعليم، تطوير المهارات الشخصية...
                    </p>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <span>تم التحديث منذ 3 أيام</span>
                      <span>•</span>
                      <span>تم توسيعها بالذكاء الاصطناعي</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Interactive Hotspots */}
          <div className="absolute top-20 left-8">
            <motion.div
              initial={{ scale: 0 }}
              whileInView={{ scale: 1 }}
              transition={{ duration: 0.5, delay: 0.8 }}
              viewport={{ once: true }}
              className="bg-primary text-white p-2 rounded-full shadow-lg cursor-pointer hover:scale-110 transition-transform"
            >
              <Play className="w-4 h-4" />
            </motion.div>
          </div>
        </motion.div>

        {/* Platform Features */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {platformFeatures.map((feature, index) => {
            const Icon = feature.icon
            return (
              <motion.div
                key={feature.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="text-center p-6 nots-shadow hover:nots-shadow-lg transition-all duration-300 border-border/50 group">
                  <CardContent className="p-0">
                    <div className="w-16 h-16 bg-gradient-purple/10 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                      <Icon className="w-8 h-8 text-primary" />
                    </div>
                    <h3 className="font-semibold text-foreground mb-2">{feature.title}</h3>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      {feature.description}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            )
          })}
        </div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <Button 
            size="lg"
            className="bg-gradient-purple hover:opacity-90 text-white px-8 py-4 text-lg font-medium rounded-xl nots-shadow-lg hover:nots-shadow-xl transition-all duration-300"
          >
            جرب المنصة مجاناً
            <Play className="w-5 h-5 mr-2" />
          </Button>
        </motion.div>
      </div>
    </section>
  )
}
