import { useState } from "react"
import { motion } from "framer-motion"
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Calendar, 
  ChevronLeft, 
  ChevronRight, 
  ArrowLeft,
  StickyNote,
  Clock
} from "lucide-react"

// Mock calendar data
const currentDate = new Date()
const currentMonth = currentDate.getMonth()
const currentYear = currentDate.getFullYear()

// Arabic month names
const arabicMonths = [
  'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
  'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
]

// Arabic day names
const arabicDays = ['ح', 'ن', 'ث', 'ر', 'خ', 'ج', 'س']

// Mock notes data for calendar
const notesData: { [key: string]: number } = {
  '2024-01-15': 3,
  '2024-01-16': 1,
  '2024-01-18': 2,
  '2024-01-20': 4,
  '2024-01-22': 1,
  '2024-01-25': 2,
  '2024-01-28': 3
}

// Recent notes for today
const todayNotes = [
  {
    id: '1',
    title: 'ملاحظات اجتماع الفريق',
    time: '10:30 ص',
    category: 'عمل'
  },
  {
    id: '2',
    title: 'أفكار مشروع جديد',
    time: '02:15 م',
    category: 'مشاريع'
  },
  {
    id: '3',
    title: 'قائمة مهام الأسبوع',
    time: '04:45 م',
    category: 'شخصي'
  }
]

export function CalendarPreview() {
  const [selectedDate, setSelectedDate] = useState(currentDate.getDate())
  
  // Get days in current month
  const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate()
  const firstDayOfMonth = new Date(currentYear, currentMonth, 1).getDay()
  
  // Create calendar grid
  const calendarDays = []
  
  // Add empty cells for days before month starts
  for (let i = 0; i < firstDayOfMonth; i++) {
    calendarDays.push(null)
  }
  
  // Add days of the month
  for (let day = 1; day <= daysInMonth; day++) {
    calendarDays.push(day)
  }
  
  const getNotesCount = (day: number) => {
    const dateKey = `${currentYear}-${String(currentMonth + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`
    return notesData[dateKey] || 0
  }
  
  const isToday = (day: number) => {
    return day === currentDate.getDate()
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.6 }}
      className="grid grid-cols-1 lg:grid-cols-2 gap-6"
    >
      {/* Calendar */}
      <Card className="border-border/50">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center space-x-2 space-x-reverse">
            <Calendar className="w-5 h-5 text-primary" />
            <span>التقويم</span>
          </CardTitle>
          <Button variant="ghost" size="sm" className="text-primary hover:text-primary/80">
            عرض التقويم الكامل
            <ArrowLeft className="w-4 h-4 mr-2" />
          </Button>
        </CardHeader>
        
        <CardContent>
          {/* Month Header */}
          <div className="flex items-center justify-between mb-4">
            <Button variant="ghost" size="icon" className="w-8 h-8">
              <ChevronRight className="w-4 h-4" />
            </Button>
            
            <h3 className="text-lg font-semibold text-foreground">
              {arabicMonths[currentMonth]} {currentYear}
            </h3>
            
            <Button variant="ghost" size="icon" className="w-8 h-8">
              <ChevronLeft className="w-4 h-4" />
            </Button>
          </div>
          
          {/* Days Header */}
          <div className="grid grid-cols-7 gap-1 mb-2">
            {arabicDays.map((day) => (
              <div
                key={day}
                className="h-8 flex items-center justify-center text-xs font-medium text-muted-foreground"
              >
                {day}
              </div>
            ))}
          </div>
          
          {/* Calendar Grid */}
          <div className="grid grid-cols-7 gap-1">
            {calendarDays.map((day, index) => {
              if (day === null) {
                return <div key={index} className="h-10" />
              }
              
              const notesCount = getNotesCount(day)
              const isCurrentDay = isToday(day)
              const isSelected = day === selectedDate
              
              return (
                <motion.button
                  key={day}
                  onClick={() => setSelectedDate(day)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className={`
                    relative h-10 flex items-center justify-center text-sm rounded-lg transition-all duration-200
                    ${isCurrentDay 
                      ? 'bg-primary text-primary-foreground font-bold' 
                      : isSelected
                        ? 'bg-primary/20 text-primary font-medium'
                        : 'hover:bg-muted/50 text-foreground'
                    }
                  `}
                >
                  {day}
                  
                  {notesCount > 0 && (
                    <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 text-white text-xs rounded-full flex items-center justify-center">
                      {notesCount}
                    </div>
                  )}
                </motion.button>
              )
            })}
          </div>
          
          {/* Legend */}
          <div className="mt-4 pt-4 border-t border-border/30">
            <div className="flex items-center justify-center space-x-4 space-x-reverse text-xs text-muted-foreground">
              <div className="flex items-center space-x-1 space-x-reverse">
                <div className="w-3 h-3 bg-primary rounded-full" />
                <span>اليوم</span>
              </div>
              <div className="flex items-center space-x-1 space-x-reverse">
                <div className="w-3 h-3 bg-green-500 rounded-full" />
                <span>يوم به ملاحظات</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Today's Notes */}
      <Card className="border-border/50">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 space-x-reverse">
            <StickyNote className="w-5 h-5 text-primary" />
            <span>ملاحظات اليوم</span>
          </CardTitle>
        </CardHeader>
        
        <CardContent>
          {todayNotes.length > 0 ? (
            <div className="space-y-3">
              {todayNotes.map((note, index) => (
                <motion.div
                  key={note.id}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="flex items-center space-x-3 space-x-reverse p-3 rounded-lg hover:bg-muted/30 transition-colors cursor-pointer group"
                >
                  <div className="w-2 h-2 bg-primary rounded-full flex-shrink-0" />
                  
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-foreground truncate">
                      {note.title}
                    </h4>
                    <div className="flex items-center space-x-2 space-x-reverse text-sm text-muted-foreground">
                      <Clock className="w-3 h-3" />
                      <span>{note.time}</span>
                      <span>•</span>
                      <Badge variant="secondary" className="text-xs">
                        {note.category}
                      </Badge>
                    </div>
                  </div>
                </motion.div>
              ))}
              
              <div className="pt-3 border-t border-border/30">
                <Button variant="ghost" size="sm" className="w-full text-primary hover:text-primary/80">
                  عرض جميع ملاحظات اليوم
                  <ArrowLeft className="w-4 h-4 mr-2" />
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <StickyNote className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>لا توجد ملاحظات لهذا اليوم</p>
              <p className="text-sm">ابدأ بإنشاء ملاحظة جديدة!</p>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  )
}
