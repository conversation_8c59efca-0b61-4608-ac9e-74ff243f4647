import { useEffect, useRef, useState } from 'react'
import EditorJ<PERSON>, { OutputData } from '@editorjs/editorjs'
import Header from '@editorjs/header'
import Paragraph from '@editorjs/paragraph'
import List from '@editorjs/list'
import Quote from '@editorjs/quote'
import Code from '@editorjs/code'
import { motion } from "framer-motion"

interface EditorJSContentProps {
  content: string | OutputData
  onContentChange: (content: OutputData) => void
  isRTL?: boolean
  onImageInsert?: (src: string, alt?: string) => void
  onLinkInsert?: (href: string, text?: string) => void
}

// Custom Image Block for Editor.js that integrates with TipTap image functionality
class CustomImageTool {
  static get toolbox() {
    return {
      title: 'صورة',
      icon: '<svg width="17" height="15" viewBox="0 0 336 276" xmlns="http://www.w3.org/2000/svg"><path d="M291 150V79c0-19-15-34-34-34H79c-19 0-34 15-34 34v42l67-44 81 72 56-29 42 30zm0 52l-43-30-56 30-81-67-67 49v23c0 19 15 34 34 34h178c17 0 31-13 34-29zM79 0h178c44 0 79 35 79 79v118c0 44-35 79-79 79H79c-44 0-79-35-79-79V79C0 35 35 0 79 0z"/></svg>'
    }
  }

  constructor({ data, api, config }: any) {
    this.data = data
    this.api = api
    this.config = config
  }

  render() {
    const wrapper = document.createElement('div')
    wrapper.classList.add('custom-image-block')
    
    if (this.data.src) {
      wrapper.innerHTML = `
        <div class="image-container" style="text-align: center; margin: 1rem 0;">
          <img src="${this.data.src}" alt="${this.data.alt || ''}" style="max-width: 100%; height: auto; border-radius: 8px;" />
          ${this.data.alt ? `<p style="margin-top: 0.5rem; font-size: 0.875rem; color: #666;">${this.data.alt}</p>` : ''}
        </div>
      `
    } else {
      wrapper.innerHTML = `
        <div class="image-placeholder" style="text-align: center; padding: 2rem; border: 2px dashed #ccc; border-radius: 8px; margin: 1rem 0;">
          <p style="color: #666;">انقر لإضافة صورة</p>
        </div>
      `
      wrapper.addEventListener('click', () => {
        // Trigger TipTap image modal
        if (this.config.onImageInsert) {
          this.config.onImageInsert()
        }
      })
    }

    return wrapper
  }

  save() {
    return this.data
  }
}

// Custom Link Block for Editor.js that integrates with TipTap link functionality
class CustomLinkTool {
  static get toolbox() {
    return {
      title: 'رابط',
      icon: '<svg width="15" height="14" xmlns="http://www.w3.org/2000/svg"><path d="M12.5 7l-3 3L8 8.5l3-3L8 3l1.5-1.5 3 3c.83.83.83 2.17 0 3zM2.5 7l3-3L7 5.5l-3 3L7 11l-1.5 1.5-3-3c-.83-.83-.83-2.17 0-3z"/></svg>'
    }
  }

  constructor({ data, api, config }: any) {
    this.data = data
    this.api = api
    this.config = config
  }

  render() {
    const wrapper = document.createElement('div')
    wrapper.classList.add('custom-link-block')
    
    if (this.data.href) {
      wrapper.innerHTML = `
        <div class="link-container" style="margin: 1rem 0; padding: 1rem; border: 1px solid #e2e8f0; border-radius: 8px; background: #f8fafc;">
          <a href="${this.data.href}" target="_blank" rel="noopener noreferrer" style="color: #3b82f6; text-decoration: none; font-weight: 500;">
            ${this.data.text || this.data.href}
          </a>
          ${this.data.text && this.data.text !== this.data.href ? `<p style="margin-top: 0.25rem; font-size: 0.875rem; color: #666;">${this.data.href}</p>` : ''}
        </div>
      `
    } else {
      wrapper.innerHTML = `
        <div class="link-placeholder" style="text-align: center; padding: 1rem; border: 2px dashed #ccc; border-radius: 8px; margin: 1rem 0;">
          <p style="color: #666;">انقر لإضافة رابط</p>
        </div>
      `
      wrapper.addEventListener('click', () => {
        // Trigger TipTap link modal
        if (this.config.onLinkInsert) {
          this.config.onLinkInsert()
        }
      })
    }

    return wrapper
  }

  save() {
    return this.data
  }
}

export function EditorJSContent({ 
  content, 
  onContentChange, 
  isRTL = true,
  onImageInsert,
  onLinkInsert 
}: EditorJSContentProps) {
  const editorRef = useRef<EditorJS | null>(null)
  const holderRef = useRef<HTMLDivElement>(null)
  const [isReady, setIsReady] = useState(false)

  // Convert TipTap HTML content to Editor.js format
  const convertHtmlToEditorJS = (html: string): OutputData => {
    if (!html || html.trim() === '') {
      return {
        time: Date.now(),
        blocks: [],
        version: "2.28.2"
      }
    }

    // Basic HTML to Editor.js conversion
    const blocks: any[] = []
    const parser = new DOMParser()
    const doc = parser.parseFromString(html, 'text/html')
    
    // Convert paragraphs
    const paragraphs = doc.querySelectorAll('p')
    paragraphs.forEach((p) => {
      if (p.textContent?.trim()) {
        blocks.push({
          id: Math.random().toString(36).substr(2, 9),
          type: 'paragraph',
          data: {
            text: p.innerHTML
          }
        })
      }
    })

    // Convert headers
    const headers = doc.querySelectorAll('h1, h2, h3, h4, h5, h6')
    headers.forEach((h) => {
      if (h.textContent?.trim()) {
        blocks.push({
          id: Math.random().toString(36).substr(2, 9),
          type: 'header',
          data: {
            text: h.textContent,
            level: parseInt(h.tagName.charAt(1))
          }
        })
      }
    })

    // Convert images
    const images = doc.querySelectorAll('img')
    images.forEach((img) => {
      blocks.push({
        id: Math.random().toString(36).substr(2, 9),
        type: 'customImage',
        data: {
          src: img.src,
          alt: img.alt
        }
      })
    })

    // Convert links
    const links = doc.querySelectorAll('a')
    links.forEach((link) => {
      blocks.push({
        id: Math.random().toString(36).substr(2, 9),
        type: 'customLink',
        data: {
          href: link.href,
          text: link.textContent
        }
      })
    })

    return {
      time: Date.now(),
      blocks,
      version: "2.28.2"
    }
  }

  // Initialize Editor.js
  useEffect(() => {
    if (!holderRef.current) return

    const initData = typeof content === 'string' 
      ? convertHtmlToEditorJS(content)
      : content

    editorRef.current = new EditorJS({
      holder: holderRef.current,
      data: initData,
      placeholder: 'ابدأ الكتابة هنا...',
      tools: {
        header: {
          class: Header,
          config: {
            placeholder: 'أدخل العنوان...',
            levels: [1, 2, 3, 4, 5, 6],
            defaultLevel: 2
          }
        },
        paragraph: {
          class: Paragraph,
          inlineToolbar: true,
          config: {
            placeholder: 'اكتب النص هنا...'
          }
        },
        list: {
          class: List,
          inlineToolbar: true,
          config: {
            defaultStyle: 'unordered'
          }
        },
        quote: {
          class: Quote,
          inlineToolbar: true,
          config: {
            quotePlaceholder: 'أدخل الاقتباس...',
            captionPlaceholder: 'المؤلف'
          }
        },
        code: {
          class: Code,
          config: {
            placeholder: 'أدخل الكود هنا...'
          }
        },
        customImage: {
          class: CustomImageTool,
          config: {
            onImageInsert
          }
        },
        customLink: {
          class: CustomLinkTool,
          config: {
            onLinkInsert
          }
        }
      },
      onChange: async () => {
        if (editorRef.current) {
          try {
            const outputData = await editorRef.current.save()
            onContentChange(outputData)
          } catch (error) {
            console.error('Error saving editor content:', error)
          }
        }
      },
      i18n: {
        direction: isRTL ? 'rtl' : 'ltr'
      }
    })

    editorRef.current.isReady.then(() => {
      setIsReady(true)
      // Expose editor instance globally for toolbar integration
      ;(window as any).editorJSInstance = editorRef.current
    })

    return () => {
      if (editorRef.current && editorRef.current.destroy) {
        editorRef.current.destroy()
      }
      // Clean up global reference
      ;(window as any).editorJSInstance = null
    }
  }, [])

  // Method to insert image block from TipTap
  const insertImageBlock = (src: string, alt?: string) => {
    if (editorRef.current && isReady) {
      editorRef.current.blocks.insert('customImage', {
        src,
        alt
      })
    }
  }

  // Method to insert link block from TipTap
  const insertLinkBlock = (href: string, text?: string) => {
    if (editorRef.current && isReady) {
      editorRef.current.blocks.insert('customLink', {
        href,
        text
      })
    }
  }

  // Expose methods for TipTap integration
  useEffect(() => {
    if (onImageInsert) {
      (window as any).editorJSInsertImage = insertImageBlock
    }
    if (onLinkInsert) {
      (window as any).editorJSInsertLink = insertLinkBlock
    }
  }, [isReady])

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className={`editor-js-container ${isRTL ? 'rtl' : 'ltr'}`}
    >
      <div className="relative bg-background rounded-xl border border-border/50 shadow-sm overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-muted/10 via-accent/5 to-transparent pointer-events-none" />

        {/* Animated Background Shapes */}
        <motion.div
          animate={{
            scale: [1, 1.1, 1],
            opacity: [0.05, 0.1, 0.05]
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
          }}
          className="absolute top-10 right-10 w-32 h-32 rounded-full bg-primary/10 blur-xl pointer-events-none"
        />

        <div className="relative z-10 overflow-hidden">
          <div
            ref={holderRef}
            className="editor-js-holder min-h-[500px] max-h-[calc(100vh-200px)] p-6 focus-within:ring-2 focus-within:ring-primary/20 focus-within:border-primary/30 transition-all duration-200"
            style={{ direction: isRTL ? 'rtl' : 'ltr' }}
          />
        </div>
      </div>
      
      <style jsx>{`
        .editor-js-container .ce-block__content {
          max-width: none;
        }

        .editor-js-container .ce-toolbar__content {
          max-width: none;
        }

        .editor-js-container .codex-editor {
          direction: ${isRTL ? 'rtl' : 'ltr'};
          font-family: "Tajawal", sans-serif;
        }

        .editor-js-container .ce-block {
          text-align: ${isRTL ? 'right' : 'left'};
        }

        .editor-js-container .ce-paragraph {
          line-height: 1.6;
          font-size: 16px;
        }

        .editor-js-container .ce-header {
          font-weight: 600;
          margin: 1rem 0;
          color: hsl(var(--foreground));
        }

        .editor-js-container .ce-quote {
          border-right: 4px solid hsl(var(--primary));
          padding-right: 1rem;
          font-style: italic;
          color: hsl(var(--muted-foreground));
        }

        .editor-js-container .ce-code {
          background: hsl(var(--muted));
          border-radius: 0.5rem;
          padding: 1rem;
          font-family: 'Courier New', monospace;
          border: 1px solid hsl(var(--border));
        }

        .editor-js-container .cdx-list {
          padding-right: ${isRTL ? '1.5rem' : '0'};
          padding-left: ${isRTL ? '0' : '1.5rem'};
        }
      `}</style>
    </motion.div>
  )
}
