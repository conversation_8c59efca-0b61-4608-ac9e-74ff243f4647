import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Repeat, 
  Calendar, 
  Clock,
  Settings,
  Plus,
  Trash2
} from 'lucide-react'

export interface RecurrenceRule {
  frequency: 'daily' | 'weekly' | 'monthly' | 'yearly' | 'custom'
  interval: number
  daysOfWeek?: number[] // 0 = Sunday, 1 = Monday, etc.
  dayOfMonth?: number
  monthOfYear?: number
  endDate?: Date
  count?: number // number of occurrences
  exceptions?: Date[] // dates to skip
}

export interface RecurringEventManagerProps {
  isOpen: boolean
  onClose: () => void
  onSave: (rule: RecurrenceRule) => void
  initialRule?: RecurrenceRule
}

export function RecurringEventManager({ 
  isOpen, 
  onClose, 
  onSave, 
  initialRule 
}: RecurringEventManagerProps) {
  const [rule, setRule] = useState<RecurrenceRule>(
    initialRule || {
      frequency: 'weekly',
      interval: 1,
      daysOfWeek: [],
      exceptions: []
    }
  )

  const [endType, setEndType] = useState<'never' | 'date' | 'count'>('never')

  const weekDays = [
    { value: 0, label: 'الأحد', short: 'ح' },
    { value: 1, label: 'الإثنين', short: 'ن' },
    { value: 2, label: 'الثلاثاء', short: 'ث' },
    { value: 3, label: 'الأربعاء', short: 'ر' },
    { value: 4, label: 'الخميس', short: 'خ' },
    { value: 5, label: 'الجمعة', short: 'ج' },
    { value: 6, label: 'السبت', short: 'س' }
  ]

  const frequencyOptions = [
    { value: 'daily', label: 'يومياً' },
    { value: 'weekly', label: 'أسبوعياً' },
    { value: 'monthly', label: 'شهرياً' },
    { value: 'yearly', label: 'سنوياً' },
    { value: 'custom', label: 'مخصص' }
  ]

  const handleFrequencyChange = (frequency: string) => {
    setRule(prev => ({
      ...prev,
      frequency: frequency as RecurrenceRule['frequency'],
      daysOfWeek: frequency === 'weekly' ? prev.daysOfWeek : undefined,
      dayOfMonth: frequency === 'monthly' ? prev.dayOfMonth : undefined,
      monthOfYear: frequency === 'yearly' ? prev.monthOfYear : undefined
    }))
  }

  const handleDayOfWeekToggle = (day: number) => {
    setRule(prev => ({
      ...prev,
      daysOfWeek: prev.daysOfWeek?.includes(day)
        ? prev.daysOfWeek.filter(d => d !== day)
        : [...(prev.daysOfWeek || []), day]
    }))
  }

  const handleSave = () => {
    const finalRule = { ...rule }
    
    if (endType === 'never') {
      delete finalRule.endDate
      delete finalRule.count
    } else if (endType === 'date') {
      delete finalRule.count
    } else if (endType === 'count') {
      delete finalRule.endDate
    }

    onSave(finalRule)
    onClose()
  }

  const getRecurrenceDescription = () => {
    const { frequency, interval, daysOfWeek } = rule
    
    switch (frequency) {
      case 'daily':
        return interval === 1 ? 'كل يوم' : `كل ${interval} أيام`
      case 'weekly':
        if (daysOfWeek && daysOfWeek.length > 0) {
          const dayNames = daysOfWeek.map(d => weekDays[d].short).join(', ')
          return `كل أسبوع في: ${dayNames}`
        }
        return interval === 1 ? 'كل أسبوع' : `كل ${interval} أسابيع`
      case 'monthly':
        return interval === 1 ? 'كل شهر' : `كل ${interval} أشهر`
      case 'yearly':
        return interval === 1 ? 'كل سنة' : `كل ${interval} سنوات`
      case 'custom':
        return 'نمط مخصص'
      default:
        return 'غير محدد'
    }
  }

  if (!isOpen) return null

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="bg-background rounded-xl border border-border/50 shadow-xl p-6 w-full max-w-md"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex items-center gap-3 mb-6">
          <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
            <Repeat className="w-5 h-5 text-white" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-foreground">الأحداث المتكررة</h2>
            <p className="text-sm text-muted-foreground">إعداد نمط التكرار</p>
          </div>
        </div>

        <div className="space-y-6">
          {/* Frequency Selection */}
          <div className="space-y-2">
            <Label htmlFor="frequency">نوع التكرار</Label>
            <Select value={rule.frequency} onValueChange={handleFrequencyChange}>
              <SelectTrigger>
                <SelectValue placeholder="اختر نوع التكرار" />
              </SelectTrigger>
              <SelectContent>
                {frequencyOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Interval */}
          <div className="space-y-2">
            <Label htmlFor="interval">الفترة</Label>
            <Input
              id="interval"
              type="number"
              min="1"
              value={rule.interval}
              onChange={(e) => setRule(prev => ({ ...prev, interval: parseInt(e.target.value) || 1 }))}
              className="w-full"
            />
          </div>

          {/* Days of Week (for weekly) */}
          {rule.frequency === 'weekly' && (
            <div className="space-y-2">
              <Label>أيام الأسبوع</Label>
              <div className="grid grid-cols-7 gap-1">
                {weekDays.map((day) => (
                  <Button
                    key={day.value}
                    variant={rule.daysOfWeek?.includes(day.value) ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleDayOfWeekToggle(day.value)}
                    className="text-xs p-2"
                  >
                    {day.short}
                  </Button>
                ))}
              </div>
            </div>
          )}

          {/* End Condition */}
          <div className="space-y-3">
            <Label>انتهاء التكرار</Label>
            
            <div className="space-y-2">
              <div className="flex items-center space-x-2 space-x-reverse">
                <Checkbox
                  id="never"
                  checked={endType === 'never'}
                  onCheckedChange={() => setEndType('never')}
                />
                <Label htmlFor="never" className="text-sm">لا ينتهي أبداً</Label>
              </div>
              
              <div className="flex items-center space-x-2 space-x-reverse">
                <Checkbox
                  id="date"
                  checked={endType === 'date'}
                  onCheckedChange={() => setEndType('date')}
                />
                <Label htmlFor="date" className="text-sm">ينتهي في تاريخ محدد</Label>
              </div>
              
              <div className="flex items-center space-x-2 space-x-reverse">
                <Checkbox
                  id="count"
                  checked={endType === 'count'}
                  onCheckedChange={() => setEndType('count')}
                />
                <Label htmlFor="count" className="text-sm">ينتهي بعد عدد معين</Label>
              </div>
            </div>

            {endType === 'date' && (
              <Input
                type="date"
                value={rule.endDate ? rule.endDate.toISOString().split('T')[0] : ''}
                onChange={(e) => setRule(prev => ({ 
                  ...prev, 
                  endDate: e.target.value ? new Date(e.target.value) : undefined 
                }))}
              />
            )}

            {endType === 'count' && (
              <Input
                type="number"
                min="1"
                placeholder="عدد المرات"
                value={rule.count || ''}
                onChange={(e) => setRule(prev => ({ 
                  ...prev, 
                  count: parseInt(e.target.value) || undefined 
                }))}
              />
            )}
          </div>

          {/* Preview */}
          <div className="p-3 bg-muted/20 rounded-lg">
            <div className="text-sm font-medium text-foreground mb-1">معاينة:</div>
            <div className="text-sm text-muted-foreground">{getRecurrenceDescription()}</div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-3 mt-6">
          <Button variant="outline" onClick={onClose}>
            إلغاء
          </Button>
          <Button onClick={handleSave}>
            حفظ
          </Button>
        </div>
      </motion.div>
    </motion.div>
  )
}
