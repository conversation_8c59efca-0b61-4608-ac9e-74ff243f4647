import { useState, useEffect } from "react"
import { useNavigate } from "react-router-dom"
import { motion } from "framer-motion"
import { ThemeProvider } from "@/components/theme/ThemeProvider"
import { DashboardLayout } from "@/components/dashboard/DashboardLayout"
import { CategoriesHeader } from "@/components/dashboard/categories/CategoriesHeader"
import { CategoryFilters } from "@/components/dashboard/categories/CategoryFilters"
import { CategoryCard } from "@/components/dashboard/categories/CategoryCard"
import { CategoryModal } from "@/components/dashboard/categories/CategoryModal"
import { CategoryStats } from "@/components/dashboard/categories/CategoryStats"
import { EmptyState } from "@/components/dashboard/categories/EmptyState"
import { useAuthStore } from "@/stores/authStore"
import { categoryHelpers, type Category } from "@/lib/supabase"
import { toast } from "sonner"

export type FilterType = 'all' | 'pinned' | 'favorite' | 'inactive'
export type SortType = 'newest' | 'oldest' | 'most_used' | 'alphabetical'

const Categories = () => {
  const navigate = useNavigate()
  const { isAuthenticated, user } = useAuthStore()
  
  // State management
  const [categories, setCategories] = useState<Category[]>([])
  const [filteredCategories, setFilteredCategories] = useState<Category[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [filterType, setFilterType] = useState<FilterType>('all')
  const [sortType, setSortType] = useState<SortType>('newest')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingCategory, setEditingCategory] = useState<Category | null>(null)

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/auth/login')
    }
  }, [isAuthenticated, navigate])

  // Load categories
  useEffect(() => {
    if (user?.id) {
      loadCategories()
    }
  }, [user?.id])

  // Filter and sort categories
  useEffect(() => {
    let filtered = [...categories]

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(category =>
        category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        category.description?.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    // Apply type filter
    switch (filterType) {
      case 'pinned':
        filtered = filtered.filter(category => category.is_pinned)
        break
      case 'favorite':
        filtered = filtered.filter(category => category.is_favorite)
        break
      case 'inactive':
        filtered = filtered.filter(category => category.notes_count === 0)
        break
    }

    // Apply sorting
    switch (sortType) {
      case 'newest':
        filtered.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
        break
      case 'oldest':
        filtered.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())
        break
      case 'most_used':
        filtered.sort((a, b) => b.notes_count - a.notes_count)
        break
      case 'alphabetical':
        filtered.sort((a, b) => a.name.localeCompare(b.name, 'ar'))
        break
    }

    setFilteredCategories(filtered)
  }, [categories, searchQuery, filterType, sortType])

  const loadCategories = async () => {
    if (!user?.id) return
    
    setIsLoading(true)
    try {
      const data = await categoryHelpers.getCategories(user.id)
      setCategories(data)
    } catch (error) {
      console.error('Error loading categories:', error)
      toast.error('فشل في تحميل الفئات')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCreateCategory = () => {
    setEditingCategory(null)
    setIsModalOpen(true)
  }

  const handleEditCategory = (category: Category) => {
    setEditingCategory(category)
    setIsModalOpen(true)
  }

  const handleDeleteCategory = async (categoryId: string) => {
    try {
      const success = await categoryHelpers.deleteCategory(categoryId)
      if (success) {
        setCategories(prev => prev.filter(cat => cat.id !== categoryId))
        toast.success('تم حذف الفئة بنجاح')
      } else {
        toast.error('فشل في حذف الفئة')
      }
    } catch (error) {
      console.error('Error deleting category:', error)
      toast.error('فشل في حذف الفئة')
    }
  }

  const handleTogglePin = async (categoryId: string, isPinned: boolean) => {
    try {
      const updated = await categoryHelpers.updateCategory(categoryId, { is_pinned: !isPinned })
      if (updated) {
        setCategories(prev => prev.map(cat => 
          cat.id === categoryId ? { ...cat, is_pinned: !isPinned } : cat
        ))
        toast.success(isPinned ? 'تم إلغاء تثبيت الفئة' : 'تم تثبيت الفئة')
      }
    } catch (error) {
      console.error('Error toggling pin:', error)
      toast.error('فشل في تحديث الفئة')
    }
  }

  const handleToggleFavorite = async (categoryId: string, isFavorite: boolean) => {
    try {
      const updated = await categoryHelpers.updateCategory(categoryId, { is_favorite: !isFavorite })
      if (updated) {
        setCategories(prev => prev.map(cat => 
          cat.id === categoryId ? { ...cat, is_favorite: !isFavorite } : cat
        ))
        toast.success(isFavorite ? 'تم إزالة من المفضلة' : 'تم إضافة للمفضلة')
      }
    } catch (error) {
      console.error('Error toggling favorite:', error)
      toast.error('فشل في تحديث الفئة')
    }
  }

  const handleModalSave = async (categoryData: Omit<Category, 'id' | 'user_id' | 'created_at' | 'updated_at'>) => {
    if (!user?.id) return

    try {
      if (editingCategory) {
        // Update existing category
        const updated = await categoryHelpers.updateCategory(editingCategory.id, categoryData)
        if (updated) {
          setCategories(prev => prev.map(cat => 
            cat.id === editingCategory.id ? updated : cat
          ))
          toast.success('تم تحديث الفئة بنجاح')
        }
      } else {
        // Create new category
        const newCategory = await categoryHelpers.createCategory({
          ...categoryData,
          user_id: user.id,
          sort_order: categories.length
        })
        if (newCategory) {
          setCategories(prev => [...prev, newCategory])
          toast.success('تم إنشاء الفئة بنجاح')
        }
      }
      setIsModalOpen(false)
      setEditingCategory(null)
    } catch (error) {
      console.error('Error saving category:', error)
      toast.error('فشل في حفظ الفئة')
    }
  }

  if (!isAuthenticated) {
    return null
  }

  return (
    <ThemeProvider defaultTheme="system" storageKey="nots-categories-theme">
      <DashboardLayout>
        <div className="space-y-6">
          {/* Header */}
          <CategoriesHeader />

          {/* Filters and Actions */}
          <CategoryFilters
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
            filterType={filterType}
            onFilterChange={setFilterType}
            sortType={sortType}
            onSortChange={setSortType}
            onCreateCategory={handleCreateCategory}
          />

          {/* Statistics */}
          <CategoryStats categories={categories} />

          {/* Categories Grid */}
          <div className="min-h-[400px]">
            {isLoading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {Array.from({ length: 8 }).map((_, index) => (
                  <div
                    key={index}
                    className="h-48 bg-muted/30 rounded-xl animate-pulse"
                  />
                ))}
              </div>
            ) : filteredCategories.length > 0 ? (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
              >
                {filteredCategories.map((category, index) => (
                  <CategoryCard
                    key={category.id}
                    category={category}
                    index={index}
                    onEdit={() => handleEditCategory(category)}
                    onDelete={() => handleDeleteCategory(category.id)}
                    onTogglePin={() => handleTogglePin(category.id, category.is_pinned)}
                    onToggleFavorite={() => handleToggleFavorite(category.id, category.is_favorite)}
                  />
                ))}
              </motion.div>
            ) : (
              <EmptyState
                hasCategories={categories.length > 0}
                searchQuery={searchQuery}
                onCreateCategory={handleCreateCategory}
              />
            )}
          </div>
        </div>

        {/* Category Modal */}
        <CategoryModal
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false)
            setEditingCategory(null)
          }}
          onSave={handleModalSave}
          category={editingCategory}
        />
      </DashboardLayout>
    </ThemeProvider>
  )
}

export default Categories
