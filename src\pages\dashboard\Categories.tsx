import { useState, useEffect } from "react"
import { useNavigate } from "react-router-dom"
import { motion } from "framer-motion"
import { ThemeProvider } from "@/components/theme/ThemeProvider"
import { DashboardLayout } from "@/components/dashboard/DashboardLayout"
import { CategoriesHeader } from "@/components/dashboard/categories/CategoriesHeader"
import { CategoryFilters } from "@/components/dashboard/categories/CategoryFilters"
import { CategoryCard } from "@/components/dashboard/categories/CategoryCard"
import { CategoryModal } from "@/components/dashboard/categories/CategoryModal"
import { CategoryStats } from "@/components/dashboard/categories/CategoryStats"
import { EmptyState } from "@/components/dashboard/categories/EmptyState"
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth"
import { categoryHelpers, type Category } from "@/lib/supabase"
import { toast } from "sonner"

export type FilterType = 'all' | 'pinned' | 'favorite' | 'inactive'
export type SortType = 'newest' | 'oldest' | 'most_used' | 'alphabetical'

const Categories = () => {
  const navigate = useNavigate()
  const { isAuthenticated, user, isLoading: isAuthLoading } = useOptimizedAuth()

  // State management
  const [categories, setCategories] = useState<Category[]>([])
  const [filteredCategories, setFilteredCategories] = useState<Category[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [filterType, setFilterType] = useState<FilterType>('all')
  const [sortType, setSortType] = useState<SortType>('newest')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingCategory, setEditingCategory] = useState<Category | null>(null)



  // Load categories and setup real-time subscription
  useEffect(() => {
    if (user?.id && !isAuthLoading) {
      console.log('Setting up categories for user:', user.id)
      loadCategories()

      // Setup real-time subscription
      const subscription = categoryHelpers.subscribeToCategories(user.id, (updatedCategories) => {
        console.log('Real-time categories update received:', updatedCategories.length)
        setCategories(updatedCategories)
        toast.success('تم تحديث الفئات')
      })

      return () => {
        console.log('Cleaning up categories subscription')
        if (subscription) {
          subscription.unsubscribe()
        }
      }
    }
  }, [user?.id, isAuthLoading])

  // Filter and sort categories
  useEffect(() => {
    let filtered = [...categories]

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(category =>
        category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        category.description?.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    // Apply type filter
    switch (filterType) {
      case 'pinned':
        filtered = filtered.filter(category => category.is_pinned)
        break
      case 'favorite':
        filtered = filtered.filter(category => category.is_favorite)
        break
      case 'inactive':
        filtered = filtered.filter(category => category.notes_count === 0)
        break
    }

    // Apply sorting
    switch (sortType) {
      case 'newest':
        filtered.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
        break
      case 'oldest':
        filtered.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())
        break
      case 'most_used':
        filtered.sort((a, b) => b.notes_count - a.notes_count)
        break
      case 'alphabetical':
        filtered.sort((a, b) => a.name.localeCompare(b.name, 'ar'))
        break
    }

    setFilteredCategories(filtered)
  }, [categories, searchQuery, filterType, sortType])

  const loadCategories = async () => {
    if (!user?.id) {
      console.warn('No user ID available for loading categories')
      return
    }

    setIsLoading(true)
    try {
      console.log('Loading categories for user:', user.id)
      const data = await categoryHelpers.getCategories(user.id)
      console.log('Categories loaded:', data.length)
      setCategories(data)
    } catch (error) {
      console.error('Error loading categories:', error)
      toast.error('فشل في تحميل الفئات. يرجى المحاولة مرة أخرى.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCreateCategory = () => {
    setEditingCategory(null)
    setIsModalOpen(true)
  }

  const handleEditCategory = (category: Category) => {
    setEditingCategory(category)
    setIsModalOpen(true)
  }

  const handleDeleteCategory = async (categoryId: string) => {
    try {
      console.log('Deleting category:', categoryId)

      // Optimistic update
      setCategories(prev => prev.filter(cat => cat.id !== categoryId))

      await categoryHelpers.deleteCategory(categoryId)
      toast.success('تم حذف الفئة بنجاح')

      // Refresh categories to ensure consistency
      await loadCategories()
    } catch (error) {
      console.error('Error deleting category:', error)
      toast.error('فشل في حذف الفئة. يرجى المحاولة مرة أخرى.')
      // Reload categories on error to revert optimistic update
      await loadCategories()
    }
  }

  const handleTogglePin = async (categoryId: string, isPinned: boolean) => {
    try {
      console.log('Toggling pin for category:', categoryId, !isPinned)

      // Optimistic update
      setCategories(prev => prev.map(cat =>
        cat.id === categoryId ? { ...cat, is_pinned: !isPinned } : cat
      ))

      await categoryHelpers.updateCategory(categoryId, { is_pinned: !isPinned })
      toast.success(isPinned ? 'تم إلغاء تثبيت الفئة' : 'تم تثبيت الفئة')

      // Refresh categories to ensure consistency
      await loadCategories()
    } catch (error) {
      console.error('Error toggling pin:', error)
      toast.error('فشل في تحديث الفئة. يرجى المحاولة مرة أخرى.')
      // Reload categories on error to revert optimistic update
      await loadCategories()
    }
  }

  const handleToggleFavorite = async (categoryId: string, isFavorite: boolean) => {
    try {
      console.log('Toggling favorite for category:', categoryId, !isFavorite)

      // Optimistic update
      setCategories(prev => prev.map(cat =>
        cat.id === categoryId ? { ...cat, is_favorite: !isFavorite } : cat
      ))

      await categoryHelpers.updateCategory(categoryId, { is_favorite: !isFavorite })
      toast.success(isFavorite ? 'تم إزالة من المفضلة' : 'تم إضافة للمفضلة')

      // Refresh categories to ensure consistency
      await loadCategories()
    } catch (error) {
      console.error('Error toggling favorite:', error)
      toast.error('فشل في تحديث الفئة. يرجى المحاولة مرة أخرى.')
      // Reload categories on error to revert optimistic update
      await loadCategories()
    }
  }

  const handleModalSave = async (categoryData: Omit<Category, 'id' | 'user_id' | 'created_at' | 'updated_at'>) => {
    if (!user?.id) {
      console.error('No user ID available for saving category')
      toast.error('خطأ في المصادقة. يرجى تسجيل الدخول مرة أخرى.')
      return
    }

    try {
      if (editingCategory) {
        // Update existing category
        console.log('Updating category:', editingCategory.id, categoryData)

        // Optimistic update
        setCategories(prev => prev.map(cat =>
          cat.id === editingCategory.id ? { ...cat, ...categoryData } : cat
        ))

        await categoryHelpers.updateCategory(editingCategory.id, categoryData)
        toast.success('تم تحديث الفئة بنجاح')

        // Refresh to ensure consistency
        await loadCategories()
      } else {
        // Create new category
        const newCategoryData = {
          ...categoryData,
          user_id: user.id,
          sort_order: categories.length,
          notes_count: 0,
          is_pinned: categoryData.is_pinned || false,
          is_favorite: categoryData.is_favorite || false
        }

        console.log('Creating new category:', newCategoryData)
        const newCategory = await categoryHelpers.createCategory(newCategoryData)

        // Optimistic update
        setCategories(prev => [...prev, newCategory])

        toast.success('تم إنشاء الفئة بنجاح')

        // Refresh to ensure consistency
        await loadCategories()
      }

      setIsModalOpen(false)
      setEditingCategory(null)
    } catch (error) {
      console.error('Error saving category:', error)
      toast.error('فشل في حفظ الفئة. يرجى المحاولة مرة أخرى.')
      // Reload categories on error
      await loadCategories()
    }
  }

  // Show loading while checking authentication
  if (isAuthLoading) {
    return (
      <ThemeProvider defaultTheme="system" storageKey="nots-categories-theme">
        <DashboardLayout>
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center space-y-4">
              <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto" />
              <p className="text-muted-foreground">جاري التحقق من المصادقة...</p>
            </div>
          </div>
        </DashboardLayout>
      </ThemeProvider>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  return (
    <ThemeProvider defaultTheme="system" storageKey="nots-categories-theme">
      <DashboardLayout>
        <div className="space-y-6">
          {/* Header */}
          <CategoriesHeader />

          {/* Filters and Actions */}
          <CategoryFilters
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
            filterType={filterType}
            onFilterChange={setFilterType}
            sortType={sortType}
            onSortChange={setSortType}
            onCreateCategory={handleCreateCategory}
          />

          {/* Statistics */}
          <CategoryStats categories={categories} />

          {/* Categories Grid */}
          <div className="min-h-[400px]">
            {isLoading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {Array.from({ length: 8 }).map((_, index) => (
                  <div
                    key={index}
                    className="h-48 bg-muted/30 rounded-xl animate-pulse"
                  />
                ))}
              </div>
            ) : filteredCategories.length > 0 ? (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
              >
                {filteredCategories.map((category, index) => (
                  <CategoryCard
                    key={category.id}
                    category={category}
                    index={index}
                    onEdit={() => handleEditCategory(category)}
                    onDelete={() => handleDeleteCategory(category.id)}
                    onTogglePin={() => handleTogglePin(category.id, category.is_pinned)}
                    onToggleFavorite={() => handleToggleFavorite(category.id, category.is_favorite)}
                  />
                ))}
              </motion.div>
            ) : (
              <EmptyState
                hasCategories={categories.length > 0}
                searchQuery={searchQuery}
                onCreateCategory={handleCreateCategory}
              />
            )}
          </div>
        </div>

        {/* Category Modal */}
        <CategoryModal
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false)
            setEditingCategory(null)
          }}
          onSave={handleModalSave}
          category={editingCategory}
        />
      </DashboardLayout>
    </ThemeProvider>
  )
}

export default Categories
