import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { Sidebar } from "./Sidebar"
import { Head<PERSON> } from "./Header"

interface DashboardLayoutProps {
  children: React.ReactNode
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [isMobile, setIsMobile] = useState(false)

  // Check if mobile on mount and resize
  useEffect(() => {
    const checkMobile = () => {
      const mobile = window.innerWidth < 1024
      setIsMobile(mobile)
      if (mobile) {
        setSidebarCollapsed(true)
      }
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-muted/30 via-accent/10 to-background font-tajawal" dir="rtl">
      {/* Background Elements */}
      <div className="fixed inset-0 bg-gradient-to-br from-muted/30 via-accent/10 to-background pointer-events-none" />
      
      {/* Animated Background Shapes */}
      <motion.div
        animate={{
          rotate: 360,
          scale: [1, 1.1, 1],
        }}
        transition={{
          rotate: { duration: 30, repeat: Infinity, ease: "linear" },
          scale: { duration: 10, repeat: Infinity, ease: "easeInOut" },
        }}
        className="fixed top-20 right-20 w-32 h-32 rounded-full bg-primary/5 blur-xl pointer-events-none"
      />
      
      <motion.div
        animate={{
          rotate: -360,
          scale: [1, 1.2, 1],
        }}
        transition={{
          rotate: { duration: 35, repeat: Infinity, ease: "linear" },
          scale: { duration: 15, repeat: Infinity, ease: "easeInOut" },
        }}
        className="fixed bottom-20 left-20 w-48 h-48 rounded-full bg-secondary/5 blur-xl pointer-events-none"
      />

      {/* Header */}
      <Header sidebarCollapsed={sidebarCollapsed} />

      {/* Sidebar */}
      <Sidebar 
        isCollapsed={sidebarCollapsed} 
        onToggle={toggleSidebar}
        isMobile={isMobile}
      />

      {/* Main Content */}
      <main 
        className="relative z-10 pt-16 transition-all duration-300"
        style={{ 
          paddingRight: sidebarCollapsed ? (isMobile ? 0 : 80) : 280 
        }}
      >
        <div className="p-6">
          {children}
        </div>
      </main>
    </div>
  )
}
