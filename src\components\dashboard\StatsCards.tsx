import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { useAuthStore } from "@/stores/authStore"
import { 
  StickyNote, 
  Brain, 
  Mic, 
  BarChart3,
  TrendingUp,
  TrendingDown
} from "lucide-react"

interface StatCardProps {
  title: string
  value: string | number
  icon: React.ElementType
  color: string
  bgColor: string
  change?: {
    value: number
    type: 'increase' | 'decrease'
  }
  index: number
}

function StatCard({ title, value, icon: Icon, color, bgColor, change, index }: StatCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      whileHover={{ y: -4 }}
    >
      <Card className="relative overflow-hidden border-border/50 hover:border-primary/20 transition-all duration-300 hover:shadow-lg">
        {/* Background Gradient */}
        <div className={`absolute inset-0 ${bgColor} opacity-5`} />
        
        <CardContent className="p-6 relative z-10">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-sm font-medium text-muted-foreground mb-1">
                {title}
              </p>
              <p className="text-2xl font-bold text-foreground">
                {value}
              </p>
              
              {change && (
                <div className="flex items-center space-x-1 space-x-reverse mt-2">
                  {change.type === 'increase' ? (
                    <TrendingUp className="w-3 h-3 text-green-500" />
                  ) : (
                    <TrendingDown className="w-3 h-3 text-red-500" />
                  )}
                  <span className={`text-xs font-medium ${
                    change.type === 'increase' ? 'text-green-500' : 'text-red-500'
                  }`}>
                    {change.value}%
                  </span>
                  <span className="text-xs text-muted-foreground">
                    من الشهر الماضي
                  </span>
                </div>
              )}
            </div>
            
            <div className={`w-12 h-12 rounded-xl ${bgColor} flex items-center justify-center`}>
              <Icon className={`w-6 h-6 ${color}`} />
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}

export function StatsCards() {
  const { user } = useAuthStore()

  // Mock data - in real app, this would come from API
  const stats = [
    {
      title: "إجمالي الملاحظات",
      value: user?.profile?.notes_count || 24,
      icon: StickyNote,
      color: "text-blue-600",
      bgColor: "bg-blue-500",
      change: {
        value: 12,
        type: 'increase' as const
      }
    },
    {
      title: "ملاحظات ملخصة بالذكاء الاصطناعي",
      value: 18,
      icon: Brain,
      color: "text-purple-600",
      bgColor: "bg-purple-500",
      change: {
        value: 8,
        type: 'increase' as const
      }
    },
    {
      title: "تسجيلات صوتية محولة",
      value: 7,
      icon: Mic,
      color: "text-green-600",
      bgColor: "bg-green-500",
      change: {
        value: 15,
        type: 'increase' as const
      }
    },
    {
      title: "معدل النشاط الشهري",
      value: "85%",
      icon: BarChart3,
      color: "text-orange-600",
      bgColor: "bg-orange-500",
      change: {
        value: 3,
        type: 'decrease' as const
      }
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {stats.map((stat, index) => (
        <StatCard
          key={stat.title}
          title={stat.title}
          value={stat.value}
          icon={stat.icon}
          color={stat.color}
          bgColor={stat.bgColor}
          change={stat.change}
          index={index}
        />
      ))}
    </div>
  )
}
