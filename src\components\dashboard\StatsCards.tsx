import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { useAuthStore } from "@/stores/authStore"
import { 
  StickyNote, 
  Brain, 
  Mic, 
  BarChart3,
  TrendingUp,
  TrendingDown
} from "lucide-react"

interface StatCardProps {
  title: string
  value: string | number
  icon: React.ElementType
  color: string
  bgColor: string
  change?: {
    value: number
    type: 'increase' | 'decrease'
  }
  index: number
}

function StatCard({ title, value, icon: Icon, color, bgColor, change, index }: StatCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      whileHover={{ y: -2, scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
    >
      <Card className="relative overflow-hidden border-border/50 hover:border-primary/20 transition-all duration-300 hover:shadow-xl bg-gradient-to-br from-background to-muted/20 group cursor-pointer">
        {/* Background Gradient */}
        <div className={`absolute inset-0 ${bgColor} opacity-5 group-hover:opacity-10 transition-opacity duration-300`} />

        {/* Animated Background Shape */}
        <motion.div
          animate={{
            scale: [1, 1.1, 1],
            rotate: [0, 5, 0],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut",
          }}
          className={`absolute -top-4 -right-4 w-16 h-16 ${bgColor} opacity-10 rounded-full blur-xl`}
        />

        <CardContent className="p-4 relative z-10">
          <div className="flex items-start justify-between mb-3">
            <div className={`w-10 h-10 rounded-xl ${bgColor} bg-opacity-20 flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-sm`}>
              <Icon className={`w-5 h-5 ${color}`} />
            </div>

            {change && (
              <div className="flex items-center space-x-1 space-x-reverse">
                {change.type === 'increase' ? (
                  <TrendingUp className="w-3 h-3 text-green-500" />
                ) : (
                  <TrendingDown className="w-3 h-3 text-red-500" />
                )}
                <span className={`text-xs font-medium ${
                  change.type === 'increase' ? 'text-green-500' : 'text-red-500'
                }`}>
                  {change.value}%
                </span>
              </div>
            )}
          </div>

          <div>
            <p className="text-lg font-bold text-foreground mb-1 group-hover:text-primary transition-colors duration-300">
              {value}
            </p>
            <p className="text-xs font-medium text-muted-foreground leading-tight">
              {title}
            </p>
          </div>

          {change && (
            <div className="mt-2 pt-2 border-t border-border/30">
              <span className="text-xs text-muted-foreground">
                من الشهر الماضي
              </span>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  )
}

export function StatsCards() {
  const { user } = useAuthStore()

  // Mock data - in real app, this would come from API
  const stats = [
    {
      title: "إجمالي الملاحظات",
      value: user?.profile?.notes_count || 24,
      icon: StickyNote,
      color: "text-blue-600",
      bgColor: "bg-blue-500",
      change: {
        value: 12,
        type: 'increase' as const
      }
    },
    {
      title: "التصنيفات والمجلدات",
      value: user?.profile?.folders_count || 8,
      icon: StickyNote,
      color: "text-green-600",
      bgColor: "bg-green-500",
      change: {
        value: 25,
        type: 'increase' as const
      }
    },
    {
      title: "ملاحظات ملخصة بالذكاء الاصطناعي",
      value: 18,
      icon: Brain,
      color: "text-purple-600",
      bgColor: "bg-purple-500",
      change: {
        value: 8,
        type: 'increase' as const
      }
    },
    {
      title: "تسجيلات صوتية محولة",
      value: 7,
      icon: Mic,
      color: "text-indigo-600",
      bgColor: "bg-indigo-500",
      change: {
        value: 15,
        type: 'increase' as const
      }
    },
    {
      title: "الإشعارات النشطة",
      value: 5,
      icon: BarChart3,
      color: "text-orange-600",
      bgColor: "bg-orange-500",
      change: {
        value: 2,
        type: 'increase' as const
      }
    },
    {
      title: "معدل النشاط الأسبوعي",
      value: "85%",
      icon: TrendingUp,
      color: "text-emerald-600",
      bgColor: "bg-emerald-500",
      change: {
        value: 3,
        type: 'decrease' as const
      }
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
      {stats.map((stat, index) => (
        <StatCard
          key={stat.title}
          title={stat.title}
          value={stat.value}
          icon={stat.icon}
          color={stat.color}
          bgColor={stat.bgColor}
          change={stat.change}
          index={index}
        />
      ))}
    </div>
  )
}
