import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { 
  PenTool, 
  Brain, 
  FolderTree, 
  Globe, 
  ArrowRight,
  CheckCircle,
  Sparkles
} from "lucide-react"

const steps = [
  {
    id: 1,
    icon: PenTool,
    title: "إنشاء ملاحظة",
    description: "ابدأ بكتابة أفكارك أو تسجيل صوتي أو رفع ملف",
    details: "اكتب بحرية، سجل صوتياً، أو ارفع ملفات PDF/Word. Nots يدعم جميع الطرق.",
    color: "text-nots-blue",
    bgColor: "bg-nots-blue/10",
    borderColor: "border-nots-blue/20"
  },
  {
    id: 2,
    icon: Brain,
    title: "معالجة بالذكاء الاصطناعي",
    description: "يقوم الذكاء الاصطناعي بتحليل وفهم محتوى ملاحظتك",
    details: "تقنيات متقدمة لفهم السياق العربي وتحليل المعنى واستخراج النقاط المهمة.",
    color: "text-primary",
    bgColor: "bg-primary/10",
    borderColor: "border-primary/20"
  },
  {
    id: 3,
    icon: FolderTree,
    title: "تنظيم تلقائي",
    description: "تصنيف ذكي وإضافة علامات وتنظيم في مجلدات مناسبة",
    details: "يحدد الموضوع تلقائياً ويضع الملاحظة في المكان المناسب مع إضافة علامات ذكية.",
    color: "text-nots-green",
    bgColor: "bg-nots-green/10",
    borderColor: "border-nots-green/20"
  },
  {
    id: 4,
    icon: Globe,
    title: "الوصول من أي مكان",
    description: "مزامنة فورية عبر جميع أجهزتك مع حماية كاملة للبيانات",
    details: "وصول آمن من الهاتف، الحاسوب، أو التابلت مع تشفير متقدم للبيانات.",
    color: "text-nots-orange",
    bgColor: "bg-nots-orange/10",
    borderColor: "border-nots-orange/20"
  }
]

const benefits = [
  "توفير 70% من وقت التنظيم",
  "تحسين جودة الملاحظات بنسبة 85%",
  "سهولة البحث والوصول للمعلومات",
  "تجربة مستخدم محسنة للعربية"
]

export function HowItWorks() {
  return (
    <section className="py-20 bg-background" id="how-it-works">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-purple/10 text-primary font-medium mb-6">
            <Sparkles className="w-5 h-5" />
            <span>❓ كيف يعمل؟</span>
          </div>
          
          <h2 className="text-3xl md:text-5xl font-bold nots-heading mb-6">
            <span className="bg-gradient-purple bg-clip-text text-transparent">أربع خطوات</span>
            <br />لتجربة ملاحظات ذكية
          </h2>
          
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            عملية بسيطة وسلسة تحول طريقة تدوينك وتنظيم أفكارك. 
            من الكتابة إلى التنظيم التلقائي في دقائق معدودة.
          </p>
        </motion.div>

        {/* Steps */}
        <div className="relative mb-16">
          {/* Connection Line */}
          <div className="hidden lg:block absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full max-w-4xl">
            <div className="h-0.5 bg-gradient-to-r from-nots-blue via-primary via-nots-green to-nots-orange opacity-30"></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {steps.map((step, index) => {
              const Icon = step.icon
              return (
                <motion.div
                  key={step.id}
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.2 }}
                  viewport={{ once: true }}
                  className="relative"
                >
                  <Card className={`text-center p-6 nots-shadow hover:nots-shadow-lg transition-all duration-300 border-2 ${step.borderColor} hover:border-opacity-40 group relative z-10 bg-card`}>
                    <CardContent className="p-0">
                      {/* Step Number */}
                      <div className="absolute -top-4 -right-4 w-8 h-8 bg-gradient-purple rounded-full flex items-center justify-center text-white font-bold text-sm">
                        {step.id}
                      </div>
                      
                      {/* Icon */}
                      <div className={`w-20 h-20 ${step.bgColor} rounded-xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300`}>
                        <Icon className={`w-10 h-10 ${step.color}`} />
                      </div>
                      
                      {/* Content */}
                      <h3 className="text-xl font-bold nots-heading mb-3">{step.title}</h3>
                      <p className="text-muted-foreground mb-4 leading-relaxed">
                        {step.description}
                      </p>
                      <p className="text-sm text-muted-foreground/80">
                        {step.details}
                      </p>
                    </CardContent>
                  </Card>
                </motion.div>
              )
            })}
          </div>
        </div>

        {/* Benefits Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="bg-nots-bg-light/50 rounded-2xl p-8 md:p-12 mb-16"
        >
          <div className="text-center mb-8">
            <h3 className="text-2xl md:text-3xl font-bold nots-heading mb-4">
              لماذا يختار المستخدمون Nots؟
            </h3>
            <p className="text-muted-foreground">
              نتائج مثبتة من آلاف المستخدمين حول العالم
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {benefits.map((benefit, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="flex items-center gap-3"
              >
                <CheckCircle className="w-6 h-6 text-nots-green flex-shrink-0" />
                <span className="text-foreground font-medium">{benefit}</span>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Interactive Demo CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <div className="bg-gradient-purple rounded-2xl p-8 md:p-12 text-white relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute top-4 right-4">
                <Brain className="w-24 h-24" />
              </div>
              <div className="absolute bottom-4 left-4">
                <Sparkles className="w-20 h-20" />
              </div>
            </div>
            
            <div className="relative z-10 max-w-2xl mx-auto">
              <h3 className="text-2xl md:text-3xl font-bold mb-4">
                جاهز لتجربة الذكاء الاصطناعي؟
              </h3>
              <p className="text-lg text-white/90 mb-8">
                ابدأ رحلتك مع Nots واكتشف كيف يمكن للذكاء الاصطناعي أن يحول طريقة تدوينك
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button 
                  size="lg"
                  className="bg-white text-primary hover:bg-white/90 px-8 py-4 text-lg font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  ابدأ مجاناً الآن
                  <ArrowRight className="w-5 h-5 mr-2" />
                </Button>
                
                <Button 
                  size="lg"
                  variant="outline"
                  className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 text-lg font-medium rounded-xl transition-all duration-300"
                >
                  شاهد العرض التوضيحي
                </Button>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
