import { useState } from "react"
import { motion } from "framer-motion"
import { <PERSON> } from "react-router-dom"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { 
  Home, 
  ChevronLeft, 
  Save, 
  Maximize, 
  Trash2, 
  Share2,

  MoreHorizontal
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { toast } from "sonner"

interface EditorToolbarProps {
  title: string
  onTitleChange: (title: string) => void
  onSave: () => void
  onDelete: () => void
  onFullscreen: () => void
  isFullscreen: boolean
  isSaving: boolean
  lastSaved: string
}

export function EditorToolbar({
  title,
  onTitleChange,
  onSave,
  onDelete,
  onFullscreen,
  isFullscreen,
  isSaving,
  lastSaved
}: EditorToolbarProps) {
  const [isEditingTitle, setIsEditingTitle] = useState(false)



  const handleShare = () => {
    toast.info('ميزة المشاركة ستكون متاحة قريباً')
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="sticky top-0 z-50 bg-background/95 backdrop-blur-md border-b border-border/50 shadow-sm"
    >
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-r from-muted/30 via-accent/10 to-background pointer-events-none" />
      
      <div className="relative z-10 p-4">
        {/* Top Row - Breadcrumb and Actions */}
        <div className="flex items-center justify-between mb-4">
          {/* Breadcrumb */}
          <nav className="flex items-center space-x-2 space-x-reverse text-sm text-muted-foreground">
            <Link 
              to="/dashboard" 
              className="flex items-center space-x-1 space-x-reverse hover:text-primary transition-colors"
            >
              <Home className="w-4 h-4" />
              <span>لوحة التحكم</span>
            </Link>
            <ChevronLeft className="w-4 h-4" />
            <Link 
              to="/dashboard/notes" 
              className="hover:text-primary transition-colors"
            >
              الملاحظات
            </Link>
            <ChevronLeft className="w-4 h-4" />
            <span className="text-foreground font-medium">
              {title || 'ملاحظة جديدة'}
            </span>
          </nav>

          {/* Action Buttons */}
          <div className="flex items-center space-x-3 space-x-reverse">
            {/* Save Status */}
            {lastSaved && lastSaved.trim() && (
              <div className="text-xs text-muted-foreground">
                آخر حفظ: {lastSaved}
              </div>
            )}



            {/* Main Actions */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleShare}
              className="border-border/50 hover:bg-muted/50 rounded-xl"
            >
              <Share2 className="w-4 h-4 mr-2" />
              مشاركة
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={onFullscreen}
              className="border-border/50 hover:bg-muted/50 rounded-xl"
            >
              <Maximize className="w-4 h-4 mr-2" />
              {isFullscreen ? 'إنهاء ملء الشاشة' : 'ملء الشاشة'}
            </Button>

            <Button
              onClick={onSave}
              disabled={isSaving}
              className="bg-gradient-to-r from-primary to-secondary hover:opacity-90 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
              size="sm"
            >
              {isSaving ? (
                <div className="flex items-center space-x-2 space-x-reverse">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  <span>جاري الحفظ...</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2 space-x-reverse">
                  <Save className="w-4 h-4" />
                  <span>حفظ</span>
                </div>
              )}
            </Button>

            {/* More Actions */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="border-border/50 hover:bg-muted/50 rounded-xl"
                >
                  <MoreHorizontal className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>
                  <FileText className="w-4 h-4 mr-2" />
                  تصدير PDF
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <FileText className="w-4 h-4 mr-2" />
                  تصدير Word
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <FileText className="w-4 h-4 mr-2" />
                  تصدير Markdown
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  onClick={onDelete}
                  className="text-red-600 focus:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  حذف الملاحظة
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Title Input */}
        <div className="flex items-center space-x-4 space-x-reverse">
          <div className="w-12 h-12 bg-gradient-to-r from-primary to-secondary rounded-xl flex items-center justify-center shadow-lg">
            <FileText className="w-6 h-6 text-white" />
          </div>
          
          <div className="flex-1">
            {isEditingTitle ? (
              <Input
                value={title}
                onChange={(e) => onTitleChange(e.target.value)}
                onBlur={() => setIsEditingTitle(false)}
                onKeyPress={(e) => e.key === 'Enter' && setIsEditingTitle(false)}
                placeholder="اكتب عنوان الملاحظة..."
                className="text-2xl font-bold border-none bg-transparent p-0 focus:ring-0 focus:border-none"
                autoFocus
                dir="rtl"
              />
            ) : (
              <h1 
                className="text-2xl font-bold text-foreground cursor-pointer hover:text-primary transition-colors"
                onClick={() => setIsEditingTitle(true)}
                dir="rtl"
              >
                {title || 'اضغط لإضافة عنوان...'}
              </h1>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  )
}
