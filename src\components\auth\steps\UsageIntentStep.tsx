import { useState } from "react"
import { motion } from "framer-motion"
import { GraduationCap, Briefcase, Palette, User } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useAuthStore } from "@/stores/authStore"

interface UsageIntentStepProps {
  onNext: () => void
  onBack: () => void
}

const usageOptions = [
  {
    id: 'student' as const,
    title: 'طالب',
    description: 'لتنظيم المحاضرات والمذاكرة',
    icon: GraduationCap,
    color: 'from-blue-500 to-blue-600'
  },
  {
    id: 'employee' as const,
    title: 'موظف',
    description: 'لإدارة المهام والاجتماعات',
    icon: Briefcase,
    color: 'from-green-500 to-green-600'
  },
  {
    id: 'creator' as const,
    title: 'مبدع',
    description: 'لتسجيل الأفكار والمشاريع',
    icon: Palette,
    color: 'from-purple-500 to-purple-600'
  },
  {
    id: 'personal' as const,
    title: 'استخدام شخصي',
    description: 'لتنظيم الحياة اليومية',
    icon: User,
    color: 'from-orange-500 to-orange-600'
  }
]

export function UsageIntentStep({ onNext, onBack }: UsageIntentStepProps) {
  const { signUpData, updateSignUpData } = useAuthStore()
  const [selectedIntent, setSelectedIntent] = useState(signUpData.usageIntent)

  const handleNext = () => {
    updateSignUpData({ usageIntent: selectedIntent })
    onNext()
  }

  const handleSkip = () => {
    updateSignUpData({ usageIntent: '' })
    onNext()
  }

  return (
    <div className="space-y-6">
      {/* Usage Options */}
      <div className="grid grid-cols-1 gap-4">
        {usageOptions.map((option, index) => {
          const Icon = option.icon
          const isSelected = selectedIntent === option.id
          
          return (
            <motion.button
              key={option.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              onClick={() => setSelectedIntent(option.id)}
              className={`
                relative p-6 rounded-xl border-2 text-right transition-all duration-300
                ${isSelected 
                  ? 'border-primary bg-primary/10 shadow-lg' 
                  : 'border-border hover:border-primary/50 hover:bg-muted/50'
                }
              `}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="flex items-center space-x-4 space-x-reverse">
                <div className={`
                  w-12 h-12 rounded-lg bg-gradient-to-r ${option.color} 
                  flex items-center justify-center text-white shadow-lg
                `}>
                  <Icon className="w-6 h-6" />
                </div>
                
                <div className="flex-1">
                  <h3 className={`
                    text-lg font-semibold mb-1 transition-colors
                    ${isSelected ? 'text-primary' : 'text-foreground'}
                  `}>
                    {option.title}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {option.description}
                  </p>
                </div>
                
                {/* Selection Indicator */}
                <div className={`
                  w-5 h-5 rounded-full border-2 transition-all duration-200
                  ${isSelected 
                    ? 'border-primary bg-primary' 
                    : 'border-muted-foreground'
                  }
                `}>
                  {isSelected && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="w-full h-full rounded-full bg-primary flex items-center justify-center"
                    >
                      <div className="w-2 h-2 rounded-full bg-white" />
                    </motion.div>
                  )}
                </div>
              </div>
            </motion.button>
          )
        })}
      </div>

      {/* Action Buttons */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.5 }}
        className="flex flex-col space-y-3"
      >
        <Button
          onClick={handleNext}
          disabled={!selectedIntent}
          className="w-full bg-gradient-to-r from-primary to-secondary hover:opacity-90 text-primary-foreground py-3 text-lg font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          التالي
        </Button>
        
        <div className="flex space-x-3 space-x-reverse">
          <Button
            variant="outline"
            onClick={onBack}
            className="flex-1 border-2 border-border text-foreground hover:bg-muted py-3 rounded-xl transition-all duration-300"
          >
            رجوع
          </Button>
          
          <Button
            variant="ghost"
            onClick={handleSkip}
            className="flex-1 text-muted-foreground hover:text-foreground hover:bg-muted/50 py-3 rounded-xl transition-all duration-300"
          >
            تخطي
          </Button>
        </div>
      </motion.div>

      {/* Help Text */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.6 }}
        className="text-center"
      >
        <p className="text-sm text-muted-foreground">
          سيساعدنا هذا في تخصيص تجربتك مع Nots
        </p>
      </motion.div>
    </div>
  )
}
