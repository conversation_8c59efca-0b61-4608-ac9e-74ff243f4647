import { useState } from "react"
import { motion } from "framer-motion"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { CategoryModal } from "@/components/dashboard/categories/CategoryModal"
import { useAuthStore } from "@/stores/authStore"
import { categoryHelpers, type Category as SupabaseCategory } from "@/lib/supabase"
import { toast } from "sonner"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Folder,
  FolderOpen,
  Plus
} from "lucide-react"

interface Category {
  id: string
  name: string
  color: string
  icon: string
}

interface CategorySelectorProps {
  selectedCategory?: string
  selectedCategoryColor?: string
  onCategoryChange: (category: string, color: string) => void
}

// Sample categories (in real app, these would come from the Categories page)
const sampleCategories: Category[] = [
  { id: '1', name: 'العمل', color: '#3B82F6', icon: 'briefcase' },
  { id: '2', name: 'شخصي', color: '#10B981', icon: 'user' },
  { id: '3', name: 'الدراسة', color: '#EC4899', icon: 'book' },
  { id: '4', name: 'الأفكار', color: '#8B5CF6', icon: 'lightbulb' },
  { id: '5', name: 'السفر', color: '#F59E0B', icon: 'plane' },
]

export function CategorySelector({
  selectedCategory,
  selectedCategoryColor,
  onCategoryChange
}: CategorySelectorProps) {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const { user } = useAuthStore()

  const handleCategorySelect = (categoryName: string) => {
    const category = sampleCategories.find(cat => cat.name === categoryName)
    if (category) {
      onCategoryChange(category.name, category.color)
    }
  }

  const handleCreateCategory = () => {
    setIsModalOpen(true)
  }

  const handleModalSave = async (categoryData: Omit<SupabaseCategory, 'id' | 'user_id' | 'created_at' | 'updated_at'>) => {
    if (!user?.id) {
      toast.error('يجب تسجيل الدخول لإنشاء فئة جديدة')
      return
    }

    try {
      const newCategoryData = {
        ...categoryData,
        user_id: user.id,
        sort_order: 0,
        notes_count: 0,
        is_pinned: false,
        is_favorite: false
      }

      const newCategory = await categoryHelpers.createCategory(newCategoryData)

      // Update the selected category to the newly created one
      onCategoryChange(newCategory.name, newCategory.color)

      toast.success('تم إنشاء الفئة بنجاح')
      setIsModalOpen(false)
    } catch (error) {
      console.error('Error creating category:', error)
      toast.error('فشل في إنشاء الفئة. يرجى المحاولة مرة أخرى.')
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-4"
    >
      {/* Header */}
      <div className="flex items-center space-x-2 space-x-reverse">
        <div className="w-8 h-8 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-lg flex items-center justify-center">
          <Folder className="w-4 h-4 text-primary" />
        </div>
        <Label className="text-sm font-semibold text-foreground">
          تصنيف الملاحظة
        </Label>
      </div>

      {/* Category Selector */}
      <Select value={selectedCategory || ""} onValueChange={handleCategorySelect}>
        <SelectTrigger className="w-full rounded-xl border-border/50 bg-background/50">
          <div className="flex items-center space-x-2 space-x-reverse">
            {selectedCategory ? (
              <>
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: selectedCategoryColor }}
                />
                <SelectValue />
              </>
            ) : (
              <>
                <FolderOpen className="w-4 h-4 text-muted-foreground" />
                <span className="text-muted-foreground">اختر تصنيف...</span>
              </>
            )}
          </div>
        </SelectTrigger>
        <SelectContent>
          {sampleCategories.map((category) => (
            <SelectItem key={category.id} value={category.name}>
              <div className="flex items-center space-x-2 space-x-reverse">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: category.color }}
                />
                <span>{category.name}</span>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* Selected Category Display */}
      {selectedCategory && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          <Badge
            variant="outline"
            className="flex items-center space-x-2 space-x-reverse w-fit border"
            style={{
              borderColor: `${selectedCategoryColor}40`,
              color: selectedCategoryColor,
              backgroundColor: `${selectedCategoryColor}10`
            }}
          >
            <div
              className="w-2 h-2 rounded-full"
              style={{ backgroundColor: selectedCategoryColor }}
            />
            <span>{selectedCategory}</span>
          </Badge>
        </motion.div>
      )}

      {/* Quick Actions */}
      <div className="pt-2 border-t border-border/30">
        <Button
          onClick={handleCreateCategory}
          variant="ghost"
          size="sm"
          className="w-full justify-start text-muted-foreground hover:text-foreground rounded-xl"
        >
          <Plus className="w-4 h-4 mr-2" />
          إنشاء تصنيف جديد
        </Button>
      </div>

      {/* Category Stats */}
      {selectedCategory && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
          className="p-3 bg-muted/30 rounded-xl border border-border/30"
        >
          <div className="text-xs text-muted-foreground space-y-1">
            <div className="flex justify-between">
              <span>عدد الملاحظات:</span>
              <span>12 ملاحظة</span>
            </div>
            <div className="flex justify-between">
              <span>آخر تحديث:</span>
              <span>منذ يومين</span>
            </div>
          </div>
        </motion.div>
      )}

      {/* Category Creation Modal */}
      <CategoryModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSave={handleModalSave}
        category={null}
      />
    </motion.div>
  )
}