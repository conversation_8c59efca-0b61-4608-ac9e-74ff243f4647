// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://eidaboqpmhfvrroersjh.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVpZGFib3FwbWhmdnJyb2Vyc2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIzMTMyMzksImV4cCI6MjA2Nzg4OTIzOX0.k8KFKbp3mc0sNF4OHPHIlf9ug1ZQajy7y-ip-fYpA8g";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});