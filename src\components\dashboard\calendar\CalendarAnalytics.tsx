import { useState, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  BarChart3,
  Pie<PERSON>hart,
  TrendingUp,
  Clock,
  CheckCircle2,
  AlertCircle,
  Calendar,
  ChevronDown,
  ChevronUp
} from 'lucide-react'
import { CalendarEvent } from './CalendarView'

export interface CalendarAnalyticsProps {
  events: CalendarEvent[]
  dateRange: { start: Date; end: Date }
}

export function CalendarAnalytics({ events, dateRange }: CalendarAnalyticsProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [activeChart, setActiveChart] = useState<'activity' | 'categories' | 'completion'>('activity')

  const analytics = useMemo(() => {
    const filteredEvents = events.filter(event => {
      const eventDate = new Date(event.start)
      return eventDate >= dateRange.start && eventDate <= dateRange.end
    })

    // Activity by day of week
    const activityByDay = Array(7).fill(0)
    const dayNames = ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت']
    
    filteredEvents.forEach(event => {
      const dayOfWeek = new Date(event.start).getDay()
      activityByDay[dayOfWeek]++
    })

    // Category distribution
    const categoryStats: Record<string, { count: number; color: string }> = {}
    filteredEvents.forEach(event => {
      if (!categoryStats[event.category]) {
        categoryStats[event.category] = { count: 0, color: event.color }
      }
      categoryStats[event.category].count++
    })

    // Completion stats
    const completedEvents = filteredEvents.filter(event => event.isCompleted).length
    const totalEvents = filteredEvents.length
    const completionRate = totalEvents > 0 ? (completedEvents / totalEvents) * 100 : 0

    // Priority distribution
    const priorityStats = {
      high: filteredEvents.filter(e => e.priority === 'high').length,
      medium: filteredEvents.filter(e => e.priority === 'medium').length,
      low: filteredEvents.filter(e => e.priority === 'low').length
    }

    // Busiest day
    const maxActivity = Math.max(...activityByDay)
    const busiestDayIndex = activityByDay.indexOf(maxActivity)
    const busiestDay = dayNames[busiestDayIndex]

    return {
      activityByDay: activityByDay.map((count, index) => ({
        day: dayNames[index],
        count,
        percentage: maxActivity > 0 ? (count / maxActivity) * 100 : 0
      })),
      categoryStats,
      completionRate,
      priorityStats,
      busiestDay,
      totalEvents,
      completedEvents
    }
  }, [events, dateRange])

  const chartVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }

  const ActivityChart = () => (
    <motion.div
      variants={chartVariants}
      initial="hidden"
      animate="visible"
      className="space-y-3"
    >
      <h4 className="font-medium text-foreground">النشاط الأسبوعي</h4>
      <div className="space-y-2">
        {analytics.activityByDay.map((day, index) => (
          <div key={day.day} className="flex items-center gap-3">
            <div className="w-16 text-sm text-muted-foreground">{day.day}</div>
            <div className="flex-1 bg-muted/30 rounded-full h-6 relative overflow-hidden">
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: `${day.percentage}%` }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="h-full bg-gradient-to-r from-primary to-secondary rounded-full"
              />
              <div className="absolute inset-0 flex items-center justify-center text-xs font-medium text-foreground">
                {day.count}
              </div>
            </div>
          </div>
        ))}
      </div>
    </motion.div>
  )

  const CategoriesChart = () => (
    <motion.div
      variants={chartVariants}
      initial="hidden"
      animate="visible"
      className="space-y-3"
    >
      <h4 className="font-medium text-foreground">توزيع الفئات</h4>
      <div className="grid grid-cols-2 gap-3">
        {Object.entries(analytics.categoryStats).map(([category, stats]) => (
          <motion.div
            key={category}
            whileHover={{ scale: 1.02 }}
            className="p-3 rounded-lg border border-border/30 bg-muted/10"
          >
            <div className="flex items-center gap-2 mb-2">
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: stats.color }}
              />
              <span className="text-sm font-medium">{category}</span>
            </div>
            <div className="text-2xl font-bold text-foreground">{stats.count}</div>
            <div className="text-xs text-muted-foreground">
              {((stats.count / analytics.totalEvents) * 100).toFixed(1)}%
            </div>
          </motion.div>
        ))}
      </div>
    </motion.div>
  )

  const CompletionChart = () => (
    <motion.div
      variants={chartVariants}
      initial="hidden"
      animate="visible"
      className="space-y-3"
    >
      <h4 className="font-medium text-foreground">معدل الإنجاز</h4>
      
      {/* Completion Rate Circle */}
      <div className="flex items-center justify-center">
        <div className="relative w-32 h-32">
          <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
            <circle
              cx="50"
              cy="50"
              r="40"
              stroke="currentColor"
              strokeWidth="8"
              fill="none"
              className="text-muted/30"
            />
            <motion.circle
              cx="50"
              cy="50"
              r="40"
              stroke="url(#gradient)"
              strokeWidth="8"
              fill="none"
              strokeLinecap="round"
              initial={{ strokeDasharray: "0 251.2" }}
              animate={{ 
                strokeDasharray: `${(analytics.completionRate / 100) * 251.2} 251.2` 
              }}
              transition={{ duration: 1, ease: "easeOut" }}
            />
            <defs>
              <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="hsl(var(--primary))" />
                <stop offset="100%" stopColor="hsl(var(--secondary))" />
              </linearGradient>
            </defs>
          </svg>
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <div className="text-2xl font-bold text-foreground">
                {analytics.completionRate.toFixed(0)}%
              </div>
              <div className="text-xs text-muted-foreground">مكتمل</div>
            </div>
          </div>
        </div>
      </div>

      {/* Priority Stats */}
      <div className="grid grid-cols-3 gap-2">
        <div className="text-center p-2 rounded-lg bg-red-500/10">
          <div className="text-lg font-bold text-red-600">{analytics.priorityStats.high}</div>
          <div className="text-xs text-muted-foreground">عالية</div>
        </div>
        <div className="text-center p-2 rounded-lg bg-yellow-500/10">
          <div className="text-lg font-bold text-yellow-600">{analytics.priorityStats.medium}</div>
          <div className="text-xs text-muted-foreground">متوسطة</div>
        </div>
        <div className="text-center p-2 rounded-lg bg-green-500/10">
          <div className="text-lg font-bold text-green-600">{analytics.priorityStats.low}</div>
          <div className="text-xs text-muted-foreground">منخفضة</div>
        </div>
      </div>
    </motion.div>
  )

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.4 }}
      className="bg-background rounded-xl border border-border/50 shadow-sm p-5"
    >
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="flex items-center justify-between w-full mb-4"
      >
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-lg flex items-center justify-center">
            <BarChart3 className="w-4 h-4 text-white" />
          </div>
          <h3 className="font-semibold text-foreground">تحليلات التقويم</h3>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="text-xs">
            {analytics.totalEvents} حدث
          </Badge>
          <div className="w-6 h-6 flex items-center justify-center rounded-full bg-muted/40 hover:bg-muted/60 transition-colors">
            {isExpanded ? (
              <ChevronUp className="w-4 h-4 text-muted-foreground" />
            ) : (
              <ChevronDown className="w-4 h-4 text-muted-foreground" />
            )}
          </div>
        </div>
      </button>

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-4"
          >
            {/* Quick Stats */}
            <div className="grid grid-cols-3 gap-3 mb-4">
              <div className="text-center p-3 rounded-lg bg-muted/20">
                <div className="text-xl font-bold text-primary">{analytics.totalEvents}</div>
                <div className="text-xs text-muted-foreground">إجمالي الأحداث</div>
              </div>
              <div className="text-center p-3 rounded-lg bg-green-500/10">
                <div className="text-xl font-bold text-green-600">{analytics.completedEvents}</div>
                <div className="text-xs text-muted-foreground">مكتملة</div>
              </div>
              <div className="text-center p-3 rounded-lg bg-blue-500/10">
                <div className="text-xl font-bold text-blue-600">{analytics.busiestDay}</div>
                <div className="text-xs text-muted-foreground">أكثر الأيام نشاطاً</div>
              </div>
            </div>

            {/* Chart Toggle */}
            <div className="flex items-center gap-2 mb-4">
              {[
                { key: 'activity', label: 'النشاط', icon: BarChart3 },
                { key: 'categories', label: 'الفئات', icon: PieChart },
                { key: 'completion', label: 'الإنجاز', icon: CheckCircle2 }
              ].map((chart) => {
                const Icon = chart.icon
                return (
                  <Button
                    key={chart.key}
                    variant={activeChart === chart.key ? "default" : "outline"}
                    size="sm"
                    onClick={() => setActiveChart(chart.key as any)}
                    className="text-xs"
                  >
                    <Icon className="w-3 h-3 ml-1" />
                    {chart.label}
                  </Button>
                )
              })}
            </div>

            {/* Chart Content */}
            <AnimatePresence mode="wait">
              {activeChart === 'activity' && <ActivityChart />}
              {activeChart === 'categories' && <CategoriesChart />}
              {activeChart === 'completion' && <CompletionChart />}
            </AnimatePresence>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}
