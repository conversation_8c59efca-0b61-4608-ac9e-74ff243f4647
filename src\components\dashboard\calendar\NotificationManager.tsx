import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Bell, 
  <PERSON>Off, 
  Clock, 
  Settings,
  Check,
  X
} from 'lucide-react'
import { CalendarEvent } from './CalendarView'

export interface NotificationSettings {
  enabled: boolean
  defaultReminders: number[] // minutes before event
  soundEnabled: boolean
  browserNotifications: boolean
}

export interface NotificationManagerProps {
  events: CalendarEvent[]
  settings: NotificationSettings
  onSettingsChange: (settings: NotificationSettings) => void
}

export function NotificationManager({ events, settings, onSettingsChange }: NotificationManagerProps) {
  const [permission, setPermission] = useState<NotificationPermission>('default')
  const [activeNotifications, setActiveNotifications] = useState<string[]>([])

  useEffect(() => {
    // Check notification permission on mount
    if ('Notification' in window) {
      setPermission(Notification.permission)
    }
  }, [])

  useEffect(() => {
    if (!settings.enabled || !settings.browserNotifications) return

    const checkUpcomingEvents = () => {
      const now = new Date()
      const upcomingEvents = events.filter(event => {
        const eventTime = new Date(event.start)
        const timeDiff = eventTime.getTime() - now.getTime()
        const minutesDiff = Math.floor(timeDiff / (1000 * 60))
        
        return settings.defaultReminders.includes(minutesDiff) && 
               !activeNotifications.includes(`${event.id}-${minutesDiff}`)
      })

      upcomingEvents.forEach(event => {
        const eventTime = new Date(event.start)
        const timeDiff = eventTime.getTime() - now.getTime()
        const minutesDiff = Math.floor(timeDiff / (1000 * 60))
        
        showNotification(event, minutesDiff)
        setActiveNotifications(prev => [...prev, `${event.id}-${minutesDiff}`])
      })
    }

    const interval = setInterval(checkUpcomingEvents, 60000) // Check every minute
    checkUpcomingEvents() // Check immediately

    return () => clearInterval(interval)
  }, [events, settings, activeNotifications])

  const requestNotificationPermission = async () => {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission()
      setPermission(permission)
      
      if (permission === 'granted') {
        onSettingsChange({
          ...settings,
          browserNotifications: true
        })
      }
    }
  }

  const showNotification = (event: CalendarEvent, minutesBefore: number) => {
    if (permission !== 'granted') return

    const notification = new Notification(`تذكير: ${event.title}`, {
      body: `سيبدأ الحدث خلال ${minutesBefore} دقيقة`,
      icon: '/favicon.ico',
      badge: '/favicon.ico',
      tag: `event-${event.id}`,
      requireInteraction: true,
      actions: [
        { action: 'view', title: 'عرض التفاصيل' },
        { action: 'dismiss', title: 'تجاهل' }
      ]
    })

    notification.onclick = () => {
      window.focus()
      notification.close()
    }

    // Auto close after 10 seconds
    setTimeout(() => {
      notification.close()
    }, 10000)
  }

  const toggleNotifications = () => {
    onSettingsChange({
      ...settings,
      enabled: !settings.enabled
    })
  }

  const updateReminderTimes = (newReminders: number[]) => {
    onSettingsChange({
      ...settings,
      defaultReminders: newReminders
    })
  }

  const reminderOptions = [
    { value: 5, label: '5 دقائق' },
    { value: 10, label: '10 دقائق' },
    { value: 15, label: '15 دقيقة' },
    { value: 30, label: '30 دقيقة' },
    { value: 60, label: 'ساعة واحدة' },
    { value: 120, label: 'ساعتان' },
    { value: 1440, label: 'يوم واحد' }
  ]

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-background rounded-xl border border-border/50 shadow-sm p-5"
    >
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-gradient-to-r from-amber-500 to-orange-500 rounded-lg flex items-center justify-center">
            {settings.enabled ? (
              <Bell className="w-4 h-4 text-white" />
            ) : (
              <BellOff className="w-4 h-4 text-white" />
            )}
          </div>
          <h3 className="font-semibold text-foreground">إدارة التنبيهات</h3>
        </div>
        
        <Button
          variant={settings.enabled ? "default" : "outline"}
          size="sm"
          onClick={toggleNotifications}
          className="text-sm"
        >
          {settings.enabled ? 'مفعل' : 'معطل'}
        </Button>
      </div>

      <AnimatePresence>
        {settings.enabled && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-4"
          >
            {/* Browser Notifications */}
            <div className="flex items-center justify-between p-3 bg-muted/20 rounded-lg">
              <div className="flex items-center gap-2">
                <Settings className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm font-medium">تنبيهات المتصفح</span>
              </div>
              
              {permission === 'granted' ? (
                <Badge variant="default" className="text-xs">
                  <Check className="w-3 h-3 mr-1" />
                  مفعل
                </Badge>
              ) : permission === 'denied' ? (
                <Badge variant="destructive" className="text-xs">
                  <X className="w-3 h-3 mr-1" />
                  مرفوض
                </Badge>
              ) : (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={requestNotificationPermission}
                  className="text-xs"
                >
                  تفعيل
                </Button>
              )}
            </div>

            {/* Default Reminder Times */}
            <div className="space-y-2">
              <label className="text-sm font-medium flex items-center gap-2">
                <Clock className="w-4 h-4 text-muted-foreground" />
                أوقات التذكير الافتراضية
              </label>
              
              <div className="grid grid-cols-2 gap-2">
                {reminderOptions.map((option) => (
                  <Button
                    key={option.value}
                    variant={settings.defaultReminders.includes(option.value) ? "default" : "outline"}
                    size="sm"
                    onClick={() => {
                      const newReminders = settings.defaultReminders.includes(option.value)
                        ? settings.defaultReminders.filter(r => r !== option.value)
                        : [...settings.defaultReminders, option.value]
                      updateReminderTimes(newReminders)
                    }}
                    className="text-xs justify-start"
                  >
                    {option.label}
                  </Button>
                ))}
              </div>
            </div>

            {/* Active Notifications Count */}
            {activeNotifications.length > 0 && (
              <div className="p-3 bg-primary/10 rounded-lg">
                <div className="flex items-center gap-2 text-sm">
                  <Bell className="w-4 h-4 text-primary" />
                  <span className="font-medium">
                    {activeNotifications.length} تنبيه نشط
                  </span>
                </div>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}
