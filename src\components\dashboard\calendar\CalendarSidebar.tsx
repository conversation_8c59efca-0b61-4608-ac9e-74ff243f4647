import { useState } from 'react'
import { motion } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Calendar,
  Clock,
  Filter,
  ChevronDown,
  ChevronUp,
  Circle,
  CheckCircle2,
  AlertCircle,
  TrendingUp
} from 'lucide-react'

export interface CalendarSidebarProps {
  events?: any[]
  onEventClick?: (eventId: string) => void
  onFilterChange?: (filters: any) => void
}

export function CalendarSidebar({ events = [], onEventClick, onFilterChange }: CalendarSidebarProps) {
  const [isUpcomingExpanded, setIsUpcomingExpanded] = useState(true)
  const [isStatsExpanded, setIsStatsExpanded] = useState(true)
  const [isFiltersExpanded, setIsFiltersExpanded] = useState(false)

  // Mock upcoming events
  const upcomingEvents = [
    {
      id: '1',
      title: 'اجتماع فريق العمل',
      time: '10:00 ص',
      date: 'اليوم',
      category: 'عمل',
      priority: 'high',
      color: '#ef4444'
    },
    {
      id: '2',
      title: 'موعد طبي',
      time: '2:00 م',
      date: 'غداً',
      category: 'صحة',
      priority: 'medium',
      color: '#22c55e'
    },
    {
      id: '3',
      title: 'مراجعة الدروس',
      time: '4:00 م',
      date: 'الأحد',
      category: 'دراسة',
      priority: 'high',
      color: '#3b82f6'
    }
  ]

  // Mock statistics
  const stats = {
    totalEvents: 12,
    completedEvents: 8,
    upcomingEvents: 4,
    overdueEvents: 2
  }

  const categories = [
    { name: 'عمل', count: 5, color: '#ef4444', enabled: true },
    { name: 'شخصي', count: 3, color: '#6366f1', enabled: true },
    { name: 'دراسة', count: 2, color: '#3b82f6', enabled: true },
    { name: 'صحة', count: 1, color: '#22c55e', enabled: true },
    { name: 'عائلة', count: 1, color: '#f59e0b', enabled: false }
  ]

  const priorities = [
    { name: 'عالية', count: 4, color: '#ef4444', enabled: true },
    { name: 'متوسطة', count: 6, color: '#f59e0b', enabled: true },
    { name: 'منخفضة', count: 2, color: '#22c55e', enabled: false }
  ]

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high':
        return <AlertCircle className="w-3 h-3 text-red-500" />
      case 'medium':
        return <Circle className="w-3 h-3 text-yellow-500" />
      case 'low':
        return <CheckCircle2 className="w-3 h-3 text-green-500" />
      default:
        return <Circle className="w-3 h-3 text-gray-500" />
    }
  }

  return (
    <div className="space-y-4">
      {/* Mini Calendar */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="bg-background rounded-xl border border-border/50 shadow-sm p-4"
      >
        <div className="flex items-center gap-2 mb-3">
          <Calendar className="w-5 h-5 text-primary" />
          <h3 className="font-semibold">التقويم المصغر</h3>
        </div>
        
        {/* Simple mini calendar grid */}
        <div className="grid grid-cols-7 gap-1 text-xs">
          {['ح', 'ن', 'ث', 'ر', 'خ', 'ج', 'س'].map((day) => (
            <div key={day} className="text-center p-1 text-muted-foreground font-medium">
              {day}
            </div>
          ))}
          {Array.from({ length: 35 }, (_, i) => {
            const day = i - 6 + 1
            const isToday = day === 15
            const hasEvent = [3, 7, 15, 18, 20, 25].includes(day)
            
            return (
              <button
                key={i}
                className={`
                  aspect-square text-xs rounded-md transition-colors relative
                  ${day > 0 && day <= 31 ? 'hover:bg-muted/50' : 'invisible'}
                  ${isToday ? 'bg-primary text-white font-bold' : ''}
                  ${hasEvent && !isToday ? 'bg-muted text-foreground font-medium' : ''}
                `}
              >
                {day > 0 && day <= 31 ? day : ''}
                {hasEvent && (
                  <div className="absolute bottom-0 right-1/2 transform translate-x-1/2 w-1 h-1 bg-primary rounded-full" />
                )}
              </button>
            )
          })}
        </div>
      </motion.div>

      {/* Upcoming Events */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
        className="bg-background rounded-xl border border-border/50 shadow-sm p-4"
      >
        <button
          onClick={() => setIsUpcomingExpanded(!isUpcomingExpanded)}
          className="flex items-center justify-between w-full mb-3"
        >
          <div className="flex items-center gap-2">
            <Clock className="w-5 h-5 text-primary" />
            <h3 className="font-semibold">الأحداث القادمة</h3>
          </div>
          {isUpcomingExpanded ? (
            <ChevronUp className="w-4 h-4 text-muted-foreground" />
          ) : (
            <ChevronDown className="w-4 h-4 text-muted-foreground" />
          )}
        </button>

        {isUpcomingExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
            className="space-y-3"
          >
            {upcomingEvents.map((event, index) => (
              <motion.button
                key={event.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.2, delay: index * 0.1 }}
                onClick={() => onEventClick?.(event.id)}
                className="w-full text-right p-3 rounded-lg border border-border/30 hover:border-primary/30 hover:bg-muted/30 transition-all group"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <div
                        className="w-2 h-2 rounded-full"
                        style={{ backgroundColor: event.color }}
                      />
                      <span className="font-medium text-sm group-hover:text-primary transition-colors">
                        {event.title}
                      </span>
                    </div>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <span>{event.date}</span>
                      <span>•</span>
                      <span>{event.time}</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-1">
                    {getPriorityIcon(event.priority)}
                    <Badge variant="secondary" className="text-xs">
                      {event.category}
                    </Badge>
                  </div>
                </div>
              </motion.button>
            ))}
          </motion.div>
        )}
      </motion.div>

      {/* Statistics */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
        className="bg-background rounded-xl border border-border/50 shadow-sm p-4"
      >
        <button
          onClick={() => setIsStatsExpanded(!isStatsExpanded)}
          className="flex items-center justify-between w-full mb-3"
        >
          <div className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5 text-primary" />
            <h3 className="font-semibold">الإحصائيات</h3>
          </div>
          {isStatsExpanded ? (
            <ChevronUp className="w-4 h-4 text-muted-foreground" />
          ) : (
            <ChevronDown className="w-4 h-4 text-muted-foreground" />
          )}
        </button>

        {isStatsExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
            className="grid grid-cols-2 gap-3"
          >
            <div className="text-center p-3 rounded-lg bg-muted/30">
              <div className="text-2xl font-bold text-primary">{stats.totalEvents}</div>
              <div className="text-xs text-muted-foreground">إجمالي الأحداث</div>
            </div>
            <div className="text-center p-3 rounded-lg bg-green-500/10">
              <div className="text-2xl font-bold text-green-600">{stats.completedEvents}</div>
              <div className="text-xs text-muted-foreground">مكتملة</div>
            </div>
            <div className="text-center p-3 rounded-lg bg-blue-500/10">
              <div className="text-2xl font-bold text-blue-600">{stats.upcomingEvents}</div>
              <div className="text-xs text-muted-foreground">قادمة</div>
            </div>
            <div className="text-center p-3 rounded-lg bg-red-500/10">
              <div className="text-2xl font-bold text-red-600">{stats.overdueEvents}</div>
              <div className="text-xs text-muted-foreground">متأخرة</div>
            </div>
          </motion.div>
        )}
      </motion.div>

      {/* Filters */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.3 }}
        className="bg-background rounded-xl border border-border/50 shadow-sm p-4"
      >
        <button
          onClick={() => setIsFiltersExpanded(!isFiltersExpanded)}
          className="flex items-center justify-between w-full mb-3"
        >
          <div className="flex items-center gap-2">
            <Filter className="w-5 h-5 text-primary" />
            <h3 className="font-semibold">التصفية</h3>
          </div>
          {isFiltersExpanded ? (
            <ChevronUp className="w-4 h-4 text-muted-foreground" />
          ) : (
            <ChevronDown className="w-4 h-4 text-muted-foreground" />
          )}
        </button>

        {isFiltersExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
            className="space-y-4"
          >
            {/* Categories */}
            <div>
              <h4 className="text-sm font-medium mb-2">الفئات</h4>
              <div className="space-y-2">
                {categories.map((category) => (
                  <label key={category.name} className="flex items-center justify-between cursor-pointer">
                    <div className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        checked={category.enabled}
                        className="rounded border-border"
                      />
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: category.color }}
                      />
                      <span className="text-sm">{category.name}</span>
                    </div>
                    <Badge variant="secondary" className="text-xs">
                      {category.count}
                    </Badge>
                  </label>
                ))}
              </div>
            </div>

            {/* Priorities */}
            <div>
              <h4 className="text-sm font-medium mb-2">الأولوية</h4>
              <div className="space-y-2">
                {priorities.map((priority) => (
                  <label key={priority.name} className="flex items-center justify-between cursor-pointer">
                    <div className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        checked={priority.enabled}
                        className="rounded border-border"
                      />
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: priority.color }}
                      />
                      <span className="text-sm">{priority.name}</span>
                    </div>
                    <Badge variant="secondary" className="text-xs">
                      {priority.count}
                    </Badge>
                  </label>
                ))}
              </div>
            </div>
          </motion.div>
        )}
      </motion.div>
    </div>
  )
}
