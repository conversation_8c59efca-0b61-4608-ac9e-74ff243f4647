import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Calendar,
  Clock,
  Filter,
  ChevronDown,
  ChevronUp,
  Circle,
  CheckCircle2,
  AlertCircle,
  TrendingUp
} from 'lucide-react'

export interface CalendarSidebarProps {
  events?: any[]
  onEventClick?: (eventId: string) => void
  onFilterChange?: (filters: any) => void
}

export function CalendarSidebar({ events = [], onEventClick, onFilterChange }: CalendarSidebarProps) {
  const [isUpcomingExpanded, setIsUpcomingExpanded] = useState(true)
  const [isStatsExpanded, setIsStatsExpanded] = useState(true)
  const [isFiltersExpanded, setIsFiltersExpanded] = useState(false)

  // Mock upcoming events
  const upcomingEvents = [
    {
      id: '1',
      title: 'اجتماع فريق العمل',
      time: '10:00 ص',
      date: 'اليوم',
      category: 'عمل',
      priority: 'high',
      color: '#ef4444'
    },
    {
      id: '2',
      title: 'موعد طبي',
      time: '2:00 م',
      date: 'غداً',
      category: 'صحة',
      priority: 'medium',
      color: '#22c55e'
    },
    {
      id: '3',
      title: 'مراجعة الدروس',
      time: '4:00 م',
      date: 'الأحد',
      category: 'دراسة',
      priority: 'high',
      color: '#3b82f6'
    }
  ]

  // Mock statistics
  const stats = {
    totalEvents: 12,
    completedEvents: 8,
    upcomingEvents: 4,
    overdueEvents: 2
  }

  const categories = [
    { name: 'عمل', count: 5, color: '#ef4444', enabled: true },
    { name: 'شخصي', count: 3, color: '#6366f1', enabled: true },
    { name: 'دراسة', count: 2, color: '#3b82f6', enabled: true },
    { name: 'صحة', count: 1, color: '#22c55e', enabled: true },
    { name: 'عائلة', count: 1, color: '#f59e0b', enabled: false }
  ]

  const priorities = [
    { name: 'عالية', count: 4, color: '#ef4444', enabled: true },
    { name: 'متوسطة', count: 6, color: '#f59e0b', enabled: true },
    { name: 'منخفضة', count: 2, color: '#22c55e', enabled: false }
  ]

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high':
        return <AlertCircle className="w-3 h-3 text-red-500" />
      case 'medium':
        return <Circle className="w-3 h-3 text-yellow-500" />
      case 'low':
        return <CheckCircle2 className="w-3 h-3 text-green-500" />
      default:
        return <Circle className="w-3 h-3 text-gray-500" />
    }
  }

  return (
    <div className="space-y-6">
      {/* Mini Calendar */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="bg-background rounded-xl border border-border/50 shadow-sm p-5"
      >
        <div className="flex items-center gap-3 mb-4">
          <div className="w-8 h-8 bg-gradient-to-r from-primary to-secondary rounded-lg flex items-center justify-center">
            <Calendar className="w-4 h-4 text-white" />
          </div>
          <h3 className="font-semibold text-foreground">التقويم المصغر</h3>
        </div>

        {/* Simple mini calendar grid */}
        <div className="grid grid-cols-7 gap-1 text-xs">
          {['ح', 'ن', 'ث', 'ر', 'خ', 'ج', 'س'].map((day) => (
            <div key={day} className="text-center p-2 text-muted-foreground font-semibold">
              {day}
            </div>
          ))}
          {Array.from({ length: 35 }, (_, i) => {
            const day = i - 6 + 1
            const isToday = day === 15
            const hasEvent = [3, 7, 15, 18, 20, 25].includes(day)

            return (
              <button
                key={i}
                className={`
                  aspect-square text-xs rounded-lg transition-all duration-200 relative font-medium
                  ${day > 0 && day <= 31 ? 'hover:bg-muted/50 hover:scale-105' : 'invisible'}
                  ${isToday ? 'bg-gradient-to-r from-primary to-secondary text-white font-bold shadow-md' : ''}
                  ${hasEvent && !isToday ? 'bg-muted/60 text-foreground font-semibold' : ''}
                `}
              >
                {day > 0 && day <= 31 ? day : ''}
                {hasEvent && (
                  <div className="absolute bottom-0.5 right-1/2 transform translate-x-1/2 w-1.5 h-1.5 bg-primary rounded-full shadow-sm" />
                )}
              </button>
            )
          })}
        </div>
      </motion.div>

      {/* Upcoming Events */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
        className="bg-background rounded-xl border border-border/50 shadow-sm p-5"
      >
        <button
          onClick={() => setIsUpcomingExpanded(!isUpcomingExpanded)}
          className="flex items-center justify-between w-full mb-4"
        >
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center">
              <Clock className="w-4 h-4 text-white" />
            </div>
            <h3 className="font-semibold text-foreground">الأحداث القادمة</h3>
          </div>
          <div className="w-6 h-6 flex items-center justify-center rounded-full bg-muted/40 hover:bg-muted/60 transition-colors">
            {isUpcomingExpanded ? (
              <ChevronUp className="w-4 h-4 text-muted-foreground" />
            ) : (
              <ChevronDown className="w-4 h-4 text-muted-foreground" />
            )}
          </div>
        </button>

        <AnimatePresence>
          {isUpcomingExpanded && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="space-y-3"
            >
              {upcomingEvents.map((event, index) => (
                <motion.button
                  key={event.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.2, delay: index * 0.1 }}
                  onClick={() => onEventClick?.(event.id)}
                  className="w-full text-right p-3 rounded-lg border border-border/30 hover:border-primary/30 hover:bg-muted/20 transition-all group relative overflow-hidden"
                >
                  {/* Background gradient based on category */}
                  <div
                    className="absolute inset-0 opacity-5"
                    style={{
                      background: `linear-gradient(to right, ${event.color}22, transparent)`
                    }}
                  />

                  <div className="relative flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: event.color }}
                        />
                        <span className="font-medium text-sm group-hover:text-primary transition-colors">
                          {event.title}
                        </span>
                      </div>
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <Clock className="w-3 h-3" />
                        <span>{event.time}</span>
                        <span>•</span>
                        <span className="font-medium">{event.date}</span>
                      </div>
                    </div>
                    <div className="flex items-center gap-1">
                      {getPriorityIcon(event.priority)}
                      <Badge
                        variant="outline"
                        className="text-xs font-medium"
                        style={{
                          borderColor: `${event.color}50`,
                          backgroundColor: `${event.color}10`,
                          color: event.color
                        }}
                      >
                        {event.category}
                      </Badge>
                    </div>
                  </div>
                </motion.button>
              ))}

              {upcomingEvents.length === 0 && (
                <div className="text-center py-6 bg-muted/20 rounded-lg">
                  <Calendar className="w-10 h-10 text-muted-foreground mx-auto mb-2 opacity-50" />
                  <p className="text-sm text-muted-foreground">لا توجد أحداث قادمة</p>
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>

      {/* Statistics */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
        className="bg-background rounded-xl border border-border/50 shadow-sm p-4"
      >
        <button
          onClick={() => setIsStatsExpanded(!isStatsExpanded)}
          className="flex items-center justify-between w-full mb-3"
        >
          <div className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5 text-primary" />
            <h3 className="font-semibold">الإحصائيات</h3>
          </div>
          {isStatsExpanded ? (
            <ChevronUp className="w-4 h-4 text-muted-foreground" />
          ) : (
            <ChevronDown className="w-4 h-4 text-muted-foreground" />
          )}
        </button>

        {isStatsExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
            className="grid grid-cols-2 gap-3"
          >
            <div className="text-center p-3 rounded-lg bg-muted/30">
              <div className="text-2xl font-bold text-primary">{stats.totalEvents}</div>
              <div className="text-xs text-muted-foreground">إجمالي الأحداث</div>
            </div>
            <div className="text-center p-3 rounded-lg bg-green-500/10">
              <div className="text-2xl font-bold text-green-600">{stats.completedEvents}</div>
              <div className="text-xs text-muted-foreground">مكتملة</div>
            </div>
            <div className="text-center p-3 rounded-lg bg-blue-500/10">
              <div className="text-2xl font-bold text-blue-600">{stats.upcomingEvents}</div>
              <div className="text-xs text-muted-foreground">قادمة</div>
            </div>
            <div className="text-center p-3 rounded-lg bg-red-500/10">
              <div className="text-2xl font-bold text-red-600">{stats.overdueEvents}</div>
              <div className="text-xs text-muted-foreground">متأخرة</div>
            </div>
          </motion.div>
        )}
      </motion.div>

      {/* Filters */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.3 }}
        className="bg-background rounded-xl border border-border/50 shadow-sm p-4"
      >
        <button
          onClick={() => setIsFiltersExpanded(!isFiltersExpanded)}
          className="flex items-center justify-between w-full mb-3"
        >
          <div className="flex items-center gap-2">
            <Filter className="w-5 h-5 text-primary" />
            <h3 className="font-semibold">التصفية</h3>
          </div>
          {isFiltersExpanded ? (
            <ChevronUp className="w-4 h-4 text-muted-foreground" />
          ) : (
            <ChevronDown className="w-4 h-4 text-muted-foreground" />
          )}
        </button>

        {isFiltersExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
            className="space-y-4"
          >
            {/* Categories */}
            <div>
              <h4 className="text-sm font-medium mb-2">الفئات</h4>
              <div className="space-y-2">
                {categories.map((category) => (
                  <label key={category.name} className="flex items-center justify-between cursor-pointer">
                    <div className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        checked={category.enabled}
                        className="rounded border-border"
                      />
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: category.color }}
                      />
                      <span className="text-sm">{category.name}</span>
                    </div>
                    <Badge variant="secondary" className="text-xs">
                      {category.count}
                    </Badge>
                  </label>
                ))}
              </div>
            </div>

            {/* Priorities */}
            <div>
              <h4 className="text-sm font-medium mb-2">الأولوية</h4>
              <div className="space-y-2">
                {priorities.map((priority) => (
                  <label key={priority.name} className="flex items-center justify-between cursor-pointer">
                    <div className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        checked={priority.enabled}
                        className="rounded border-border"
                      />
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: priority.color }}
                      />
                      <span className="text-sm">{priority.name}</span>
                    </div>
                    <Badge variant="secondary" className="text-xs">
                      {priority.count}
                    </Badge>
                  </label>
                ))}
              </div>
            </div>
          </motion.div>
        )}
      </motion.div>
    </div>
  )
}
