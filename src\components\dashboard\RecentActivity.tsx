import { motion } from "framer-motion"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Clock, 
  Brain, 
  Mic, 
  FileText, 
  Lightbulb,
  ArrowLeft,
  MoreHorizontal
} from "lucide-react"

interface ActivityItem {
  id: string
  title: string
  type: 'summary' | 'expansion' | 'voice-to-text' | 'created'
  timestamp: string
  category?: string
  status: 'completed' | 'processing' | 'failed'
}

const activities: ActivityItem[] = [
  {
    id: '1',
    title: 'ملاحظات اجتماع الفريق الأسبوعي',
    type: 'summary',
    timestamp: 'منذ 5 دقائق',
    category: 'العمل',
    status: 'completed'
  },
  {
    id: '2',
    title: 'أفكار مشروع التطبيق الجديد',
    type: 'expansion',
    timestamp: 'منذ 15 دقيقة',
    category: 'مشاريع',
    status: 'completed'
  },
  {
    id: '3',
    title: 'تسجيل محاضرة الذكاء الاصطناعي',
    type: 'voice-to-text',
    timestamp: 'منذ 30 دقيقة',
    category: 'تعليم',
    status: 'processing'
  },
  {
    id: '4',
    title: 'قائمة مهام الأسبوع القادم',
    type: 'created',
    timestamp: 'منذ ساعة',
    category: 'شخصي',
    status: 'completed'
  },
  {
    id: '5',
    title: 'ملخص كتاب إدارة الوقت',
    type: 'summary',
    timestamp: 'منذ ساعتين',
    category: 'قراءة',
    status: 'completed'
  },
  {
    id: '6',
    title: 'خطة السفر الصيفية',
    type: 'expansion',
    timestamp: 'منذ 3 ساعات',
    category: 'شخصي',
    status: 'failed'
  }
]

const getActivityIcon = (type: ActivityItem['type']) => {
  switch (type) {
    case 'summary':
      return Brain
    case 'expansion':
      return Lightbulb
    case 'voice-to-text':
      return Mic
    case 'created':
      return FileText
    default:
      return FileText
  }
}

const getActivityColor = (type: ActivityItem['type']) => {
  switch (type) {
    case 'summary':
      return 'text-purple-600'
    case 'expansion':
      return 'text-orange-600'
    case 'voice-to-text':
      return 'text-green-600'
    case 'created':
      return 'text-blue-600'
    default:
      return 'text-gray-600'
  }
}

const getActivityBgColor = (type: ActivityItem['type']) => {
  switch (type) {
    case 'summary':
      return 'bg-purple-100 dark:bg-purple-900/20'
    case 'expansion':
      return 'bg-orange-100 dark:bg-orange-900/20'
    case 'voice-to-text':
      return 'bg-green-100 dark:bg-green-900/20'
    case 'created':
      return 'bg-blue-100 dark:bg-blue-900/20'
    default:
      return 'bg-gray-100 dark:bg-gray-900/20'
  }
}

const getActivityLabel = (type: ActivityItem['type']) => {
  switch (type) {
    case 'summary':
      return 'تلخيص'
    case 'expansion':
      return 'توسيع'
    case 'voice-to-text':
      return 'صوت لنص'
    case 'created':
      return 'إنشاء'
    default:
      return 'نشاط'
  }
}

const getStatusColor = (status: ActivityItem['status']) => {
  switch (status) {
    case 'completed':
      return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
    case 'processing':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
    case 'failed':
      return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
  }
}

const getStatusLabel = (status: ActivityItem['status']) => {
  switch (status) {
    case 'completed':
      return 'مكتمل'
    case 'processing':
      return 'قيد المعالجة'
    case 'failed':
      return 'فشل'
    default:
      return 'غير معروف'
  }
}

export function RecentActivity() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.4 }}
    >
      <Card className="border-border/50">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center space-x-2 space-x-reverse">
            <Clock className="w-5 h-5 text-primary" />
            <span>النشاط الأخير</span>
          </CardTitle>
          <Button variant="ghost" size="sm" className="text-primary hover:text-primary/80">
            عرض الكل
            <ArrowLeft className="w-4 h-4 mr-2" />
          </Button>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {activities.map((activity, index) => {
            const Icon = getActivityIcon(activity.type)
            
            return (
              <motion.div
                key={activity.id}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="flex items-center space-x-4 space-x-reverse p-3 rounded-lg hover:bg-muted/30 transition-colors cursor-pointer group"
              >
                <div className={`w-10 h-10 rounded-lg ${getActivityBgColor(activity.type)} flex items-center justify-center flex-shrink-0`}>
                  <Icon className={`w-5 h-5 ${getActivityColor(activity.type)}`} />
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 space-x-reverse mb-1">
                    <h4 className="font-medium text-foreground truncate">
                      {activity.title}
                    </h4>
                    <Badge variant="secondary" className="text-xs">
                      {getActivityLabel(activity.type)}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center space-x-2 space-x-reverse text-sm text-muted-foreground">
                    <span>{activity.timestamp}</span>
                    {activity.category && (
                      <>
                        <span>•</span>
                        <span>{activity.category}</span>
                      </>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center space-x-2 space-x-reverse">
                  <Badge className={`text-xs ${getStatusColor(activity.status)}`}>
                    {getStatusLabel(activity.status)}
                  </Badge>
                  
                  <Button
                    variant="ghost"
                    size="icon"
                    className="opacity-0 group-hover:opacity-100 transition-opacity w-8 h-8"
                  >
                    <MoreHorizontal className="w-4 h-4" />
                  </Button>
                </div>
              </motion.div>
            )
          })}
          
          {activities.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <Clock className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>لا يوجد نشاط حديث</p>
              <p className="text-sm">ابدأ بإنشاء ملاحظة جديدة!</p>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  )
}
