'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Search, 
  Filter, 
  ChevronDown,
  Bell,
  Clock,
  Calendar,
  Zap,
  AlertTriangle,
  SortAsc,
  SortDesc
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { FilterState } from '@/pages/dashboard/Notifications'

interface NotificationsFilterProps {
  filter: FilterState
  onFilterChange: (filter: Partial<FilterState>) => void
  isLoading: boolean
}

interface TabOption {
  id: FilterState['activeTab']
  label: string
  icon: React.ReactNode
  count?: number
}

interface SortOption {
  value: FilterState['sortBy']
  label: string
}

export function NotificationsFilter({ filter, onFilterChange, isLoading }: NotificationsFilterProps) {
  const [showSortDropdown, setShowSortDropdown] = useState(false)

  const tabs: TabOption[] = [
    {
      id: 'all',
      label: 'الكل',
      icon: <Bell className="w-4 h-4" />
    },
    {
      id: 'reminders',
      label: 'التذكيرات',
      icon: <Clock className="w-4 h-4" />
    },
    {
      id: 'system',
      label: 'تنبيهات النظام',
      icon: <AlertTriangle className="w-4 h-4" />
    },
    {
      id: 'events',
      label: 'الأحداث القادمة',
      icon: <Calendar className="w-4 h-4" />
    },
    {
      id: 'ai',
      label: 'الذكاء الاصطناعي',
      icon: <Zap className="w-4 h-4" />
    }
  ]

  const sortOptions: SortOption[] = [
    { value: 'date', label: 'التاريخ' },
    { value: 'type', label: 'النوع' },
    { value: 'priority', label: 'الأولوية' }
  ]

  const handleTabChange = (tabId: FilterState['activeTab']) => {
    onFilterChange({ activeTab: tabId })
  }

  const handleSearchChange = (value: string) => {
    onFilterChange({ searchQuery: value })
  }

  const handleSortChange = (sortBy: FilterState['sortBy']) => {
    onFilterChange({ sortBy })
    setShowSortDropdown(false)
  }

  const toggleSortOrder = () => {
    onFilterChange({ 
      sortOrder: filter.sortOrder === 'asc' ? 'desc' : 'asc' 
    })
  }

  if (isLoading) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="bg-background rounded-xl border border-border/50 shadow-sm p-6"
      >
        <div className="space-y-4">
          {/* Tabs Skeleton */}
          <div className="flex flex-wrap gap-2">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="h-10 bg-muted/50 rounded-lg animate-pulse w-24"></div>
            ))}
          </div>
          
          {/* Search and Sort Skeleton */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 h-10 bg-muted/50 rounded-lg animate-pulse"></div>
            <div className="h-10 bg-muted/50 rounded-lg animate-pulse w-32"></div>
          </div>
        </div>
      </motion.div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.1 }}
      className="bg-background rounded-xl border border-border/50 shadow-sm p-6"
    >
      <div className="space-y-6">
        {/* Filter Tabs */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4 text-muted-foreground" />
            <h3 className="text-sm font-medium text-foreground">تصفية التنبيهات</h3>
          </div>
          
          <div className="flex flex-wrap gap-2">
            {tabs.map((tab, index) => (
              <motion.button
                key={tab.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                onClick={() => handleTabChange(tab.id)}
                className={`
                  flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200
                  ${filter.activeTab === tab.id
                    ? 'bg-primary text-primary-foreground shadow-md'
                    : 'bg-muted/50 text-muted-foreground hover:bg-muted hover:text-foreground'
                  }
                `}
              >
                {tab.icon}
                <span>{tab.label}</span>
                {tab.count && (
                  <span className={`
                    px-2 py-0.5 rounded-full text-xs font-bold
                    ${filter.activeTab === tab.id
                      ? 'bg-primary-foreground/20 text-primary-foreground'
                      : 'bg-primary/20 text-primary'
                    }
                  `}>
                    {tab.count}
                  </span>
                )}
              </motion.button>
            ))}
          </div>
        </div>

        {/* Search and Sort */}
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search Input */}
          <div className="flex-1 relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="البحث في التنبيهات..."
              value={filter.searchQuery}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pr-10 bg-muted/30 border-border/50 focus:border-primary/50 focus:bg-background transition-all duration-200"
            />
          </div>

          {/* Sort Controls */}
          <div className="flex items-center gap-2">
            {/* Sort Dropdown */}
            <div className="relative">
              <Button
                variant="outline"
                onClick={() => setShowSortDropdown(!showSortDropdown)}
                className="flex items-center gap-2 bg-muted/30 border-border/50 hover:bg-muted hover:border-border"
              >
                <span className="text-sm">
                  ترتيب: {sortOptions.find(opt => opt.value === filter.sortBy)?.label}
                </span>
                <ChevronDown className={`w-4 h-4 transition-transform duration-200 ${
                  showSortDropdown ? 'rotate-180' : ''
                }`} />
              </Button>

              <AnimatePresence>
                {showSortDropdown && (
                  <motion.div
                    initial={{ opacity: 0, y: -10, scale: 0.95 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: -10, scale: 0.95 }}
                    transition={{ duration: 0.2 }}
                    className="absolute top-full left-0 mt-2 w-48 bg-background rounded-lg border border-border/50 shadow-lg z-50"
                  >
                    <div className="p-2">
                      {sortOptions.map((option) => (
                        <button
                          key={option.value}
                          onClick={() => handleSortChange(option.value)}
                          className={`
                            w-full text-right px-3 py-2 rounded-md text-sm transition-colors duration-200
                            ${filter.sortBy === option.value
                              ? 'bg-primary/10 text-primary font-medium'
                              : 'text-foreground hover:bg-muted/50'
                            }
                          `}
                        >
                          {option.label}
                        </button>
                      ))}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Sort Order Toggle */}
            <Button
              variant="outline"
              size="sm"
              onClick={toggleSortOrder}
              className="bg-muted/30 border-border/50 hover:bg-muted hover:border-border"
            >
              {filter.sortOrder === 'asc' ? (
                <SortAsc className="w-4 h-4" />
              ) : (
                <SortDesc className="w-4 h-4" />
              )}
            </Button>
          </div>
        </div>

        {/* Active Filters Summary */}
        {(filter.searchQuery || filter.activeTab !== 'all') && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            transition={{ duration: 0.3 }}
            className="flex items-center gap-2 text-sm text-muted-foreground"
          >
            <span>المرشحات النشطة:</span>
            {filter.activeTab !== 'all' && (
              <span className="px-2 py-1 bg-primary/10 text-primary rounded-md">
                {tabs.find(tab => tab.id === filter.activeTab)?.label}
              </span>
            )}
            {filter.searchQuery && (
              <span className="px-2 py-1 bg-secondary/10 text-secondary rounded-md">
                البحث: "{filter.searchQuery}"
              </span>
            )}
            <button
              onClick={() => onFilterChange({ activeTab: 'all', searchQuery: '' })}
              className="text-primary hover:text-primary/80 transition-colors"
            >
              مسح الكل
            </button>
          </motion.div>
        )}
      </div>
    </motion.div>
  )
}
