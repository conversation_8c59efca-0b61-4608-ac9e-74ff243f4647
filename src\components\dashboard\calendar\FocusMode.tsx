import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Focus,
  Clock,
  Bell,
  BellOff,
  Play,
  Pause,
  Square,
  Settings,
  Eye,
  EyeOff
} from 'lucide-react'

export interface FocusSession {
  id: string
  title: string
  duration: number // minutes
  startTime: Date
  endTime: Date
  isActive: boolean
  type: 'work' | 'break' | 'custom'
}

export interface FocusModeProps {
  onSessionStart?: (session: FocusSession) => void
  onSessionEnd?: (session: FocusSession) => void
  onNotificationBlock?: (blocked: boolean) => void
}

export function FocusMode({ onSessionStart, onSessionEnd, onNotificationBlock }: FocusModeProps) {
  const [isActive, setIsActive] = useState(false)
  const [currentSession, setCurrentSession] = useState<FocusSession | null>(null)
  const [timeRemaining, setTimeRemaining] = useState(0)
  const [sessionTitle, setSessionTitle] = useState('')
  const [sessionDuration, setSessionDuration] = useState(25) // Pomodoro default
  const [sessionType, setSessionType] = useState<'work' | 'break' | 'custom'>('work')
  const [blockNotifications, setBlockNotifications] = useState(true)
  const [showSettings, setShowSettings] = useState(false)

  // Predefined session types
  const sessionTypes = [
    { value: 'work', label: 'جلسة عمل', duration: 25, color: '#ef4444' },
    { value: 'break', label: 'استراحة', duration: 5, color: '#22c55e' },
    { value: 'custom', label: 'مخصص', duration: sessionDuration, color: '#6366f1' }
  ]

  // Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout

    if (isActive && timeRemaining > 0) {
      interval = setInterval(() => {
        setTimeRemaining(prev => {
          if (prev <= 1) {
            handleSessionComplete()
            return 0
          }
          return prev - 1
        })
      }, 1000)
    }

    return () => clearInterval(interval)
  }, [isActive, timeRemaining])

  // Notification blocking effect
  useEffect(() => {
    if (isActive && blockNotifications) {
      onNotificationBlock?.(true)
      
      // Block browser notifications
      if ('Notification' in window) {
        const originalRequestPermission = Notification.requestPermission
        Notification.requestPermission = () => Promise.resolve('denied' as NotificationPermission)
        
        return () => {
          Notification.requestPermission = originalRequestPermission
          onNotificationBlock?.(false)
        }
      }
    }
  }, [isActive, blockNotifications, onNotificationBlock])

  const startSession = () => {
    const selectedType = sessionTypes.find(t => t.value === sessionType)
    const duration = sessionType === 'custom' ? sessionDuration : selectedType?.duration || 25
    
    const session: FocusSession = {
      id: Date.now().toString(),
      title: sessionTitle || selectedType?.label || 'جلسة تركيز',
      duration,
      startTime: new Date(),
      endTime: new Date(Date.now() + duration * 60 * 1000),
      isActive: true,
      type: sessionType
    }

    setCurrentSession(session)
    setTimeRemaining(duration * 60) // Convert to seconds
    setIsActive(true)
    onSessionStart?.(session)
  }

  const pauseSession = () => {
    setIsActive(false)
  }

  const resumeSession = () => {
    setIsActive(true)
  }

  const stopSession = () => {
    if (currentSession) {
      const endedSession = {
        ...currentSession,
        endTime: new Date(),
        isActive: false
      }
      onSessionEnd?.(endedSession)
    }
    
    setIsActive(false)
    setCurrentSession(null)
    setTimeRemaining(0)
  }

  const handleSessionComplete = () => {
    if (currentSession) {
      const completedSession = {
        ...currentSession,
        endTime: new Date(),
        isActive: false
      }
      onSessionEnd?.(completedSession)
    }

    setIsActive(false)
    setCurrentSession(null)
    
    // Show completion notification
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification('انتهت جلسة التركيز!', {
        body: `تم إكمال ${currentSession?.title} بنجاح`,
        icon: '/favicon.ico'
      })
    }
  }

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const getProgressPercentage = () => {
    if (!currentSession) return 0
    const totalSeconds = currentSession.duration * 60
    return ((totalSeconds - timeRemaining) / totalSeconds) * 100
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-background rounded-xl border border-border/50 shadow-sm p-5"
    >
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className={`w-8 h-8 rounded-lg flex items-center justify-center transition-colors ${
            isActive 
              ? 'bg-gradient-to-r from-red-500 to-orange-500' 
              : 'bg-gradient-to-r from-purple-500 to-indigo-500'
          }`}>
            <Focus className="w-4 h-4 text-white" />
          </div>
          <div>
            <h3 className="font-semibold text-foreground">وضع التركيز</h3>
            {isActive && currentSession && (
              <p className="text-xs text-muted-foreground">{currentSession.title}</p>
            )}
          </div>
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowSettings(!showSettings)}
          className="w-8 h-8 p-0"
        >
          <Settings className="w-4 h-4" />
        </Button>
      </div>

      {/* Active Session Display */}
      {isActive && currentSession && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="mb-6"
        >
          {/* Timer Display */}
          <div className="text-center mb-4">
            <div className="text-4xl font-bold text-foreground mb-2">
              {formatTime(timeRemaining)}
            </div>
            
            {/* Progress Ring */}
            <div className="relative w-24 h-24 mx-auto">
              <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
                <circle
                  cx="50"
                  cy="50"
                  r="40"
                  stroke="currentColor"
                  strokeWidth="8"
                  fill="none"
                  className="text-muted/30"
                />
                <motion.circle
                  cx="50"
                  cy="50"
                  r="40"
                  stroke="url(#focusGradient)"
                  strokeWidth="8"
                  fill="none"
                  strokeLinecap="round"
                  initial={{ strokeDasharray: "0 251.2" }}
                  animate={{ 
                    strokeDasharray: `${(getProgressPercentage() / 100) * 251.2} 251.2` 
                  }}
                  transition={{ duration: 0.5 }}
                />
                <defs>
                  <linearGradient id="focusGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#ef4444" />
                    <stop offset="100%" stopColor="#f97316" />
                  </linearGradient>
                </defs>
              </svg>
            </div>
          </div>

          {/* Session Controls */}
          <div className="flex justify-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={isActive ? pauseSession : resumeSession}
              className="flex items-center gap-2"
            >
              {isActive ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
              {isActive ? 'إيقاف مؤقت' : 'استئناف'}
            </Button>
            
            <Button
              variant="destructive"
              size="sm"
              onClick={stopSession}
              className="flex items-center gap-2"
            >
              <Square className="w-4 h-4" />
              إنهاء
            </Button>
          </div>
        </motion.div>
      )}

      {/* Session Setup */}
      {!isActive && (
        <AnimatePresence>
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="space-y-4"
          >
            {/* Session Type */}
            <div className="space-y-2">
              <Label>نوع الجلسة</Label>
              <div className="grid grid-cols-3 gap-2">
                {sessionTypes.map((type) => (
                  <Button
                    key={type.value}
                    variant={sessionType === type.value ? "default" : "outline"}
                    size="sm"
                    onClick={() => {
                      setSessionType(type.value as any)
                      if (type.value !== 'custom') {
                        setSessionDuration(type.duration)
                      }
                    }}
                    className="text-xs"
                  >
                    {type.label}
                  </Button>
                ))}
              </div>
            </div>

            {/* Custom Settings */}
            <AnimatePresence>
              {showSettings && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="space-y-3 border-t border-border/30 pt-3"
                >
                  <div className="space-y-2">
                    <Label htmlFor="title">عنوان الجلسة</Label>
                    <Input
                      id="title"
                      value={sessionTitle}
                      onChange={(e) => setSessionTitle(e.target.value)}
                      placeholder="أدخل عنوان الجلسة"
                      className="text-sm"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="duration">المدة (دقيقة)</Label>
                    <Input
                      id="duration"
                      type="number"
                      min="1"
                      max="180"
                      value={sessionDuration}
                      onChange={(e) => setSessionDuration(parseInt(e.target.value) || 25)}
                      className="text-sm"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="notifications" className="text-sm">
                      حظر التنبيهات
                    </Label>
                    <Switch
                      id="notifications"
                      checked={blockNotifications}
                      onCheckedChange={setBlockNotifications}
                    />
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Start Button */}
            <Button
              onClick={startSession}
              className="w-full bg-gradient-to-r from-purple-500 to-indigo-500 hover:opacity-90 text-white"
            >
              <Play className="w-4 h-4 ml-2" />
              بدء جلسة التركيز
            </Button>
          </motion.div>
        </AnimatePresence>
      )}
    </motion.div>
  )
}
