import { useState, useEffect } from "react"
import { useNavigate, useParams } from "react-router-dom"
import { motion } from "framer-motion"
import { ThemeProvider } from "@/components/theme/ThemeProvider"
import { DashboardLayout } from "@/components/dashboard/DashboardLayout"
import { EditorToolbar } from "@/components/dashboard/notes/editor/EditorToolbar"
import { EditorContent } from "@/components/dashboard/notes/editor/EditorContent"
import { EditorSidebar } from "@/components/dashboard/notes/editor/EditorSidebar"
import { useAuthStore } from "@/stores/authStore"
import { toast } from "sonner"

interface NoteEditorData {
  id: string
  title: string
  content: string
  category?: string
  categoryColor?: string
  tags: string[]
  wordCount: number
  lastSaved?: string
  isRTL: boolean
  metadata: {
    readingTime: number
    characterCount: number
    createdAt: string
    updatedAt: string
  }
}

// Sample note data (in real app, this would come from API)
const sampleNote: NoteEditorData = {
  id: "1",
  title: "أفكار المشروع الجديد",
  content: `<h1>مشروع تطبيق إدارة الملاحظات الذكي</h1>

<p>هذا المشروع يهدف إلى إنشاء تطبيق متقدم لإدارة الملاحظات باللغة العربية مع دعم كامل للكتابة من اليمين إلى اليسار.</p>

<h2>المميزات الرئيسية:</h2>

<ul>
  <li><strong>محرر نصوص غني:</strong> يدعم التنسيق المتقدم والجداول والقوائم</li>
  <li><strong>البحث الذكي:</strong> إمكانية البحث في المحتوى والعلامات</li>
  <li><strong>التصنيف التلقائي:</strong> تنظيم الملاحظات حسب الموضوع</li>
  <li><strong>المزامنة السحابية:</strong> الوصول للملاحظات من أي جهاز</li>
</ul>

<h2>التقنيات المستخدمة:</h2>

<ul>
  <li>React + TypeScript</li>
  <li>TipTap Editor</li>
  <li>Tailwind CSS</li>
  <li>Framer Motion</li>
  <li>Supabase</li>
</ul>

<blockquote>
  <p>"الهدف هو إنشاء تجربة مستخدم ممتازة للمستخدمين العرب في إدارة ملاحظاتهم وأفكارهم."</p>
</blockquote>

<h2>المراحل القادمة:</h2>

<ol>
  <li>إكمال واجهة المحرر</li>
  <li>إضافة ميزات الذكاء الاصطناعي</li>
  <li>تطوير تطبيق الهاتف المحمول</li>
  <li>إطلاق النسخة التجريبية</li>
</ol>`,
  category: "العمل",
  categoryColor: "#3B82F6",
  tags: ["مشروع", "تطوير", "عربي", "تقنية"],
  wordCount: 156,
  lastSaved: "2024-01-15T10:30:00Z",
  isRTL: true,
  metadata: {
    readingTime: 2,
    characterCount: 892,
    createdAt: "2024-01-15T09:00:00Z",
    updatedAt: "2024-01-15T10:30:00Z"
  }
}

const NoteEditor = () => {
  const navigate = useNavigate()
  const { id } = useParams<{ id: string }>()
  const { isAuthenticated, user, initializeAuth } = useAuthStore()
  
  // State management
  const [noteData, setNoteData] = useState<NoteEditorData>(sampleNote)
  const [isLoading, setIsLoading] = useState(true)
  const [isAuthLoading, setIsAuthLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)

  // Initialize auth and check authentication status
  useEffect(() => {
    const checkAuth = async () => {
      try {
        await initializeAuth()
      } catch (error) {
        console.error('Auth initialization failed:', error)
      } finally {
        setIsAuthLoading(false)
      }
    }
    
    checkAuth()
  }, [initializeAuth])

  // Handle authentication redirect with proper loading state
  useEffect(() => {
    if (!isAuthLoading && !isAuthenticated) {
      console.log('User not authenticated, redirecting to login')
      navigate('/auth/login')
    }
  }, [isAuthenticated, isAuthLoading, navigate])

  // Generate unique ID for new notes
  const generateUniqueId = () => {
    return `note_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // Load note data (simulate loading)
  useEffect(() => {
    if (user?.id && !isAuthLoading) {
      if (id === 'new') {
        // Create new note with unique ID
        const newId = generateUniqueId()
        const newNote: typeof sampleNote = {
          ...sampleNote,
          id: newId,
          title: '',
          content: '',
          category: '',
          categoryColor: '#6B7280',
          tags: [],
          wordCount: 0,
          lastSaved: '',
          metadata: {
            ...sampleNote.metadata,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        }
        setNoteData(newNote)
        console.log('Created new note with ID:', newId)
      } else if (id) {
        console.log('Loading note:', id, 'for user:', user.id)
        // Load existing note (keep sample data for now)
      }

      setTimeout(() => {
        setIsLoading(false)
      }, 500)
    }
  }, [user?.id, isAuthLoading, id])

  // Auto-save functionality
  useEffect(() => {
    const autoSaveInterval = setInterval(() => {
      if (noteData.title || noteData.content) {
        handleSave(true) // Silent save
      }
    }, 30000) // Auto-save every 30 seconds

    return () => clearInterval(autoSaveInterval)
  }, [noteData])

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 's':
            e.preventDefault()
            handleSave()
            break
          case 'f':
            e.preventDefault()
            setIsFullscreen(!isFullscreen)
            break
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isFullscreen])

  const handleSave = async (silent = false) => {
    if (!noteData.title.trim() && !noteData.content.trim()) {
      if (!silent) toast.error('لا يمكن حفظ ملاحظة فارغة')
      return
    }

    setIsSaving(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const now = new Date().toISOString()
      setNoteData(prev => ({
        ...prev,
        lastSaved: now,
        metadata: {
          ...prev.metadata,
          updatedAt: now
        }
      }))
      
      if (!silent) toast.success('تم حفظ الملاحظة بنجاح')
    } catch (error) {
      console.error('Error saving note:', error)
      if (!silent) toast.error('فشل في حفظ الملاحظة')
    } finally {
      setIsSaving(false)
    }
  }

  const handleDelete = async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
      toast.success('تم حذف الملاحظة بنجاح')
      navigate('/dashboard/notes')
    } catch (error) {
      console.error('Error deleting note:', error)
      toast.error('فشل في حذف الملاحظة')
    }
  }

  const handleTitleChange = (title: string) => {
    setNoteData(prev => ({ ...prev, title }))
  }

  const handleContentChange = (content: string) => {
    // Calculate word count safely
    try {
      const text = content.replace(/<[^>]*>/g, '').trim()
      const wordCount = text ? text.split(/\s+/).filter(word => word.length > 0).length : 0

      setNoteData(prev => ({
        ...prev,
        content: content || '',
        wordCount: wordCount || 0,
        metadata: {
          ...prev.metadata,
          characterCount: text.length || 0
        }
      }))
    } catch (error) {
      console.error('Error updating content:', error)
      // Fallback to just updating content
      setNoteData(prev => ({
        ...prev,
        content: content || ''
      }))
    }
  }

  const handleCategoryChange = (category: string, color: string) => {
    setNoteData(prev => ({
      ...prev,
      category,
      categoryColor: color
    }))
  }

  const handleTagsChange = (tags: string[]) => {
    setNoteData(prev => ({ ...prev, tags }))
  }

  const handleAIAction = (action: string) => {
    console.log('AI Action requested:', action)
    // This will be implemented when AI features are added
  }

  const formatLastSaved = (dateString?: string): string => {
    if (!dateString || typeof dateString !== 'string') return ''
    try {
      const date = new Date(dateString)
      if (isNaN(date.getTime())) return ''
      const timeString = date.toLocaleTimeString('ar-SA', {
        hour: '2-digit',
        minute: '2-digit'
      })
      return typeof timeString === 'string' ? timeString : ''
    } catch {
      return ''
    }
  }

  // Show loading while checking authentication
  if (isAuthLoading) {
    return (
      <ThemeProvider defaultTheme="system" storageKey="nots-note-editor-theme">
        <DashboardLayout>
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center space-y-4">
              <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto" />
              <p className="text-muted-foreground">جاري التحقق من المصادقة...</p>
            </div>
          </div>
        </DashboardLayout>
      </ThemeProvider>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  if (isLoading) {
    return (
      <ThemeProvider defaultTheme="system" storageKey="nots-note-editor-theme">
        <DashboardLayout>
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center space-y-4">
              <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto" />
              <p className="text-muted-foreground">جاري تحميل الملاحظة...</p>
            </div>
          </div>
        </DashboardLayout>
      </ThemeProvider>
    )
  }

  if (isFullscreen) {
    return (
      <ThemeProvider defaultTheme="system" storageKey="nots-note-editor-theme">
        <div className="fixed inset-0 z-50 bg-background flex flex-col">
          {/* Fullscreen Toolbar */}
          <EditorToolbar
            title={noteData.title || ''}
            onTitleChange={handleTitleChange}
            onSave={() => handleSave()}
            onDelete={handleDelete}
            onFullscreen={() => setIsFullscreen(!isFullscreen)}
            isFullscreen={isFullscreen}
            isSaving={isSaving}
            lastSaved={formatLastSaved(noteData.lastSaved) || ''}
          />

          {/* Fullscreen Content */}
          <div className="flex-1 flex overflow-hidden">
            {/* Editor Content */}
            <div className="flex-1 overflow-hidden">
              <div className="h-full p-6">
                <EditorContent
                  content={noteData.content || ''}
                  onContentChange={handleContentChange}
                  isRTL={noteData.isRTL || true}
                  onAIAction={handleAIAction}
                />
              </div>
            </div>

            {/* Sidebar in fullscreen */}
            <EditorSidebar
              isCollapsed={isSidebarCollapsed}
              onToggle={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
              category={noteData.category || ''}
              categoryColor={noteData.categoryColor || '#6B7280'}
              tags={noteData.tags || []}
              wordCount={noteData.wordCount || 0}
              lastSaved={formatLastSaved(noteData.lastSaved) || ''}
              onCategoryChange={handleCategoryChange}
              onTagsChange={handleTagsChange}
            />
          </div>
        </div>
      </ThemeProvider>
    )
  }

  return (
    <ThemeProvider defaultTheme="system" storageKey="nots-note-editor-theme">
      <DashboardLayout>
        <div className="h-full flex flex-col">
          {/* Normal Toolbar */}
          <EditorToolbar
            title={noteData.title || ''}
            onTitleChange={handleTitleChange}
            onSave={() => handleSave()}
            onDelete={handleDelete}
            onFullscreen={() => setIsFullscreen(!isFullscreen)}
            isFullscreen={isFullscreen}
            isSaving={isSaving}
            lastSaved={formatLastSaved(noteData.lastSaved) || ''}
          />

          {/* Normal Content */}
          <div className="flex-1 flex overflow-hidden">
            {/* Editor Content */}
            <div className="flex-1 overflow-hidden">
              <div className="h-full p-6">
                <EditorContent
                  content={noteData.content || ''}
                  onContentChange={handleContentChange}
                  isRTL={noteData.isRTL || true}
                  onAIAction={handleAIAction}
                />
              </div>
            </div>

            {/* Sidebar */}
            <EditorSidebar
              isCollapsed={isSidebarCollapsed}
              onToggle={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
              category={noteData.category || ''}
              categoryColor={noteData.categoryColor || '#6B7280'}
              tags={noteData.tags || []}
              wordCount={noteData.wordCount || 0}
              lastSaved={formatLastSaved(noteData.lastSaved) || ''}
              onCategoryChange={handleCategoryChange}
              onTagsChange={handleTagsChange}
            />
          </div>
        </div>
      </DashboardLayout>
    </ThemeProvider>
  )
}

export default NoteEditor
