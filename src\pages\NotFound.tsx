
import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { Home, ArrowLeft } from "lucide-react"
import { ThemeProvider } from "@/components/theme/ThemeProvider"

const NotFound = () => {
  return (
    <ThemeProvider defaultTheme="system" storageKey="arabic-app-theme">
      <div className="min-h-screen bg-background flex items-center justify-center font-vazirmatn" dir="rtl">
        <div className="text-center max-w-2xl mx-auto px-4">
          {/* Animated 404 */}
          <motion.div
            initial={{ scale: 0, rotate: -180 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="text-8xl md:text-9xl font-bold gradient-gold bg-clip-text text-transparent mb-8"
          >
            ٤٠٤
          </motion.div>

          {/* Title */}
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="text-3xl md:text-4xl font-bold arabic-heading mb-4"
          >
            عذراً، الصفحة غير موجودة
          </motion.h1>

          {/* Description */}
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="text-lg text-muted-foreground mb-8 arabic-body leading-relaxed"
          >
            يبدو أن الصفحة التي تبحث عنها غير موجودة أو تم نقلها. 
            لا تقلق، يمكنك العودة إلى الصفحة الرئيسية لاستكشاف تطبيقنا.
          </motion.p>

          {/* Back to Home Button */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.7 }}
          >
            <Button
              size="lg"
              onClick={() => window.location.href = "/"}
              className="text-lg px-8 py-6 rounded-full group"
            >
              <Home className="ml-2 h-5 w-5" />
              العودة للرئيسية
              <ArrowLeft className="mr-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Button>
          </motion.div>

          {/* Decorative Elements */}
          <motion.div
            animate={{
              rotate: 360,
              scale: [1, 1.1, 1],
            }}
            transition={{
              rotate: { duration: 20, repeat: Infinity, ease: "linear" },
              scale: { duration: 8, repeat: Infinity, ease: "easeInOut" },
            }}
            className="absolute top-20 right-20 w-32 h-32 rounded-full bg-gradient-to-br from-primary/20 to-accent/20 blur-xl -z-10"
          />
          
          <motion.div
            animate={{
              rotate: -360,
              scale: [1, 1.2, 1],
            }}
            transition={{
              rotate: { duration: 25, repeat: Infinity, ease: "linear" },
              scale: { duration: 12, repeat: Infinity, ease: "easeInOut" },
            }}
            className="absolute bottom-20 left-20 w-48 h-48 rounded-full bg-gradient-to-br from-accent/15 to-primary/15 blur-xl -z-10"
          />
        </div>
      </div>
    </ThemeProvider>
  );
};

export default NotFound;
