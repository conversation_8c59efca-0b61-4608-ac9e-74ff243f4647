import { useState } from "react"
import { motion } from "framer-motion"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { useNavigate, Link } from "react-router-dom"
import { Eye, EyeOff, Mail, Lock, Chrome } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { AuthLayout } from "./shared/AuthLayout"
import { loginSchema, type LoginFormData } from "@/lib/validations/auth"
import { useAuthStore } from "@/stores/authStore"
import { toast } from "sonner"

export function LoginForm() {
  const [showPassword, setShowPassword] = useState(false)
  const navigate = useNavigate()
  const { login, isLoading } = useAuthStore()

  const {
    register,
    handleSubmit,
    formState: { errors, isValid }
  } = useForm<LoginFormData>({
    resolver: zod<PERSON><PERSON>olver(loginSchema),
    mode: "onChange"
  })

  const onSubmit = async (data: LoginFormData) => {
    const success = await login(data.email, data.password)
    
    if (success) {
      toast.success("🎉 تم تسجيل الدخول بنجاح!", {
        description: "مرحباً بك مرة أخرى في Nots"
      })
      navigate('/')
    } else {
      toast.error("فشل في تسجيل الدخول", {
        description: "يرجى التحقق من البريد الإلكتروني وكلمة المرور"
      })
    }
  }

  const handleGoogleLogin = () => {
    toast.info("تسجيل الدخول مع Google", {
      description: "هذه الميزة ستكون متاحة قريباً"
    })
  }

  return (
    <AuthLayout
      title="تسجيل الدخول"
      description="أدخل بياناتك للوصول إلى حسابك في Nots"
    >
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Email Field */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="space-y-2"
        >
          <Label htmlFor="email" className="text-sm font-medium text-foreground">
            البريد الإلكتروني
          </Label>
          <div className="relative">
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              className={`pr-10 transition-all duration-200 ${
                errors.email ? 'border-red-500 focus:border-red-500' : ''
              }`}
              {...register("email")}
            />
            <Mail className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          </div>
          {errors.email && (
            <motion.p
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-sm text-red-500"
            >
              {errors.email.message}
            </motion.p>
          )}
        </motion.div>

        {/* Password Field */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="space-y-2"
        >
          <Label htmlFor="password" className="text-sm font-medium text-foreground">
            كلمة المرور
          </Label>
          <div className="relative">
            <Input
              id="password"
              type={showPassword ? "text" : "password"}
              placeholder="أدخل كلمة المرور"
              className={`px-10 transition-all duration-200 ${
                errors.password ? 'border-red-500 focus:border-red-500' : ''
              }`}
              {...register("password")}
            />
            <Lock className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
            >
              {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </button>
          </div>
          {errors.password && (
            <motion.p
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-sm text-red-500"
            >
              {errors.password.message}
            </motion.p>
          )}
        </motion.div>

        {/* Forgot Password Link */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="text-left"
        >
          <Link
            to="/auth/forgot"
            className="text-sm text-primary hover:text-primary/80 transition-colors"
          >
            نسيت كلمة المرور؟
          </Link>
        </motion.div>

        {/* Login Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <Button
            type="submit"
            disabled={!isValid || isLoading}
            className="w-full bg-gradient-to-r from-primary to-secondary hover:opacity-90 text-primary-foreground py-3 text-lg font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <div className="flex items-center space-x-2 space-x-reverse">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                <span>جاري تسجيل الدخول...</span>
              </div>
            ) : (
              'تسجيل الدخول'
            )}
          </Button>
        </motion.div>

        {/* Divider */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
          className="relative"
        >
          <Separator />
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="bg-card px-2 text-sm text-muted-foreground">أو</span>
          </div>
        </motion.div>

        {/* Google Login */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <Button
            type="button"
            variant="outline"
            onClick={handleGoogleLogin}
            className="w-full border-2 border-border text-foreground hover:bg-muted py-3 rounded-xl transition-all duration-300 flex items-center justify-center space-x-2 space-x-reverse"
          >
            <Chrome className="w-5 h-5" />
            <span>تسجيل الدخول مع Google</span>
          </Button>
        </motion.div>

        {/* Sign Up Link */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.7 }}
          className="text-center"
        >
          <p className="text-sm text-muted-foreground">
            ليس لديك حساب؟{' '}
            <Link
              to="/auth/signup"
              className="text-primary hover:text-primary/80 font-medium transition-colors"
            >
              سجل الآن
            </Link>
          </p>
        </motion.div>
      </form>
    </AuthLayout>
  )
}
