
import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft, Sparkles, Shield, Globe, CreditCard, Play } from "lucide-react"

export function Hero() {
  return (
    <section id="home" className="min-h-screen flex items-center justify-center relative overflow-hidden pt-20 md:pt-24">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-muted/30 via-accent/10 to-background"></div>

      {/* Animated Background Shapes */}
      <motion.div
        animate={{
          rotate: 360,
          scale: [1, 1.1, 1],
        }}
        transition={{
          rotate: { duration: 20, repeat: Infinity, ease: "linear" },
          scale: { duration: 8, repeat: Infinity, ease: "easeInOut" },
        }}
        className="absolute top-20 right-20 w-32 h-32 rounded-full bg-primary/20 blur-xl"
      />

      <motion.div
        animate={{
          rotate: -360,
          scale: [1, 1.2, 1],
        }}
        transition={{
          rotate: { duration: 25, repeat: Infinity, ease: "linear" },
          scale: { duration: 12, repeat: Infinity, ease: "easeInOut" },
        }}
        className="absolute bottom-20 left-20 w-48 h-48 rounded-full bg-secondary/15 blur-xl"
      />

      <div className="container mx-auto px-4 text-center relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="max-w-5xl mx-auto"
        >
          {/* Badge */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center gap-2 px-6 py-3 rounded-full bg-secondary/20 text-primary font-medium mb-8 backdrop-blur-sm border border-primary/20"
          >
            <Sparkles className="w-4 h-4" />
            <span>✨ مرحباً بك في Nots - تطبيق الملاحظات الذكي</span>
          </motion.div>

          {/* Main Heading */}
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="text-4xl md:text-6xl lg:text-7xl font-bold nots-heading mb-6 leading-tight"
          >
            <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
              ملاحظاتك الذكية
            </span>
            <br />
            <span className="text-foreground">بقوة الذكاء الاصطناعي</span>
          </motion.h1>

          {/* English Subtitle */}
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.35 }}
            className="text-lg md:text-xl text-muted-foreground mb-4 font-medium"
          >
            AI-Powered Note-Taking with Full Arabic Support
          </motion.p>

          {/* Description */}
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-lg md:text-xl text-muted-foreground mb-8 max-w-3xl mx-auto nots-body leading-relaxed"
          >
            حوّل أفكارك إلى ملاحظات منظمة ومفهومة بمساعدة الذكاء الاصطناعي.
            تلخيص تلقائي، توسيع الأفكار، وتحويل الصوت إلى نص - كل ذلك بدعم كامل للغة العربية.
          </motion.p>

          {/* CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8"
          >
            <Button
              size="lg"
              className="bg-gradient-to-r from-primary to-secondary hover:opacity-90 text-primary-foreground px-8 py-4 text-lg font-medium rounded-xl nots-shadow-lg hover:nots-shadow-xl transition-all duration-300 flex items-center gap-2"
            >
              <Sparkles className="w-5 h-5" />
              <span>✨ ابدأ مجاناً</span>
              <ArrowLeft className="w-5 h-5" />
            </Button>

            <Button
              variant="outline"
              size="lg"
              className="border-2 border-primary text-primary hover:bg-primary hover:text-primary-foreground px-8 py-4 text-lg font-medium rounded-xl transition-all duration-300 flex items-center gap-2"
            >
              <Play className="w-5 h-5" />
              <span>شاهد العرض التوضيحي</span>
            </Button>
          </motion.div>

          {/* Trust Indicators */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="flex flex-wrap justify-center items-center gap-6 mb-4 text-sm text-muted-foreground"
          >
            <div className="flex items-center gap-1">
              <Sparkles className="w-4 h-4 text-accent" />
              <span>مدعوم بـ GPT-4</span>
            </div>
            <div className="flex items-center gap-1">
              <Shield className="w-4 h-4 text-primary" />
              <span>حماية الخصوصية</span>
            </div>
            <div className="flex items-center gap-1">
              <Globe className="w-4 h-4 text-secondary" />
              <span>واجهة عربية كاملة</span>
            </div>
          </motion.div>

          {/* No Credit Card Required */}
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.7 }}
            className="text-sm text-muted-foreground flex items-center justify-center gap-1 mb-16"
          >
            <CreditCard className="w-4 h-4" />
            <span>لا حاجة لبطاقة ائتمانية</span>
          </motion.p>

          {/* Stats */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-2xl mx-auto"
          >
            {[
              { number: "20,000+", label: "مستخدم يثق بنا" },
              { number: "99.9%", label: "وقت تشغيل" },
              { number: "24/7", label: "دعم فني" },
            ].map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.9 + index * 0.1 }}
                className="text-center p-6 rounded-xl bg-card/50 backdrop-blur-sm border border-border/50 nots-shadow hover:bg-card/70 transition-colors duration-300"
              >
                <div className="text-2xl md:text-3xl font-bold text-primary mb-2">
                  {stat.number}
                </div>
                <div className="text-muted-foreground text-sm">
                  {stat.label}
                </div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>

        {/* Scroll Indicator */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 1.2 }}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        >
          <motion.div
            animate={{ y: [0, 10, 0] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            className="w-6 h-10 border-2 border-primary/30 rounded-full flex justify-center hover:border-primary/50 transition-colors duration-300"
          >
            <motion.div
              animate={{ y: [0, 12, 0] }}
              transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
              className="w-1 h-3 bg-primary rounded-full mt-2"
            />
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}
