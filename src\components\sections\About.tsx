
import { motion } from "framer-motion"
import { Button } from "@/components/ui/button"
import { <PERSON>Left, CheckCircle } from "lucide-react"

const achievements = [
  "تصميم عربي أصيل",
  "تقنيات عالمية حديثة", 
  "أداء عالي وسرعة فائقة",
  "دعم كامل للأجهزة المختلفة"
]

export function About() {
  return (
    <section id="about" className="py-20">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <motion.h2
              className="text-3xl md:text-4xl font-bold arabic-heading mb-6"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              حول التطبيق العربي
            </motion.h2>

            <motion.p
              className="text-lg text-muted-foreground mb-8 arabic-body leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
            >
              نحن نؤمن بأن التكنولوجيا يجب أن تتحدث لغتنا وتفهم ثقافتنا. 
              لذلك طورنا هذا التطبيق ليكون منصة عربية أصيلة مبنية بأحدث 
              التقنيات العالمية، مع التركيز على تجربة المستخدم العربي.
            </motion.p>

            <motion.div
              className="space-y-4 mb-8"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              viewport={{ once: true }}
            >
              {achievements.map((achievement, index) => (
                <motion.div
                  key={achievement}
                  className="flex items-center gap-3"
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.4, delay: 0.8 + index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <CheckCircle className="h-5 w-5 text-primary flex-shrink-0" />
                  <span className="arabic-body">{achievement}</span>
                </motion.div>
              ))}
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.2 }}
              viewport={{ once: true }}
            >
              <Button
                size="lg"
                className="rounded-full px-8 group"
              >
                اعرف المزيد
                <ArrowLeft className="mr-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
              </Button>
            </motion.div>
          </motion.div>

          {/* Visual Element */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="relative"
          >
            <div className="relative bg-gradient-to-br from-primary/20 via-accent/20 to-primary/10 rounded-3xl p-8 overflow-hidden">
              {/* Floating Elements */}
              <motion.div
                animate={{
                  y: [0, -20, 0],
                  rotate: [0, 5, 0],
                }}
                transition={{
                  duration: 6,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
                className="absolute top-8 right-8 w-20 h-20 bg-primary/30 rounded-2xl backdrop-blur-sm"
              />
              
              <motion.div
                animate={{
                  y: [0, 15, 0],
                  rotate: [0, -3, 0],
                }}
                transition={{
                  duration: 8,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
                className="absolute bottom-12 left-12 w-16 h-16 bg-accent/40 rounded-xl backdrop-blur-sm"
              />

              {/* Main Content */}
              <div className="relative z-10 text-center py-12">
                <motion.div
                  animate={{ scale: [1, 1.1, 1] }}
                  transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
                  className="text-6xl mb-4"
                >
                  🚀
                </motion.div>
                <h3 className="text-2xl font-bold arabic-heading mb-2">
                  مستقبل التطبيقات العربية
                </h3>
                <p className="text-muted-foreground arabic-body">
                  تجربة فريدة ومتميزة
                </p>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
