import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { FileText, Mic, Lightbulb, Image, FileType, Brain, Lock } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { useAuthStore } from "@/stores/authStore"
import { toast } from "sonner"

interface SmartFeaturesStepProps {
  onNext: () => void
  onBack: () => void
}

const availableFeatures = [
  {
    id: 'autoSummarization',
    title: 'التلخيص التلقائي',
    description: 'تلخيص ذكي للنصوص الطويلة',
    icon: FileText,
    color: 'from-blue-500 to-blue-600',
    available: true
  },
  {
    id: 'speechToText',
    title: 'تحويل الصوت إلى نص',
    description: 'تسجيل صوتي وتحويل فوري للنص',
    icon: Mic,
    color: 'from-green-500 to-green-600',
    available: true
  },
  {
    id: 'ideaExpansion',
    title: 'توسيع الأفكار',
    description: 'تطوير الأفكار البسيطة لمحتوى شامل',
    icon: Lightbulb,
    color: 'from-yellow-500 to-yellow-600',
    available: true
  }
]

const upcomingFeatures = [
  {
    id: 'ocrText',
    title: 'تحويل الصور إلى نص (OCR)',
    description: 'استخراج النص من الصور والمستندات',
    icon: Image,
    color: 'from-purple-500 to-purple-600',
    available: false
  },
  {
    id: 'documentSummary',
    title: 'تلخيص ملفات PDF/Word',
    description: 'تلخيص المستندات الطويلة تلقائياً',
    icon: FileType,
    color: 'from-red-500 to-red-600',
    available: false
  },
  {
    id: 'studyAssistant',
    title: 'مساعد ذكي للمذاكرة',
    description: 'إنشاء أسئلة وملخصات للمراجعة',
    icon: Brain,
    color: 'from-indigo-500 to-indigo-600',
    available: false
  }
]

export function SmartFeaturesStep({ onNext, onBack }: SmartFeaturesStepProps) {
  const { signUpData, updateSignUpData } = useAuthStore()
  const [features, setFeatures] = useState(signUpData.smartFeatures)

  const handleFeatureToggle = (featureId: keyof typeof features) => {
    if (featureId === 'enableAllFeatures') {
      const newValue = !features.enableAllFeatures
      setFeatures({
        autoSummarization: newValue,
        speechToText: newValue,
        ideaExpansion: newValue,
        enableAllFeatures: newValue
      })
    } else {
      const newFeatures = {
        ...features,
        [featureId]: !features[featureId]
      }
      
      // Update enableAllFeatures based on individual selections
      const allAvailableSelected = availableFeatures.every(
        feature => newFeatures[feature.id as keyof typeof features]
      )
      newFeatures.enableAllFeatures = allAvailableSelected
      
      setFeatures(newFeatures)
    }
  }

  const handleNext = () => {
    updateSignUpData({ smartFeatures: features })
    
    const selectedCount = availableFeatures.filter(
      feature => features[feature.id as keyof typeof features]
    ).length
    
    if (selectedCount > 0) {
      toast.success(`✨ تم تفعيل ${selectedCount} من الميزات الذكية!`, {
        description: "ستتمكن من استخدامها فور إنشاء حسابك"
      })
    }
    
    onNext()
  }

  return (
    <div className="space-y-6">
      {/* Master Toggle */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="p-4 bg-primary/10 border border-primary/20 rounded-xl"
      >
        <div className="flex items-center space-x-3 space-x-reverse">
          <Checkbox
            id="enableAllFeatures"
            checked={features.enableAllFeatures}
            onCheckedChange={() => handleFeatureToggle('enableAllFeatures')}
            className="data-[state=checked]:bg-primary data-[state=checked]:border-primary"
          />
          <label htmlFor="enableAllFeatures" className="flex-1 cursor-pointer">
            <div className="font-medium text-primary">
              أوافق على تجربة الميزات الذكية الآن
            </div>
            <div className="text-sm text-muted-foreground mt-1">
              تفعيل جميع الميزات المتاحة للاستخدام الفوري
            </div>
          </label>
        </div>
      </motion.div>

      {/* Available Features */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-foreground">الميزات المتاحة الآن</h3>
        
        {availableFeatures.map((feature, index) => {
          const Icon = feature.icon
          const isSelected = features[feature.id as keyof typeof features]
          
          return (
            <motion.div
              key={feature.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className={`
                p-4 rounded-xl border-2 transition-all duration-300 cursor-pointer
                ${isSelected 
                  ? 'border-primary bg-primary/10' 
                  : 'border-border hover:border-primary/50 hover:bg-muted/50'
                }
              `}
              onClick={() => handleFeatureToggle(feature.id as keyof typeof features)}
            >
              <div className="flex items-center space-x-4 space-x-reverse">
                <Checkbox
                  checked={isSelected}
                  onCheckedChange={() => handleFeatureToggle(feature.id as keyof typeof features)}
                  className="data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                />
                
                <div className={`
                  w-10 h-10 rounded-lg bg-gradient-to-r ${feature.color} 
                  flex items-center justify-center text-white shadow-md
                `}>
                  <Icon className="w-5 h-5" />
                </div>
                
                <div className="flex-1">
                  <h4 className="font-medium text-foreground">{feature.title}</h4>
                  <p className="text-sm text-muted-foreground">{feature.description}</p>
                </div>
              </div>
            </motion.div>
          )
        })}
      </div>

      {/* Upcoming Features */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-muted-foreground flex items-center space-x-2 space-x-reverse">
          <Lock className="w-5 h-5" />
          <span>قريباً</span>
        </h3>
        
        {upcomingFeatures.map((feature, index) => {
          const Icon = feature.icon
          
          return (
            <motion.div
              key={feature.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: (availableFeatures.length + index) * 0.1 }}
              className="p-4 rounded-xl border-2 border-dashed border-muted bg-muted/20 opacity-60"
            >
              <div className="flex items-center space-x-4 space-x-reverse">
                <Checkbox disabled className="opacity-50" />
                
                <div className={`
                  w-10 h-10 rounded-lg bg-gradient-to-r ${feature.color} opacity-50
                  flex items-center justify-center text-white shadow-md
                `}>
                  <Icon className="w-5 h-5" />
                </div>
                
                <div className="flex-1">
                  <h4 className="font-medium text-muted-foreground flex items-center space-x-2 space-x-reverse">
                    <span>{feature.title}</span>
                    <span className="text-xs bg-muted px-2 py-1 rounded-full">قريباً 🔒</span>
                  </h4>
                  <p className="text-sm text-muted-foreground">{feature.description}</p>
                </div>
              </div>
            </motion.div>
          )
        })}
      </div>

      {/* Action Buttons */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.8 }}
        className="flex space-x-3 space-x-reverse"
      >
        <Button
          onClick={onBack}
          variant="outline"
          className="flex-1 border-2 border-border text-foreground hover:bg-muted py-3 rounded-xl transition-all duration-300"
        >
          رجوع
        </Button>
        
        <Button
          onClick={handleNext}
          className="flex-1 bg-gradient-to-r from-primary to-secondary hover:opacity-90 text-primary-foreground py-3 text-lg font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
        >
          التالي
        </Button>
      </motion.div>
    </div>
  )
}
