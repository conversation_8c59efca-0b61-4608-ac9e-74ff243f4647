import { Document, Packer, Paragraph, TextRun, HeadingLevel, AlignmentType } from 'docx'
import { saveAs } from 'file-saver'

interface NoteData {
  title: string
  content: string
  category?: string
  tags?: string[]
  wordCount?: number
  lastSaved?: string
  metadata?: {
    createdAt?: string
    updatedAt?: string
  }
}

export async function exportToWord(noteData: NoteData) {
  try {
    // Convert HTML content to plain text and parse basic formatting
    const plainText = htmlToPlainText(noteData.content)
    const paragraphs = parseContentToParagraphs(plainText)

    // Create document
    const doc = new Document({
      sections: [{
        properties: {
          page: {
            margin: {
              top: 720,    // 0.5 inch
              right: 720,
              bottom: 720,
              left: 720,
            },
          },
        },
        children: [
          // Title
          new Paragraph({
            children: [
              new TextRun({
                text: noteData.title || 'ملاحظة بدون عنوان',
                bold: true,
                size: 32, // 16pt
                font: 'Arial',
              }),
            ],
            heading: HeadingLevel.TITLE,
            alignment: AlignmentType.RIGHT,
            spacing: {
              after: 400,
            },
          }),

          // Metadata section
          ...(noteData.category || noteData.tags?.length ? [
            new Paragraph({
              children: [
                new TextRun({
                  text: 'معلومات الملاحظة',
                  bold: true,
                  size: 24, // 12pt
                  font: 'Arial',
                }),
              ],
              heading: HeadingLevel.HEADING_2,
              alignment: AlignmentType.RIGHT,
              spacing: {
                before: 200,
                after: 200,
              },
            }),
          ] : []),

          // Category
          ...(noteData.category ? [
            new Paragraph({
              children: [
                new TextRun({
                  text: `الفئة: ${noteData.category}`,
                  font: 'Arial',
                  size: 22, // 11pt
                }),
              ],
              alignment: AlignmentType.RIGHT,
              spacing: {
                after: 100,
              },
            }),
          ] : []),

          // Tags
          ...(noteData.tags?.length ? [
            new Paragraph({
              children: [
                new TextRun({
                  text: `العلامات: ${noteData.tags.join(', ')}`,
                  font: 'Arial',
                  size: 22, // 11pt
                }),
              ],
              alignment: AlignmentType.RIGHT,
              spacing: {
                after: 100,
              },
            }),
          ] : []),

          // Word count
          ...(noteData.wordCount ? [
            new Paragraph({
              children: [
                new TextRun({
                  text: `عدد الكلمات: ${noteData.wordCount}`,
                  font: 'Arial',
                  size: 22, // 11pt
                }),
              ],
              alignment: AlignmentType.RIGHT,
              spacing: {
                after: 100,
              },
            }),
          ] : []),

          // Creation date
          ...(noteData.metadata?.createdAt ? [
            new Paragraph({
              children: [
                new TextRun({
                  text: `تاريخ الإنشاء: ${formatDate(noteData.metadata.createdAt)}`,
                  font: 'Arial',
                  size: 22, // 11pt
                }),
              ],
              alignment: AlignmentType.RIGHT,
              spacing: {
                after: 200,
              },
            }),
          ] : []),

          // Separator
          new Paragraph({
            children: [
              new TextRun({
                text: '─'.repeat(50),
                font: 'Arial',
                size: 22,
                color: '666666',
              }),
            ],
            alignment: AlignmentType.CENTER,
            spacing: {
              before: 200,
              after: 200,
            },
          }),

          // Content heading
          new Paragraph({
            children: [
              new TextRun({
                text: 'المحتوى',
                bold: true,
                size: 24, // 12pt
                font: 'Arial',
              }),
            ],
            heading: HeadingLevel.HEADING_2,
            alignment: AlignmentType.RIGHT,
            spacing: {
              before: 200,
              after: 200,
            },
          }),

          // Content paragraphs
          ...paragraphs,
        ],
      }],
    })

    // Generate and save the document
    const buffer = await Packer.toBuffer(doc)
    const fileName = `${noteData.title || 'ملاحظة'}.docx`
    
    saveAs(new Blob([buffer]), fileName)
    
    return true
  } catch (error) {
    console.error('Error exporting to Word:', error)
    throw new Error('فشل في تصدير الملف')
  }
}

function htmlToPlainText(html: string): string {
  // Remove HTML tags and decode entities
  const text = html
    .replace(/<br\s*\/?>/gi, '\n')
    .replace(/<\/p>/gi, '\n\n')
    .replace(/<\/h[1-6]>/gi, '\n\n')
    .replace(/<\/li>/gi, '\n')
    .replace(/<[^>]*>/g, '')
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .trim()

  return text
}

function parseContentToParagraphs(content: string): Paragraph[] {
  if (!content.trim()) {
    return [
      new Paragraph({
        children: [
          new TextRun({
            text: 'لا يوجد محتوى في هذه الملاحظة.',
            font: 'Arial',
            size: 22,
            italics: true,
            color: '666666',
          }),
        ],
        alignment: AlignmentType.RIGHT,
      }),
    ]
  }

  const lines = content.split('\n').filter(line => line.trim())
  
  return lines.map(line => {
    const trimmedLine = line.trim()
    
    // Check if it's a heading (starts with #)
    if (trimmedLine.startsWith('#')) {
      const level = trimmedLine.match(/^#+/)?.[0].length || 1
      const text = trimmedLine.replace(/^#+\s*/, '')
      
      return new Paragraph({
        children: [
          new TextRun({
            text,
            bold: true,
            size: level === 1 ? 28 : level === 2 ? 26 : 24,
            font: 'Arial',
          }),
        ],
        heading: level === 1 ? HeadingLevel.HEADING_1 : 
                level === 2 ? HeadingLevel.HEADING_2 : HeadingLevel.HEADING_3,
        alignment: AlignmentType.RIGHT,
        spacing: {
          before: 200,
          after: 200,
        },
      })
    }
    
    // Regular paragraph
    return new Paragraph({
      children: [
        new TextRun({
          text: trimmedLine,
          font: 'Arial',
          size: 22, // 11pt
        }),
      ],
      alignment: AlignmentType.RIGHT,
      spacing: {
        after: 200,
      },
    })
  })
}

function formatDate(dateString: string): string {
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  } catch {
    return dateString
  }
}
