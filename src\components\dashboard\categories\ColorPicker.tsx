import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Check, <PERSON>, Crown, Eye, EyeOff } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useAuthStore } from "@/stores/authStore"

const colors = [
  // Free colors (6 colors)
  { name: "أزرق", value: "#3B82F6", isFree: true },
  { name: "أخضر", value: "#10B981", isFree: true },
  { name: "بنفسجي", value: "#8B5CF6", isFree: true },
  { name: "وردي", value: "#EC4899", isFree: true },
  { name: "أحمر", value: "#EF4444", isFree: true },
  { name: "برتقالي", value: "#F59E0B", isFree: true },

  // Premium colors (10 colors)
  { name: "أصفر", value: "#EAB308", isFree: false },
  { name: "رمادي", value: "#6B7280", isFree: false },
  { name: "أزرق فاتح", value: "#06B6D4", isFree: false },
  { name: "أخضر فاتح", value: "#84CC16", isFree: false },
  { name: "بنفسجي فاتح", value: "#A855F7", isFree: false },
  { name: "وردي فاتح", value: "#F472B6", isFree: false },
  { name: "أحمر داكن", value: "#DC2626", isFree: false },
  { name: "أزرق داكن", value: "#1E40AF", isFree: false },
  { name: "أخضر داكن", value: "#059669", isFree: false },
  { name: "بني", value: "#92400E", isFree: false }
]

interface ColorPickerProps {
  value: string
  onChange: (color: string) => void
}

export function ColorPicker({ value, onChange }: ColorPickerProps) {
  const { user } = useAuthStore()
  const [showAllColors, setShowAllColors] = useState(false)

  const isPro = user?.plan_choice === 'upgrade'
  const freeColors = colors.filter(color => color.isFree)
  const premiumColors = colors.filter(color => !color.isFree)
  const displayColors = showAllColors ? colors : (isPro ? colors : freeColors)

  const handleColorClick = (color: typeof colors[0]) => {
    if (color.isFree || isPro) {
      onChange(color.value)
    }
  }

  return (
    <div className="space-y-4">
      {/* Color Grid */}
      <div className="grid grid-cols-6 gap-3">
        {displayColors.map((color, index) => {
          const isLocked = !color.isFree && !isPro
          const isSelected = value === color.value

          return (
            <motion.div
              key={color.value}
              className="relative"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.2, delay: index * 0.03 }}
            >
              <motion.button
                type="button"
                onClick={() => handleColorClick(color)}
                disabled={isLocked}
                className={`
                  relative w-12 h-12 rounded-xl shadow-sm border-2 transition-all duration-300 group
                  ${isSelected
                    ? 'border-foreground scale-110 shadow-lg ring-2 ring-primary/30'
                    : isLocked
                      ? 'border-border/20 opacity-60 cursor-not-allowed'
                      : 'border-border/30 hover:border-border hover:scale-105 hover:shadow-md'
                  }
                `}
                style={{ backgroundColor: color.value }}
                whileHover={!isLocked ? { scale: isSelected ? 1.1 : 1.05 } : {}}
                whileTap={!isLocked ? { scale: 0.95 } : {}}
                title={isLocked ? `${color.name} - يتطلب الترقية للخطة المدفوعة` : color.name}
              >
                {/* Selected Check */}
                {isSelected && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.5 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="absolute inset-0 flex items-center justify-center"
                  >
                    <div className="w-6 h-6 bg-white/90 rounded-full flex items-center justify-center shadow-sm">
                      <Check className="w-4 h-4 text-gray-800" />
                    </div>
                  </motion.div>
                )}

                {/* Lock Icon for Premium Colors */}
                {isLocked && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.5 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="absolute inset-0 flex items-center justify-center"
                  >
                    <div className="w-6 h-6 bg-black/60 rounded-full flex items-center justify-center backdrop-blur-sm">
                      <Lock className="w-3 h-3 text-white" />
                    </div>
                  </motion.div>
                )}

                {/* Premium Badge */}
                {!color.isFree && (
                  <div className="absolute -top-1 -right-1">
                    <Crown className="w-3 h-3 text-yellow-500 drop-shadow-sm" />
                  </div>
                )}
              </motion.button>
            </motion.div>
          )
        })}
      </div>

      {/* Controls */}
      <div className="flex items-center justify-between">
        {/* Show All Colors Toggle */}
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => setShowAllColors(!showAllColors)}
          className="text-xs text-muted-foreground hover:text-foreground transition-colors"
        >
          {showAllColors ? (
            <>
              <EyeOff className="w-3 h-3 mr-1" />
              إخفاء الألوان المدفوعة
            </>
          ) : (
            <>
              <Eye className="w-3 h-3 mr-1" />
              عرض جميع الألوان ({premiumColors.length} مدفوعة)
            </>
          )}
        </Button>

        {/* Upgrade Prompt for Free Users */}
        {!isPro && (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="text-xs text-muted-foreground"
          >
            <span className="flex items-center space-x-1 space-x-reverse">
              <Crown className="w-3 h-3 text-yellow-500" />
              <span>{premiumColors.length} ألوان إضافية في النسخة المدفوعة</span>
            </span>
          </motion.div>
        )}
      </div>

      {/* Free vs Pro Info */}
      <div className="grid grid-cols-2 gap-3 text-xs">
        <div className="flex items-center space-x-2 space-x-reverse p-2 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <span className="text-green-700 dark:text-green-300">
            {freeColors.length} ألوان مجانية
          </span>
        </div>
        <div className="flex items-center space-x-2 space-x-reverse p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
          <Crown className="w-3 h-3 text-yellow-600" />
          <span className="text-yellow-700 dark:text-yellow-300">
            {premiumColors.length} ألوان مدفوعة
          </span>
        </div>
      </div>
    </div>
  )
}
