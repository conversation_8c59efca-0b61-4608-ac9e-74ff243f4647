import { motion } from "framer-motion"
import { Check } from "lucide-react"

const colors = [
  { name: "أزرق", value: "#3B82F6" },
  { name: "أخضر", value: "#10B981" },
  { name: "بنفسجي", value: "#8B5CF6" },
  { name: "وردي", value: "#EC4899" },
  { name: "أحمر", value: "#EF4444" },
  { name: "برتقالي", value: "#F59E0B" },
  { name: "أصفر", value: "#EAB308" },
  { name: "رمادي", value: "#6B7280" },
  { name: "أزرق فاتح", value: "#06B6D4" },
  { name: "أخضر فاتح", value: "#84CC16" },
  { name: "بنفسجي فاتح", value: "#A855F7" },
  { name: "وردي فاتح", value: "#F472B6" }
]

interface ColorPickerProps {
  value: string
  onChange: (color: string) => void
}

export function ColorPicker({ value, onChange }: ColorPickerProps) {
  return (
    <div className="grid grid-cols-6 gap-3">
      {colors.map((color, index) => (
        <motion.button
          key={color.value}
          type="button"
          onClick={() => onChange(color.value)}
          className={`
            relative w-10 h-10 rounded-xl shadow-sm border-2 transition-all duration-200
            ${value === color.value 
              ? 'border-foreground scale-110 shadow-lg' 
              : 'border-border/30 hover:border-border hover:scale-105'
            }
          `}
          style={{ backgroundColor: color.value }}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.2, delay: index * 0.05 }}
          title={color.name}
        >
          {value === color.value && (
            <motion.div
              initial={{ opacity: 0, scale: 0.5 }}
              animate={{ opacity: 1, scale: 1 }}
              className="absolute inset-0 flex items-center justify-center"
            >
              <Check className="w-5 h-5 text-white drop-shadow-sm" />
            </motion.div>
          )}
        </motion.button>
      ))}
    </div>
  )
}
