import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { ColorPicker } from "./ColorPicker"
import { IconSelector } from "./IconSelector"
import { CategoryPreview } from "./CategoryPreview"
import type { Category } from "@/lib/supabase"
import { toast } from "sonner"

const categorySchema = z.object({
  name: z.string().min(1, "اسم الفئة مطلوب").max(50, "اسم الفئة طويل جداً"),
  description: z.string().max(200, "الوصف طويل جداً").optional(),
  color: z.string().min(1, "يرجى اختيار لون"),
  icon: z.string().min(1, "يرجى اختيار أيقونة"),
})

type CategoryFormData = z.infer<typeof categorySchema>

interface CategoryModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (data: Omit<Category, 'id' | 'user_id' | 'created_at' | 'updated_at'>) => void
  category?: Category | null
}

export function CategoryModal({ isOpen, onClose, onSave, category }: CategoryModalProps) {
  const [isLoading, setIsLoading] = useState(false)
  
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors }
  } = useForm<CategoryFormData>({
    resolver: zodResolver(categorySchema),
    defaultValues: {
      name: "",
      description: "",
      color: "#3B82F6",
      icon: "folder"
    }
  })

  // Watch form values for preview
  const watchedValues = watch()

  // Reset form when modal opens/closes or category changes
  useEffect(() => {
    if (isOpen) {
      if (category) {
        reset({
          name: category.name,
          description: category.description || "",
          color: category.color,
          icon: category.icon
        })
      } else {
        reset({
          name: "",
          description: "",
          color: "#3B82F6",
          icon: "folder"
        })
      }
    }
  }, [isOpen, category, reset])

  const onSubmit = async (data: CategoryFormData) => {
    setIsLoading(true)
    try {
      await onSave({
        name: data.name,
        description: data.description || undefined,
        color: data.color,
        icon: data.icon,
        notes_count: category?.notes_count || 0,
        is_pinned: category?.is_pinned || false,
        is_favorite: category?.is_favorite || false,
        sort_order: category?.sort_order || 0
      })
      onClose()
    } catch (error) {
      console.error('Error saving category:', error)
      toast.error('فشل في حفظ الفئة')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[95vh] overflow-hidden p-0 bg-gradient-to-br from-background via-muted/20 to-accent/10">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-secondary/5 to-transparent pointer-events-none" />

        {/* Animated Background Shapes */}
        <motion.div
          animate={{
            rotate: 360,
            scale: [1, 1.1, 1],
          }}
          transition={{
            rotate: { duration: 20, repeat: Infinity, ease: "linear" },
            scale: { duration: 8, repeat: Infinity, ease: "easeInOut" },
          }}
          className="absolute top-10 right-10 w-20 h-20 rounded-full bg-primary/10 blur-xl pointer-events-none"
        />

        <motion.div
          animate={{
            rotate: -360,
            scale: [1, 1.2, 1],
          }}
          transition={{
            rotate: { duration: 25, repeat: Infinity, ease: "linear" },
            scale: { duration: 12, repeat: Infinity, ease: "easeInOut" },
          }}
          className="absolute bottom-10 left-10 w-16 h-16 rounded-full bg-secondary/10 blur-xl pointer-events-none"
        />

        <div className="relative z-10">
          {/* Header */}
          <DialogHeader className="p-6 pb-4 border-b border-border/30 bg-gradient-to-r from-muted/30 to-accent/20">
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="flex items-center space-x-4 space-x-reverse"
            >
              <div className="w-12 h-12 bg-gradient-to-r from-primary to-secondary rounded-xl flex items-center justify-center shadow-lg">
                <motion.div
                  animate={{ rotate: [0, 10, -10, 0] }}
                  transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                >
                  📂
                </motion.div>
              </div>
              <div>
                <DialogTitle className="text-2xl font-bold text-foreground">
                  {category ? 'تعديل الفئة' : 'إنشاء فئة جديدة'}
                </DialogTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  {category ? 'قم بتحديث معلومات الفئة' : 'أضف فئة جديدة لتنظيم ملاحظاتك'}
                </p>
              </div>
            </motion.div>
          </DialogHeader>

          {/* Content */}
          <div className="max-h-[calc(95vh-120px)] overflow-y-auto">
            <form onSubmit={handleSubmit(onSubmit)} className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Form Fields - 2/3 width */}
                <div className="lg:col-span-2 space-y-6">
                  {/* Category Name */}
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.1 }}
                    className="space-y-3"
                  >
                    <Label htmlFor="name" className="text-sm font-semibold text-foreground flex items-center space-x-2 space-x-reverse">
                      <span className="w-2 h-2 bg-primary rounded-full"></span>
                      <span>اسم الفئة *</span>
                    </Label>
                    <div className="relative">
                      <Input
                        id="name"
                        {...register("name")}
                        placeholder="مثال: العمل، الدراسة، الأفكار..."
                        className="rounded-xl border-border/50 bg-background/50 backdrop-blur-sm focus:border-primary/50 focus:ring-2 focus:ring-primary/20 transition-all duration-300 text-lg py-3"
                      />
                      <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-primary/5 to-secondary/5 pointer-events-none opacity-0 group-focus-within:opacity-100 transition-opacity duration-300" />
                    </div>
                    {errors.name && (
                      <motion.p
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="text-sm text-red-600 flex items-center space-x-1 space-x-reverse"
                      >
                        <span>⚠️</span>
                        <span>{errors.name.message}</span>
                      </motion.p>
                    )}
                  </motion.div>

                  {/* Category Description */}
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.2 }}
                    className="space-y-3"
                  >
                    <Label htmlFor="description" className="text-sm font-semibold text-foreground flex items-center space-x-2 space-x-reverse">
                      <span className="w-2 h-2 bg-secondary rounded-full"></span>
                      <span>الوصف (اختياري)</span>
                    </Label>
                    <div className="relative">
                      <Textarea
                        id="description"
                        {...register("description")}
                        placeholder="وصف مختصر للفئة يساعد في تذكر محتواها..."
                        className="rounded-xl border-border/50 bg-background/50 backdrop-blur-sm focus:border-primary/50 focus:ring-2 focus:ring-primary/20 transition-all duration-300 resize-none"
                        rows={4}
                      />
                      <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-primary/5 to-secondary/5 pointer-events-none opacity-0 group-focus-within:opacity-100 transition-opacity duration-300" />
                    </div>
                    {errors.description && (
                      <motion.p
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="text-sm text-red-600 flex items-center space-x-1 space-x-reverse"
                      >
                        <span>⚠️</span>
                        <span>{errors.description.message}</span>
                      </motion.p>
                    )}
                  </motion.div>

                  {/* Color Picker */}
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                    className="space-y-3"
                  >
                    <Label className="text-sm font-semibold text-foreground flex items-center space-x-2 space-x-reverse">
                      <span className="w-2 h-2 bg-accent rounded-full"></span>
                      <span>اللون *</span>
                    </Label>
                    <div className="p-4 bg-muted/20 rounded-xl border border-border/30">
                      <ColorPicker
                        value={watchedValues.color}
                        onChange={(color) => setValue("color", color)}
                      />
                    </div>
                    {errors.color && (
                      <motion.p
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="text-sm text-red-600 flex items-center space-x-1 space-x-reverse"
                      >
                        <span>⚠️</span>
                        <span>{errors.color.message}</span>
                      </motion.p>
                    )}
                  </motion.div>

                  {/* Icon Selector */}
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.4 }}
                    className="space-y-3"
                  >
                    <Label className="text-sm font-semibold text-foreground flex items-center space-x-2 space-x-reverse">
                      <span className="w-2 h-2 bg-yellow-500 rounded-full"></span>
                      <span>الأيقونة *</span>
                    </Label>
                    <div className="p-4 bg-muted/20 rounded-xl border border-border/30">
                      <IconSelector
                        value={watchedValues.icon}
                        onChange={(icon) => setValue("icon", icon)}
                      />
                    </div>
                    {errors.icon && (
                      <motion.p
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="text-sm text-red-600 flex items-center space-x-1 space-x-reverse"
                      >
                        <span>⚠️</span>
                        <span>{errors.icon.message}</span>
                      </motion.p>
                    )}
                  </motion.div>
                </div>

                {/* Preview - 1/3 width */}
                <div className="lg:col-span-1">
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.5 }}
                    className="space-y-4 sticky top-4"
                  >
                    <Label className="text-sm font-semibold text-foreground flex items-center space-x-2 space-x-reverse">
                      <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                      <span>معاينة الفئة</span>
                    </Label>
                    <div className="relative">
                      <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-xl blur-sm" />
                      <CategoryPreview
                        name={watchedValues.name || "اسم الفئة"}
                        description={watchedValues.description}
                        color={watchedValues.color}
                        icon={watchedValues.icon}
                      />
                    </div>

                    {/* Tips */}
                    <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200 dark:border-blue-800">
                      <h4 className="text-sm font-semibold text-blue-800 dark:text-blue-200 mb-2">💡 نصائح مفيدة</h4>
                      <ul className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
                        <li>• اختر اسماً واضحاً ومميزاً للفئة</li>
                        <li>• استخدم ألواناً مختلفة لسهولة التمييز</li>
                        <li>• الوصف يساعد في تذكر محتوى الفئة</li>
                      </ul>
                    </div>
                  </motion.div>
                </div>
              </div>

              {/* Form Actions */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.6 }}
                className="flex flex-col sm:flex-row gap-4 justify-end pt-8 border-t border-border/30 mt-8"
              >
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                  disabled={isLoading}
                  className="rounded-xl border-border/50 hover:bg-muted/50 transition-all duration-300 px-8 py-3"
                >
                  إلغاء
                </Button>
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Button
                    type="submit"
                    disabled={isLoading}
                    className="bg-gradient-to-r from-primary to-secondary hover:opacity-90 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 px-8 py-3 font-semibold"
                  >
                    {isLoading ? (
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                        <span>جاري الحفظ...</span>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <span>{category ? 'حفظ التعديلات' : 'إنشاء الفئة'}</span>
                        <span>{category ? '💾' : '✨'}</span>
                      </div>
                    )}
                  </Button>
                </motion.div>
              </motion.div>
            </form>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
