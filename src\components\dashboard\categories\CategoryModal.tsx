import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { ColorPicker } from "./ColorPicker"
import { IconSelector } from "./IconSelector"
import { CategoryPreview } from "./CategoryPreview"
import type { Category } from "@/lib/supabase"
import { toast } from "sonner"

const categorySchema = z.object({
  name: z.string().min(1, "اسم الفئة مطلوب").max(50, "اسم الفئة طويل جداً"),
  description: z.string().max(200, "الوصف طويل جداً").optional(),
  color: z.string().min(1, "يرجى اختيار لون"),
  icon: z.string().min(1, "يرجى اختيار أيقونة"),
})

type CategoryFormData = z.infer<typeof categorySchema>

interface CategoryModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (data: Omit<Category, 'id' | 'user_id' | 'created_at' | 'updated_at'>) => void
  category?: Category | null
}

export function CategoryModal({ isOpen, onClose, onSave, category }: CategoryModalProps) {
  const [isLoading, setIsLoading] = useState(false)
  
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors }
  } = useForm<CategoryFormData>({
    resolver: zodResolver(categorySchema),
    defaultValues: {
      name: "",
      description: "",
      color: "#3B82F6",
      icon: "folder"
    }
  })

  // Watch form values for preview
  const watchedValues = watch()

  // Reset form when modal opens/closes or category changes
  useEffect(() => {
    if (isOpen) {
      if (category) {
        reset({
          name: category.name,
          description: category.description || "",
          color: category.color,
          icon: category.icon
        })
      } else {
        reset({
          name: "",
          description: "",
          color: "#3B82F6",
          icon: "folder"
        })
      }
    }
  }, [isOpen, category, reset])

  const onSubmit = async (data: CategoryFormData) => {
    setIsLoading(true)
    try {
      await onSave({
        name: data.name,
        description: data.description || undefined,
        color: data.color,
        icon: data.icon,
        notes_count: category?.notes_count || 0,
        is_pinned: category?.is_pinned || false,
        is_favorite: category?.is_favorite || false,
        sort_order: category?.sort_order || 0
      })
      onClose()
    } catch (error) {
      console.error('Error saving category:', error)
      toast.error('فشل في حفظ الفئة')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-foreground">
            {category ? 'تعديل الفئة' : 'إنشاء فئة جديدة'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Form Fields */}
            <div className="space-y-4">
              {/* Category Name */}
              <div className="space-y-2">
                <Label htmlFor="name" className="text-sm font-medium">
                  اسم الفئة *
                </Label>
                <Input
                  id="name"
                  {...register("name")}
                  placeholder="مثال: العمل، الدراسة، الأفكار..."
                  className="rounded-xl"
                />
                {errors.name && (
                  <p className="text-sm text-red-600">{errors.name.message}</p>
                )}
              </div>

              {/* Category Description */}
              <div className="space-y-2">
                <Label htmlFor="description" className="text-sm font-medium">
                  الوصف (اختياري)
                </Label>
                <Textarea
                  id="description"
                  {...register("description")}
                  placeholder="وصف مختصر للفئة..."
                  className="rounded-xl resize-none"
                  rows={3}
                />
                {errors.description && (
                  <p className="text-sm text-red-600">{errors.description.message}</p>
                )}
              </div>

              {/* Color Picker */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  اللون *
                </Label>
                <ColorPicker
                  value={watchedValues.color}
                  onChange={(color) => setValue("color", color)}
                />
                {errors.color && (
                  <p className="text-sm text-red-600">{errors.color.message}</p>
                )}
              </div>

              {/* Icon Selector */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  الأيقونة *
                </Label>
                <IconSelector
                  value={watchedValues.icon}
                  onChange={(icon) => setValue("icon", icon)}
                />
                {errors.icon && (
                  <p className="text-sm text-red-600">{errors.icon.message}</p>
                )}
              </div>
            </div>

            {/* Preview */}
            <div className="space-y-4">
              <Label className="text-sm font-medium">
                معاينة الفئة
              </Label>
              <CategoryPreview
                name={watchedValues.name || "اسم الفئة"}
                description={watchedValues.description}
                color={watchedValues.color}
                icon={watchedValues.icon}
              />
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex flex-col sm:flex-row gap-3 justify-end pt-6 border-t border-border/30">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
              className="rounded-xl"
            >
              إلغاء
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="bg-gradient-to-r from-primary to-secondary hover:opacity-90 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
            >
              {isLoading ? (
                <div className="flex items-center space-x-2 space-x-reverse">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  <span>جاري الحفظ...</span>
                </div>
              ) : (
                category ? 'حفظ التعديلات' : 'إنشاء الفئة'
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
