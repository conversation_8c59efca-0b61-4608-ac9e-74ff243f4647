'use client'

import { motion } from 'framer-motion'
import { 
  Bell, 
  Calendar, 
  Clock, 
  Zap, 
  AlertTriangle,
  TrendingUp
} from 'lucide-react'
import { NotificationStats } from '@/pages/dashboard/Notifications'

interface NotificationsHeaderProps {
  stats: NotificationStats
  isLoading: boolean
}

interface StatCardProps {
  title: string
  value: number
  icon: React.ReactNode
  color: string
  bgColor: string
  delay: number
  isLoading: boolean
}

function StatCard({ title, value, icon, color, bgColor, delay, isLoading }: StatCardProps) {
  if (isLoading) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay }}
        className="bg-background rounded-xl border border-border/50 shadow-sm p-6"
      >
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <div className="h-4 bg-muted/50 rounded animate-pulse w-20"></div>
            <div className="h-8 bg-muted/50 rounded animate-pulse w-12"></div>
          </div>
          <div className="w-12 h-12 bg-muted/50 rounded-lg animate-pulse"></div>
        </div>
      </motion.div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay }}
      whileHover={{ scale: 1.02 }}
      className="bg-background rounded-xl border border-border/50 shadow-sm hover:shadow-md transition-all duration-300 p-6 group"
    >
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <p className="text-sm font-medium text-muted-foreground group-hover:text-foreground transition-colors">
            {title}
          </p>
          <p className="text-3xl font-bold text-foreground">
            {value.toLocaleString('ar-SA')}
          </p>
        </div>
        <div 
          className={`w-12 h-12 rounded-lg flex items-center justify-center ${bgColor} group-hover:scale-110 transition-transform duration-300`}
        >
          <div className={`${color}`}>
            {icon}
          </div>
        </div>
      </div>
    </motion.div>
  )
}

export function NotificationsHeader({ stats, isLoading }: NotificationsHeaderProps) {
  const statCards = [
    {
      title: 'إجمالي التنبيهات',
      value: stats.total,
      icon: <Bell className="w-6 h-6" />,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100 dark:bg-blue-900/30',
      delay: 0.1
    },
    {
      title: 'التذكيرات',
      value: stats.reminders,
      icon: <Clock className="w-6 h-6" />,
      color: 'text-green-600',
      bgColor: 'bg-green-100 dark:bg-green-900/30',
      delay: 0.2
    },
    {
      title: 'الأحداث القادمة',
      value: stats.events,
      icon: <Calendar className="w-6 h-6" />,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100 dark:bg-purple-900/30',
      delay: 0.3
    },
    {
      title: 'تنبيهات الذكاء الاصطناعي',
      value: stats.ai,
      icon: <Zap className="w-6 h-6" />,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100 dark:bg-orange-900/30',
      delay: 0.4
    },
    {
      title: 'غير مقروءة',
      value: stats.unread,
      icon: <AlertTriangle className="w-6 h-6" />,
      color: 'text-red-600',
      bgColor: 'bg-red-100 dark:bg-red-900/30',
      delay: 0.5
    }
  ]

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center lg:text-right"
      >
        <div className="space-y-3">
          <motion.h1 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-3xl lg:text-4xl font-bold text-foreground"
          >
            أهلاً بك مرة أخرى، محمود 👋
          </motion.h1>
          
          <motion.p 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="text-lg text-muted-foreground max-w-3xl mx-auto lg:mx-0 leading-relaxed"
          >
            في هذا القسم، ستجد جميع تنبيهاتك القادمة والأحداث المجدولة والتذكيرات الذكية 
            التي تساعدك على إنجاز مهامك بكفاءة أكبر.
          </motion.p>
        </div>
      </motion.div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3 sm:gap-4">
        {statCards.map((stat, index) => (
          <StatCard
            key={stat.title}
            title={stat.title}
            value={stat.value}
            icon={stat.icon}
            color={stat.color}
            bgColor={stat.bgColor}
            delay={stat.delay}
            isLoading={isLoading}
          />
        ))}
      </div>

      {/* Quick Insights */}
      {!isLoading && stats.unread > 0 && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="bg-gradient-to-r from-primary/10 via-secondary/10 to-primary/10 rounded-xl border border-primary/20 p-4"
        >
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-primary/20 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-4 h-4 text-primary" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-foreground">
                لديك {stats.unread.toLocaleString('ar-SA')} تنبيه غير مقروء
              </p>
              <p className="text-xs text-muted-foreground">
                تحقق من التنبيهات الجديدة لتبقى على اطلاع بآخر التحديثات
              </p>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  )
}
