import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON>Header, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { EditorJSContent } from '@/components/dashboard/notes/editor/EditorJSContent'
import { OutputData } from '@editorjs/editorjs'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Calendar,
  Clock,
  Tag,
  Flag,
  Trash2,
  Sparkles,
  MessageSquare,
  Wand2
} from 'lucide-react'
import { CalendarEvent } from './CalendarView'

export interface EventModalProps {
  isOpen: boolean
  onClose: () => void
  event: CalendarEvent | null
  onSave: (event: CalendarEvent) => void
  onDelete: (eventId: string) => void
}

export function EventModal({ isOpen, onClose, event, onSave, onDelete }: EventModalProps) {
  const [title, setTitle] = useState('')
  const [content, setContent] = useState<OutputData | string>('')
  const [startDate, setStartDate] = useState('')
  const [startTime, setStartTime] = useState('')
  const [endDate, setEndDate] = useState('')
  const [endTime, setEndTime] = useState('')
  const [category, setCategory] = useState('شخصي')
  const [priority, setPriority] = useState<'high' | 'medium' | 'low'>('medium')
  const [color, setColor] = useState('#6366f1')
  const [showAIPanel, setShowAIPanel] = useState(false)

  useEffect(() => {
    if (event) {
      setTitle(event.title)
      setContent(event.content)
      
      // Format dates for input fields
      const startDateObj = new Date(event.start)
      setStartDate(formatDateForInput(startDateObj))
      setStartTime(formatTimeForInput(startDateObj))
      
      const endDateObj = new Date(event.end)
      setEndDate(formatDateForInput(endDateObj))
      setEndTime(formatTimeForInput(endDateObj))
      
      setCategory(event.category)
      setPriority(event.priority)
      setColor(event.color)
    }
  }, [event])

  const formatDateForInput = (date: Date) => {
    return date.toISOString().split('T')[0]
  }

  const formatTimeForInput = (date: Date) => {
    return date.toTimeString().slice(0, 5)
  }

  const handleSave = () => {
    if (!event) return
    
    const startDateTime = new Date(`${startDate}T${startTime}`)
    const endDateTime = new Date(`${endDate}T${endTime}`)
    
    const updatedEvent: CalendarEvent = {
      ...event,
      title,
      content,
      start: startDateTime,
      end: endDateTime,
      category,
      priority,
      color
    }
    
    onSave(updatedEvent)
  }

  const handleDelete = () => {
    if (event) {
      onDelete(event.id)
    }
  }

  const handleEditorChange = (data: OutputData) => {
    setContent(data)
  }

  const categoryOptions = [
    { value: 'عمل', color: '#ef4444' },
    { value: 'شخصي', color: '#6366f1' },
    { value: 'دراسة', color: '#3b82f6' },
    { value: 'صحة', color: '#22c55e' },
    { value: 'عائلة', color: '#f59e0b' }
  ]

  const priorityOptions = [
    { value: 'high', label: 'عالية', color: '#ef4444' },
    { value: 'medium', label: 'متوسطة', color: '#f59e0b' },
    { value: 'low', label: 'منخفضة', color: '#22c55e' }
  ]

  const colorOptions = [
    '#ef4444', '#f59e0b', '#22c55e', '#3b82f6', '#6366f1', '#a855f7', '#ec4899'
  ]

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold">
            {event?.id ? 'تعديل الحدث' : 'إضافة حدث جديد'}
          </DialogTitle>
        </DialogHeader>

        <div className="grid gap-6 py-4">
          {/* Title */}
          <div className="grid gap-2">
            <Label htmlFor="title">العنوان</Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="أدخل عنوان الحدث"
              className="text-lg"
            />
          </div>

          {/* Tabs for Content and Settings */}
          <Tabs defaultValue="content" className="w-full">
            <TabsList className="grid grid-cols-2 mb-4">
              <TabsTrigger value="content">المحتوى</TabsTrigger>
              <TabsTrigger value="settings">الإعدادات</TabsTrigger>
            </TabsList>

            {/* Content Tab */}
            <TabsContent value="content" className="space-y-4">
              {/* Editor */}
              <div className="border border-border/50 rounded-lg overflow-hidden">
                <EditorJSContent
                  content={content}
                  onContentChange={handleEditorChange}
                  isRTL={true}
                />
              </div>

              {/* AI Assistant Buttons */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowAIPanel(!showAIPanel)}
                    className="text-sm"
                  >
                    <Sparkles className="w-4 h-4 ml-2 text-amber-500" />
                    مساعدة الذكاء الاصطناعي
                  </Button>
                </div>
              </div>

              {/* AI Panel */}
              <AnimatePresence>
                {showAIPanel && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.2 }}
                    className="bg-muted/30 border border-border/50 rounded-lg p-4 overflow-hidden"
                  >
                    <div className="flex flex-col gap-3">
                      <h3 className="text-sm font-medium">مساعدة الذكاء الاصطناعي</h3>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                        <Button variant="outline" className="justify-start">
                          <Wand2 className="w-4 h-4 ml-2 text-purple-500" />
                          تحسين النص
                        </Button>
                        
                        <Button variant="outline" className="justify-start">
                          <MessageSquare className="w-4 h-4 ml-2 text-blue-500" />
                          تلخيص تلقائي
                        </Button>
                        
                        <Button variant="outline" className="justify-start">
                          <Sparkles className="w-4 h-4 ml-2 text-amber-500" />
                          إضافة محتوى ذكي
                        </Button>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </TabsContent>

            {/* Settings Tab */}
            <TabsContent value="settings" className="space-y-4">
              {/* Date and Time */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="start-date" className="flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-muted-foreground" />
                    تاريخ البداية
                  </Label>
                  <Input
                    id="start-date"
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="start-time" className="flex items-center gap-2">
                    <Clock className="w-4 h-4 text-muted-foreground" />
                    وقت البداية
                  </Label>
                  <Input
                    id="start-time"
                    type="time"
                    value={startTime}
                    onChange={(e) => setStartTime(e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="end-date" className="flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-muted-foreground" />
                    تاريخ النهاية
                  </Label>
                  <Input
                    id="end-date"
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="end-time" className="flex items-center gap-2">
                    <Clock className="w-4 h-4 text-muted-foreground" />
                    وقت النهاية
                  </Label>
                  <Input
                    id="end-time"
                    type="time"
                    value={endTime}
                    onChange={(e) => setEndTime(e.target.value)}
                  />
                </div>
              </div>

              {/* Category and Priority */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="category" className="flex items-center gap-2">
                    <Tag className="w-4 h-4 text-muted-foreground" />
                    الفئة
                  </Label>
                  <Select value={category} onValueChange={setCategory}>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر الفئة" />
                    </SelectTrigger>
                    <SelectContent>
                      {categoryOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          <div className="flex items-center gap-2">
                            <div
                              className="w-3 h-3 rounded-full"
                              style={{ backgroundColor: option.color }}
                            />
                            {option.value}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="priority" className="flex items-center gap-2">
                    <Flag className="w-4 h-4 text-muted-foreground" />
                    الأولوية
                  </Label>
                  <Select 
                    value={priority} 
                    onValueChange={(value) => setPriority(value as 'high' | 'medium' | 'low')}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر الأولوية" />
                    </SelectTrigger>
                    <SelectContent>
                      {priorityOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          <div className="flex items-center gap-2">
                            <div
                              className="w-3 h-3 rounded-full"
                              style={{ backgroundColor: option.color }}
                            />
                            {option.label}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Color */}
              <div className="space-y-2">
                <Label className="block mb-2">لون الحدث</Label>
                <div className="flex flex-wrap gap-2">
                  {colorOptions.map((colorOption) => (
                    <button
                      key={colorOption}
                      type="button"
                      onClick={() => setColor(colorOption)}
                      className={`w-8 h-8 rounded-full transition-all ${
                        color === colorOption
                          ? 'ring-2 ring-offset-2 ring-primary'
                          : 'hover:scale-110'
                      }`}
                      style={{ backgroundColor: colorOption }}
                    />
                  ))}
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <DialogFooter className="flex justify-between">
          <Button
            variant="destructive"
            onClick={handleDelete}
            className="flex items-center gap-2"
          >
            <Trash2 className="w-4 h-4" />
            حذف
          </Button>
          
          <div className="flex gap-2">
            <Button variant="outline" onClick={onClose}>
              إلغاء
            </Button>
            <Button onClick={handleSave}>
              حفظ
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
