import { useEffect, useState } from "react"
import { useNavigate } from "react-router-dom"
import { motion } from "framer-motion"
import { Loader2, CheckCircle, AlertCircle } from "lucide-react"
import { supabase, profileHelpers } from "@/lib/supabase"
import { useAuthStore } from "@/stores/authStore"
import { AuthLayout } from "./shared/AuthLayout"
import { toast } from "sonner"

export function OAuthCallback() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error' | 'profile-incomplete'>('loading')
  const [message, setMessage] = useState('')
  const navigate = useNavigate()
  const { updateSignUpData, setCurrentStep } = useAuthStore()

  useEffect(() => {
    const handleOAuthCallback = async () => {
      try {
        // Get the session from the URL hash
        const { data, error } = await supabase.auth.getSession()
        
        if (error) {
          throw error
        }

        if (!data.session?.user) {
          throw new Error('No user session found')
        }

        const user = data.session.user
        
        // Check if profile exists
        let profile = await profileHelpers.getProfile(user.id)
        
        if (!profile) {
          // Create basic profile from OAuth data
          profile = await profileHelpers.createProfile({
            id: user.id,
            full_name: user.user_metadata?.full_name || user.user_metadata?.name || 'مستخدم جديد',
            email: user.email || '',
            avatar_url: user.user_metadata?.avatar_url,
            language_preference: 'ar',
            theme_preference: 'system',
            country_code: 'SA',
            smart_features: {
              autoSummarization: false,
              speechToText: false,
              ideaExpansion: false,
              enableAllFeatures: false,
            },
            plan_choice: 'free',
            selected_folders: [],
            custom_folders: [],
            subscription_status: 'inactive',
            email_verified: true,
            profile_completed: false, // Mark as incomplete for OAuth users
            notifications_enabled: true,
            failed_login_attempts: 0,
            notes_count: 0,
            folders_count: 0,
            metadata: {
              oauth_provider: 'google',
              oauth_data: user.user_metadata
            }
          })
        }

        if (!profile) {
          throw new Error('Failed to create profile')
        }

        // Update last login
        await profileHelpers.updateLastLogin(user.id)

        // Check if profile is complete
        if (!profile.profile_completed) {
          setStatus('profile-incomplete')
          setMessage('يرجى إكمال إعداد حسابك')
          
          // Pre-fill signup data with OAuth info
          updateSignUpData({
            fullName: profile.full_name,
            email: profile.email,
            password: '', // OAuth users don't need password
            confirmPassword: '',
          })
          
          // Redirect to step 2 (Usage Intent) after a short delay
          setTimeout(() => {
            setCurrentStep(2)
            navigate('/auth/signup')
          }, 2000)
          
          return
        }

        // Profile is complete, redirect to main app
        setStatus('success')
        setMessage('تم تسجيل الدخول بنجاح!')
        
        toast.success("🎉 تم تسجيل الدخول بنجاح!", {
          description: "مرحباً بك في Nots"
        })
        
        setTimeout(() => {
          navigate('/')
        }, 1500)

      } catch (error: any) {
        console.error('OAuth callback error:', error)
        setStatus('error')
        setMessage('حدث خطأ أثناء تسجيل الدخول')
        
        toast.error("فشل في تسجيل الدخول", {
          description: "يرجى المحاولة مرة أخرى"
        })
        
        setTimeout(() => {
          navigate('/auth/login')
        }, 3000)
      }
    }

    handleOAuthCallback()
  }, [navigate, updateSignUpData, setCurrentStep])

  const getStatusIcon = () => {
    switch (status) {
      case 'loading':
        return <Loader2 className="w-12 h-12 text-primary animate-spin" />
      case 'success':
        return <CheckCircle className="w-12 h-12 text-green-500" />
      case 'profile-incomplete':
        return <CheckCircle className="w-12 h-12 text-blue-500" />
      case 'error':
        return <AlertCircle className="w-12 h-12 text-red-500" />
      default:
        return <Loader2 className="w-12 h-12 text-primary animate-spin" />
    }
  }

  const getStatusColor = () => {
    switch (status) {
      case 'loading':
        return 'text-primary'
      case 'success':
        return 'text-green-600'
      case 'profile-incomplete':
        return 'text-blue-600'
      case 'error':
        return 'text-red-600'
      default:
        return 'text-primary'
    }
  }

  return (
    <AuthLayout
      title="جاري تسجيل الدخول..."
      description="يرجى الانتظار بينما نقوم بإعداد حسابك"
      showLogo={true}
    >
      <div className="flex flex-col items-center justify-center py-12 space-y-6">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.5 }}
          className="flex items-center justify-center"
        >
          {getStatusIcon()}
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="text-center space-y-2"
        >
          <h3 className={`text-xl font-semibold ${getStatusColor()}`}>
            {status === 'loading' && 'جاري المعالجة...'}
            {status === 'success' && 'تم بنجاح!'}
            {status === 'profile-incomplete' && 'إعداد الحساب'}
            {status === 'error' && 'حدث خطأ'}
          </h3>
          
          <p className="text-muted-foreground">
            {message}
          </p>
        </motion.div>

        {status === 'loading' && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="w-full max-w-xs"
          >
            <div className="w-full bg-muted rounded-full h-2 overflow-hidden">
              <motion.div
                className="h-full bg-primary"
                initial={{ width: '0%' }}
                animate={{ width: '100%' }}
                transition={{ duration: 2, ease: "easeInOut" }}
              />
            </div>
          </motion.div>
        )}

        {status === 'profile-incomplete' && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
            className="text-center text-sm text-muted-foreground"
          >
            سيتم توجيهك لإكمال إعداد حسابك...
          </motion.div>
        )}
      </div>
    </AuthLayout>
  )
}
