
import { motion } from "framer-motion"
import { Card, CardContent, CardDes<PERSON>, CardHeader, CardTitle } from "@/components/ui/card"
import { Palette, Zap, Globe, Shield, Code, Smartphone } from "lucide-react"

const features = [
  {
    icon: <Globe className="h-8 w-8" />,
    title: "دعم اللغة العربية",
    description: "دعم كامل للنصوص العربية مع اتجاه RTL والخط العربي الأنيق"
  },
  {
    icon: <Palette className="h-8 w-8" />,
    title: "تصميم عصري",
    description: "واجهة مستخدم حديثة مع ألوان عربية مستوحاة من التراث"
  },
  {
    icon: <Zap className="h-8 w-8" />,
    title: "أداء سريع",
    description: "تحميل فوري وتجربة مستخدم سلسة مع أحدث التقنيات"
  },
  {
    icon: <Shield className="h-8 w-8" />,
    title: "آمان عالي",
    description: "حماية متقدمة للبيانات مع أفضل معايير الأمان"
  },
  {
    icon: <Code className="h-8 w-8" />,
    title: "تقنيات حديثة",
    description: "مبني بـ React و TypeScript و Tailwind CSS مع Framer Motion"
  },
  {
    icon: <Smartphone className="h-8 w-8" />,
    title: "متجاوب",
    description: "يعمل بسلاسة على جميع الأجهزة من الهاتف إلى سطح المكتب"
  }
]

export function Features() {
  return (
    <section id="services" className="py-20 bg-muted/30">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold arabic-heading mb-4">
            مميزات التطبيق
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto arabic-body">
            اكتشف الإمكانيات المتقدمة التي يوفرها تطبيقنا العربي الحديث
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -5 }}
              className="h-full"
            >
              <Card className="h-full hover:shadow-lg transition-all duration-300 border-border/50 bg-card/50 backdrop-blur-sm">
                <CardHeader className="text-center">
                  <motion.div
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    transition={{ duration: 0.2 }}
                    className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-primary/10 text-primary mx-auto mb-4"
                  >
                    {feature.icon}
                  </motion.div>
                  <CardTitle className="text-xl arabic-heading">
                    {feature.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-center arabic-body text-base leading-relaxed">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
