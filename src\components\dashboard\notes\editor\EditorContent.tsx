import { useState } from 'react'
import { OutputData } from '@editorjs/editorjs'
import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { FloatingAIAssistant } from "./FloatingAIAssistant"
import { ImageUploadCard } from "./ImageUploadCard"
import { LinkInsertCard } from "./LinkInsertCard"
import { EditorJSContent } from "./EditorJSContent"
import { 
  Bold,
  Italic,
  Underline as UnderlineIcon,
  Strikethrough,
  Highlighter as HighlightIcon,
  AlignRight,
  AlignCenter,
  AlignLeft,
  List,
  ListOrdered,
  Quote,
  Code,
  Minus,
  Subscript as SubscriptIcon,
  Superscript as SuperscriptIcon,
  Image as ImageIcon,
  Link as LinkIcon,
  Palette,
  Heading1,
  Heading2,
  Heading3
} from "lucide-react"

interface EditorContentProps {
  content: string | OutputData
  onContentChange: (content: OutputData) => void
  isRTL?: boolean
  onAIAction?: (action: string) => void
}

export function EditorContent({ content, onContentChange, isRTL = true, onAIAction }: EditorContentProps) {
  const [isImageModalOpen, setIsImageModalOpen] = useState(false)
  const [isLinkModalOpen, setIsLinkModalOpen] = useState(false)
  const [selectedText, setSelectedText] = useState('')

  // Safety checks for props to prevent React error #130
  const safeContent = content || ''
  const safeOnContentChange = typeof onContentChange === 'function' ? onContentChange : () => {}
  const safeOnAIAction = typeof onAIAction === 'function' ? onAIAction : undefined

  const handleImageInsert = (src: string, alt?: string) => {
    // Use global method to insert image into Editor.js
    if ((window as any).editorJSInsertImage) {
      (window as any).editorJSInsertImage(src, alt)
    }
    setIsImageModalOpen(false)
  }

  const handleLinkInsert = (href: string, text?: string) => {
    // Use global method to insert link into Editor.js
    if ((window as any).editorJSInsertLink) {
      (window as any).editorJSInsertLink(href, text)
    }
    setIsLinkModalOpen(false)
  }

  const handleOpenLinkModal = () => {
    // Get selected text
    const selection = editor?.state.selection
    if (selection && !selection.empty) {
      const selectedContent = editor?.state.doc.textBetween(selection.from, selection.to)
      setSelectedText(selectedContent || '')
    } else {
      setSelectedText('')
    }
    setIsLinkModalOpen(true)
  }
  // Calculate word and character count manually to avoid React rendering issues
  const getWordCount = (content: OutputData): number => {
    if (!content.blocks) return 0
    const text = content.blocks
      .map(block => {
        if (block.type === 'paragraph' || block.type === 'header') {
          return block.data.text?.replace(/<[^>]*>/g, '') || ''
        }
        return ''
      })
      .join(' ')
      .trim()
    return text ? text.split(/\s+/).length : 0
  }

  const getCharCount = (content: OutputData): number => {
    if (!content.blocks) return 0
    const text = content.blocks
      .map(block => {
        if (block.type === 'paragraph' || block.type === 'header') {
          return block.data.text?.replace(/<[^>]*>/g, '') || ''
        }
        return ''
      })
      .join('')
      .trim()
    return text.length
  }

  // Handle content change from Editor.js
  const handleEditorChange = (outputData: OutputData) => {
    safeOnContentChange(outputData)
  }
  // Convert content for Editor.js if needed
  const editorContent = typeof safeContent === 'string' ? safeContent : safeContent

  // Get current content for word/char count
  const currentContent = typeof editorContent === 'string'
    ? { blocks: [], time: Date.now(), version: "2.28.2" } as OutputData
    : editorContent

  const ToolbarButton = ({ 
    onClick, 
    isActive, 
    children, 
    title 
  }: { 
    onClick: () => void
    isActive?: boolean
    children: React.ReactNode
    title: string
  }) => (
    <Button
      onClick={onClick}
      variant={isActive ? "default" : "ghost"}
      size="sm"
      className={`w-8 h-8 p-0 rounded-lg transition-all duration-200 ${
        isActive 
          ? 'bg-primary text-white shadow-sm' 
          : 'hover:bg-muted/50 text-muted-foreground hover:text-foreground'
      }`}
      title={title}
    >
      {children}
    </Button>
  )

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.2 }}
      className="relative h-full"
    >
      {/* Editor.js Content */}
      <EditorJSContent
        content={editorContent}
        onContentChange={handleEditorChange}
        isRTL={isRTL}
        onImageInsert={() => setIsImageModalOpen(true)}
        onLinkInsert={() => setIsLinkModalOpen(true)}
      />

      {/* Stats Bar */}
      <div className="mt-4 flex items-center justify-between text-xs text-muted-foreground bg-muted/30 rounded-lg px-3 py-2">
        <div className="flex items-center space-x-4 space-x-reverse">
          <span>الكلمات: {getWordCount(currentContent)}</span>
          <span>الأحرف: {getCharCount(currentContent)}</span>
        </div>
        <div className="flex items-center space-x-2 space-x-reverse">
          <span>آخر تحديث: {new Date().toLocaleTimeString('ar-SA')}</span>
        </div>
      </div>

      {/* Image Upload Modal */}
      {isImageModalOpen && (
        <ImageUploadCard
          onImageInsert={handleImageInsert}
          onClose={() => setIsImageModalOpen(false)}
        />
      )}

      {/* Link Insert Modal */}
      {isLinkModalOpen && (
        <LinkInsertCard
          selectedText={selectedText}
          onLinkInsert={handleLinkInsert}
          onClose={() => setIsLinkModalOpen(false)}
        />
      )}

      {/* Floating AI Assistant */}
      {safeOnAIAction && (
        <FloatingAIAssistant
          onAIAction={safeOnAIAction}
          selectedText=""
        />
      )}
    </motion.div>
  )
}
          <div className="flex items-center space-x-1 space-x-reverse">
            <ToolbarButton
              onClick={() => {
                try {
                  editor?.chain().focus().toggleBold().run()
                } catch (error) {
                  console.error('Error toggling bold:', error)
                }
              }}
              isActive={editor?.isActive('bold') || false}
              title="غامق"
            >
              <Bold className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => {
                try {
                  editor?.chain().focus().toggleItalic().run()
                } catch (error) {
                  console.error('Error toggling italic:', error)
                }
              }}
              isActive={editor?.isActive('italic') || false}
              title="مائل"
            >
              <Italic className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => {
                try {
                  editor?.chain().focus().toggleUnderline().run()
                } catch (error) {
                  console.error('Error toggling underline:', error)
                }
              }}
              isActive={editor?.isActive('underline') || false}
              title="تحته خط"
            >
              <UnderlineIcon className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor?.chain().focus().toggleHighlight().run()}
              isActive={editor?.isActive('highlight')}
              title="تمييز"
            >
              <HighlightIcon className="w-4 h-4" />
            </ToolbarButton>
          </div>

          {/* Headings */}
          <div className="flex items-center space-x-1 space-x-reverse border-r border-border/30 pr-2">
            <ToolbarButton
              onClick={() => editor?.chain().focus().toggleHeading({ level: 1 }).run()}
              isActive={editor?.isActive('heading', { level: 1 })}
              title="عنوان كبير"
            >
              <Heading1 className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor?.chain().focus().toggleHeading({ level: 2 }).run()}
              isActive={editor?.isActive('heading', { level: 2 })}
              title="عنوان متوسط"
            >
              <Heading2 className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor?.chain().focus().toggleHeading({ level: 3 }).run()}
              isActive={editor?.isActive('heading', { level: 3 })}
              title="عنوان صغير"
            >
              <Heading3 className="w-4 h-4" />
            </ToolbarButton>
          </div>

          {/* Lists */}
          <div className="flex items-center space-x-1 space-x-reverse border-r border-border/30 pr-2">
            <ToolbarButton
              onClick={() => editor?.chain().focus().toggleBulletList().run()}
              isActive={editor?.isActive('bulletList')}
              title="قائمة نقطية"
            >
              <List className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor?.chain().focus().toggleOrderedList().run()}
              isActive={editor?.isActive('orderedList')}
              title="قائمة مرقمة"
            >
              <ListOrdered className="w-4 h-4" />
            </ToolbarButton>


          </div>

          {/* Alignment */}
          <div className="flex items-center space-x-1 space-x-reverse border-r border-border/30 pr-2">
            <ToolbarButton
              onClick={() => editor?.chain().focus().setTextAlign('right').run()}
              isActive={editor?.isActive({ textAlign: 'right' })}
              title="محاذاة يمين"
            >
              <AlignRight className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor?.chain().focus().setTextAlign('center').run()}
              isActive={editor?.isActive({ textAlign: 'center' })}
              title="محاذاة وسط"
            >
              <AlignCenter className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor?.chain().focus().setTextAlign('left').run()}
              isActive={editor?.isActive({ textAlign: 'left' })}
              title="محاذاة يسار"
            >
              <AlignLeft className="w-4 h-4" />
            </ToolbarButton>
          </div>

          {/* Advanced Formatting */}
          <div className="flex items-center space-x-1 space-x-reverse">
            <ToolbarButton
              onClick={() => editor?.chain().focus().toggleBlockquote().run()}
              isActive={editor?.isActive('blockquote')}
              title="اقتباس"
            >
              <Quote className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor?.chain().focus().toggleCodeBlock().run()}
              isActive={editor?.isActive('codeBlock')}
              title="كود"
            >
              <Code className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor?.chain().focus().setHorizontalRule().run()}
              title="خط فاصل"
            >
              <Minus className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor?.chain().focus().toggleSubscript().run()}
              isActive={editor?.isActive('subscript')}
              title="نص سفلي"
            >
              <SubscriptIcon className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor?.chain().focus().toggleSuperscript().run()}
              isActive={editor?.isActive('superscript')}
              title="نص علوي"
            >
              <SuperscriptIcon className="w-4 h-4" />
            </ToolbarButton>
          </div>

          {/* Media and Links */}
          <div className="flex items-center space-x-1 space-x-reverse">
            <ToolbarButton
              onClick={() => setIsImageModalOpen(true)}
              title="إدراج صورة"
            >
              <ImageIcon className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={handleOpenLinkModal}
              isActive={editor?.isActive('link')}
              title="إدراج رابط"
            >
              <LinkIcon className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => {
                // Color picker functionality (placeholder for now)
                const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']
                const color = colors[Math.floor(Math.random() * colors.length)]
                editor?.chain().focus().toggleHighlight({ color }).run()
              }}
              title="ألوان التمييز"
            >
              <Palette className="w-4 h-4" />
            </ToolbarButton>
          </div>
        </div>
      </div>

      {/* Main Editor */}
      <div className="relative bg-background rounded-xl border border-border/50 shadow-sm overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-muted/10 via-accent/5 to-transparent pointer-events-none" />

        {/* Animated Background Shapes */}
        <motion.div
          animate={{
            scale: [1, 1.1, 1],
            opacity: [0.05, 0.1, 0.05]
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
          }}
          className="absolute top-10 right-10 w-32 h-32 rounded-full bg-primary/10 blur-xl pointer-events-none"
        />

        <div className="relative z-10 overflow-hidden">
          <TipTapEditorContent
            editor={editor}
            className="min-h-[500px] max-h-[calc(100vh-200px)] overflow-y-auto scroll-smooth focus-within:ring-2 focus-within:ring-primary/20 focus-within:border-primary/30 transition-all duration-200 prose-headings:scroll-mt-20"
          />
        </div>

        {/* Floating AI Assistant */}
        {safeOnAIAction && (
          <FloatingAIAssistant onAIAction={safeOnAIAction} />
        )}

        {/* Image Upload Modal */}
        <ImageUploadCard
          isOpen={isImageModalOpen}
          onClose={() => setIsImageModalOpen(false)}
          onImageInsert={handleImageInsert}
        />

        {/* Link Insert Modal */}
        <LinkInsertCard
          isOpen={isLinkModalOpen}
          onClose={() => setIsLinkModalOpen(false)}
          onLinkInsert={handleLinkInsert}
          selectedText={selectedText}
        />
      </div>

      {/* Editor Stats */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.5 }}
        className="flex items-center justify-between mt-4 text-xs text-muted-foreground"
      >
        <div className="flex items-center space-x-4 space-x-reverse">
          <span>الكلمات: {getWordCount(safeContent)}</span>
          <span>الأحرف: {getCharCount(safeContent)}</span>
        </div>
        <div className="flex items-center space-x-2 space-x-reverse">
          <span>اضغط</span>
          <kbd className="px-2 py-1 bg-muted rounded text-xs">Ctrl + S</kbd>
          <span>للحفظ</span>
        </div>
      </motion.div>
    </motion.div>
  )
}
