import { useState } from 'react'
import { OutputData } from '@editorjs/editorjs'
import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { FloatingAIAssistant } from "./FloatingAIAssistant"
import { ImageUploadCard } from "./ImageUploadCard"
import { LinkInsertCard } from "./LinkInsertCard"
import { EditorJSContent } from "./EditorJSContent"


interface EditorContentProps {
  content: string | OutputData
  onContentChange: (content: OutputData) => void
  isRTL?: boolean
  onAIAction?: (action: string) => void
}

export function EditorContent({ content, onContentChange, isRTL = true, onAIAction }: EditorContentProps) {
  const [isImageModalOpen, setIsImageModalOpen] = useState(false)
  const [isLinkModalOpen, setIsLinkModalOpen] = useState(false)
  const [selectedText, setSelectedText] = useState('')

  // Safety checks for props to prevent React error #130
  const safeContent = content || ''
  const safeOnContentChange = typeof onContentChange === 'function' ? onContentChange : () => {}
  const safeOnAIAction = typeof onAIAction === 'function' ? onAIAction : undefined

  const handleImageInsert = (src: string, alt?: string) => {
    // Use global method to insert image into Editor.js
    if ((window as any).editorJSInsertImage) {
      (window as any).editorJSInsertImage(src, alt)
    }
    setIsImageModalOpen(false)
  }

  const handleLinkInsert = (href: string, text?: string) => {
    // Use global method to insert link into Editor.js
    if ((window as any).editorJSInsertLink) {
      (window as any).editorJSInsertLink(href, text)
    }
    setIsLinkModalOpen(false)
  }

  // Calculate word and character count manually to avoid React rendering issues
  const getWordCount = (content: OutputData): number => {
    if (!content.blocks) return 0
    const text = content.blocks
      .map(block => {
        if (block.type === 'paragraph' || block.type === 'header') {
          return block.data.text?.replace(/<[^>]*>/g, '') || ''
        }
        return ''
      })
      .join(' ')
      .trim()
    return text ? text.split(/\s+/).length : 0
  }

  const getCharCount = (content: OutputData): number => {
    if (!content.blocks) return 0
    const text = content.blocks
      .map(block => {
        if (block.type === 'paragraph' || block.type === 'header') {
          return block.data.text?.replace(/<[^>]*>/g, '') || ''
        }
        return ''
      })
      .join('')
      .trim()
    return text.length
  }

  // Handle content change from Editor.js
  const handleEditorChange = (outputData: OutputData) => {
    safeOnContentChange(outputData)
  }
  // Convert content for Editor.js if needed
  const editorContent = typeof safeContent === 'string' ? safeContent : safeContent

  // Get current content for word/char count
  const currentContent = typeof editorContent === 'string'
    ? { blocks: [], time: Date.now(), version: "2.28.2" } as OutputData
    : editorContent



  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.2 }}
      className="relative h-full"
    >
      {/* Editor.js Content */}
      <EditorJSContent
        content={editorContent}
        onContentChange={handleEditorChange}
        isRTL={isRTL}
        onImageInsert={() => setIsImageModalOpen(true)}
        onLinkInsert={() => setIsLinkModalOpen(true)}
      />

      {/* Stats Bar */}
      <div className="mt-4 flex items-center justify-between text-xs text-muted-foreground bg-muted/30 rounded-lg px-3 py-2">
        <div className="flex items-center space-x-4 space-x-reverse">
          <span>الكلمات: {getWordCount(currentContent)}</span>
          <span>الأحرف: {getCharCount(currentContent)}</span>
        </div>
        <div className="flex items-center space-x-2 space-x-reverse">
          <span>آخر تحديث: {new Date().toLocaleTimeString('ar-SA')}</span>
        </div>
      </div>

      {/* Image Upload Modal */}
      {isImageModalOpen && (
        <ImageUploadCard
          onImageInsert={handleImageInsert}
          onClose={() => setIsImageModalOpen(false)}
        />
      )}

      {/* Link Insert Modal */}
      {isLinkModalOpen && (
        <LinkInsertCard
          selectedText={selectedText}
          onLinkInsert={handleLinkInsert}
          onClose={() => setIsLinkModalOpen(false)}
        />
      )}

      {/* Floating AI Assistant */}
      {safeOnAIAction && (
        <FloatingAIAssistant
          onAIAction={safeOnAIAction}
          selectedText=""
        />
      )}
    </motion.div>
  )
}
