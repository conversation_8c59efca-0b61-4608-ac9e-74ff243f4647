import { useState } from 'react'
import { useE<PERSON><PERSON>, EditorContent as TipTapEditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Placeholder from '@tiptap/extension-placeholder'
import TextAlign from '@tiptap/extension-text-align'
import Highlight from '@tiptap/extension-highlight'
import Underline from '@tiptap/extension-underline'
import CodeBlock from '@tiptap/extension-code-block'
import Blockquote from '@tiptap/extension-blockquote'
import HorizontalRule from '@tiptap/extension-horizontal-rule'
import Subscript from '@tiptap/extension-subscript'
import Superscript from '@tiptap/extension-superscript'
import Image from '@tiptap/extension-image'
import Link from '@tiptap/extension-link'
import { motion } from "framer-motion"
import { Button } from "@/components/ui/button"
import { FloatingAIAssistant } from "./FloatingAIAssistant"
import { ImageUploadCard } from "./ImageUploadCard"
import { LinkInsertCard } from "./LinkInsertCard"
import { 
  Bold,
  Italic,
  Underline as UnderlineIcon,
  Strikethrough,
  Highlighter as HighlightIcon,
  AlignRight,
  AlignCenter,
  AlignLeft,
  List,
  ListOrdered,
  Quote,
  Code,
  Minus,
  Subscript as SubscriptIcon,
  Superscript as SuperscriptIcon,
  Image as ImageIcon,
  Link as LinkIcon,
  Palette,
  Heading1,
  Heading2,
  Heading3
} from "lucide-react"

interface EditorContentProps {
  content: string
  onContentChange: (content: string) => void
  isRTL?: boolean
  onAIAction?: (action: string) => void
}

export function EditorContent({ content, onContentChange, isRTL = true, onAIAction }: EditorContentProps) {
  const [isImageModalOpen, setIsImageModalOpen] = useState(false)
  const [isLinkModalOpen, setIsLinkModalOpen] = useState(false)
  const [selectedText, setSelectedText] = useState('')

  // Safety checks for props to prevent React error #130
  const safeContent = typeof content === 'string' ? content : ''
  const safeOnContentChange = typeof onContentChange === 'function' ? onContentChange : () => {}
  const safeOnAIAction = typeof onAIAction === 'function' ? onAIAction : undefined

  const handleImageInsert = (src: string, alt?: string) => {
    editor?.chain().focus().setImage({ src, alt }).run()
  }

  const handleLinkInsert = (href: string, text?: string) => {
    if (text && text !== href) {
      // Insert text with link
      editor?.chain().focus().insertContent(`<a href="${href}">${text}</a>`).run()
    } else {
      // Just set link on selected text or insert link
      editor?.chain().focus().setLink({ href }).run()
    }
  }

  const handleOpenLinkModal = () => {
    // Get selected text
    const selection = editor?.state.selection
    if (selection && !selection.empty) {
      const selectedContent = editor?.state.doc.textBetween(selection.from, selection.to)
      setSelectedText(selectedContent || '')
    } else {
      setSelectedText('')
    }
    setIsLinkModalOpen(true)
  }
  // Calculate word and character count manually to avoid React rendering issues
  const getWordCount = (html: string): number => {
    const text = html.replace(/<[^>]*>/g, '').trim()
    return text ? text.split(/\s+/).length : 0
  }

  const getCharCount = (html: string): number => {
    const text = html.replace(/<[^>]*>/g, '').trim()
    return text.length
  }
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3],
        },
        blockquote: false, // We'll use the separate extension
        codeBlock: false, // We'll use the separate extension
        horizontalRule: false, // We'll use the separate extension
      }),
      Placeholder.configure({
        placeholder: 'اكتب ملاحظتك هنا... يمكنك استخدام التنسيق الغني لتنظيم أفكارك بشكل جميل.',
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
        alignments: ['left', 'center', 'right'],
        defaultAlignment: isRTL ? 'right' : 'left',
      }),
      Highlight.configure({
        multicolor: true,
      }),
      Underline,
      CodeBlock.configure({
        HTMLAttributes: {
          class: 'bg-muted/50 rounded-lg p-4 font-mono text-sm border border-border/30',
        },
      }),
      Blockquote.configure({
        HTMLAttributes: {
          class: 'border-r-4 border-primary/50 pr-4 italic text-muted-foreground',
        },
      }),
      HorizontalRule.configure({
        HTMLAttributes: {
          class: 'my-6 border-border/50',
        },
      }),
      Subscript,
      Superscript,
      Image.configure({
        HTMLAttributes: {
          class: 'max-w-full h-auto rounded-lg shadow-sm border border-border/30',
        },
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-primary hover:text-primary/80 underline cursor-pointer',
        },
      }),
    ],
    content: safeContent,
    onUpdate: ({ editor }) => {
      try {
        const html = editor.getHTML()
        safeOnContentChange(html)
      } catch (error) {
        console.error('Error updating content:', error)
      }
    },
    onPaste: () => {
      // Handle paste events to prevent toolbar issues
      try {
        console.log('Paste event detected, maintaining toolbar state')
        // Let TipTap handle the paste normally, but ensure toolbar remains stable
        return false // Allow default paste behavior
      } catch (error) {
        console.error('Error handling paste:', error)
        return false
      }
    },
    editorProps: {
      attributes: {
        class: 'prose prose-lg dark:prose-invert max-w-none focus:outline-none min-h-[500px] p-6 text-foreground',
        dir: isRTL ? 'rtl' : 'ltr',
        style: 'font-family: "Tajawal", sans-serif;',
      },
      handlePaste: () => {
        // Additional paste handling to ensure toolbar stability
        try {
          console.log('Processing paste content, toolbar should remain stable')
          return false // Let TipTap handle normally
        } catch (error) {
          console.error('Error in paste handler:', error)
          return false
        }
      },
    },
    immediatelyRender: false,
    // Add error recovery
    onDestroy: () => {
      console.log('Editor destroyed, cleaning up')
    },
    onCreate: () => {
      console.log('Editor created successfully')
    },
  })

  if (!editor) {
    return (
      <div className="flex items-center justify-center min-h-[500px]">
        <div className="text-center space-y-4">
          <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto" />
          <p className="text-muted-foreground">جاري تحميل المحرر...</p>
        </div>
      </div>
    )
  }

  // Additional safety check to prevent rendering if editor is in invalid state
  try {
    if (!editor.isDestroyed && !editor.view) {
      return (
        <div className="flex items-center justify-center min-h-[500px]">
          <div className="text-center space-y-4">
            <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto" />
            <p className="text-muted-foreground">جاري إعداد المحرر...</p>
          </div>
        </div>
      )
    }
  } catch (error) {
    console.error('Editor state check error:', error)
    return (
      <div className="flex items-center justify-center min-h-[500px]">
        <div className="text-center space-y-4">
          <p className="text-red-500">حدث خطأ في تحميل المحرر</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-primary text-white rounded-lg"
          >
            إعادة تحميل
          </button>
        </div>
      </div>
    )
  }

  const ToolbarButton = ({ 
    onClick, 
    isActive, 
    children, 
    title 
  }: { 
    onClick: () => void
    isActive?: boolean
    children: React.ReactNode
    title: string
  }) => (
    <Button
      onClick={onClick}
      variant={isActive ? "default" : "ghost"}
      size="sm"
      className={`w-8 h-8 p-0 rounded-lg transition-all duration-200 ${
        isActive 
          ? 'bg-primary text-white shadow-sm' 
          : 'hover:bg-muted/50 text-muted-foreground hover:text-foreground'
      }`}
      title={title}
    >
      {children}
    </Button>
  )

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.2 }}
      className="relative h-full"
    >
      {/* Formatting Toolbar */}
      <div className="mb-4 p-3 bg-muted/30 rounded-xl border border-border/30">
        <div className="flex items-center space-x-2 space-x-reverse flex-wrap gap-2">
          {/* Text Formatting */}
          <div className="flex items-center space-x-1 space-x-reverse">
            <ToolbarButton
              onClick={() => {
                try {
                  editor?.chain().focus().toggleBold().run()
                } catch (error) {
                  console.error('Error toggling bold:', error)
                }
              }}
              isActive={editor?.isActive('bold') || false}
              title="غامق"
            >
              <Bold className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => {
                try {
                  editor?.chain().focus().toggleItalic().run()
                } catch (error) {
                  console.error('Error toggling italic:', error)
                }
              }}
              isActive={editor?.isActive('italic') || false}
              title="مائل"
            >
              <Italic className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => {
                try {
                  editor?.chain().focus().toggleUnderline().run()
                } catch (error) {
                  console.error('Error toggling underline:', error)
                }
              }}
              isActive={editor?.isActive('underline') || false}
              title="تحته خط"
            >
              <UnderlineIcon className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor?.chain().focus().toggleHighlight().run()}
              isActive={editor?.isActive('highlight')}
              title="تمييز"
            >
              <HighlightIcon className="w-4 h-4" />
            </ToolbarButton>
          </div>

          {/* Headings */}
          <div className="flex items-center space-x-1 space-x-reverse border-r border-border/30 pr-2">
            <ToolbarButton
              onClick={() => editor?.chain().focus().toggleHeading({ level: 1 }).run()}
              isActive={editor?.isActive('heading', { level: 1 })}
              title="عنوان كبير"
            >
              <Heading1 className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor?.chain().focus().toggleHeading({ level: 2 }).run()}
              isActive={editor?.isActive('heading', { level: 2 })}
              title="عنوان متوسط"
            >
              <Heading2 className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor?.chain().focus().toggleHeading({ level: 3 }).run()}
              isActive={editor?.isActive('heading', { level: 3 })}
              title="عنوان صغير"
            >
              <Heading3 className="w-4 h-4" />
            </ToolbarButton>
          </div>

          {/* Lists */}
          <div className="flex items-center space-x-1 space-x-reverse border-r border-border/30 pr-2">
            <ToolbarButton
              onClick={() => editor?.chain().focus().toggleBulletList().run()}
              isActive={editor?.isActive('bulletList')}
              title="قائمة نقطية"
            >
              <List className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor?.chain().focus().toggleOrderedList().run()}
              isActive={editor?.isActive('orderedList')}
              title="قائمة مرقمة"
            >
              <ListOrdered className="w-4 h-4" />
            </ToolbarButton>


          </div>

          {/* Alignment */}
          <div className="flex items-center space-x-1 space-x-reverse border-r border-border/30 pr-2">
            <ToolbarButton
              onClick={() => editor?.chain().focus().setTextAlign('right').run()}
              isActive={editor?.isActive({ textAlign: 'right' })}
              title="محاذاة يمين"
            >
              <AlignRight className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor?.chain().focus().setTextAlign('center').run()}
              isActive={editor?.isActive({ textAlign: 'center' })}
              title="محاذاة وسط"
            >
              <AlignCenter className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor?.chain().focus().setTextAlign('left').run()}
              isActive={editor?.isActive({ textAlign: 'left' })}
              title="محاذاة يسار"
            >
              <AlignLeft className="w-4 h-4" />
            </ToolbarButton>
          </div>

          {/* Advanced Formatting */}
          <div className="flex items-center space-x-1 space-x-reverse">
            <ToolbarButton
              onClick={() => editor?.chain().focus().toggleBlockquote().run()}
              isActive={editor?.isActive('blockquote')}
              title="اقتباس"
            >
              <Quote className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor?.chain().focus().toggleCodeBlock().run()}
              isActive={editor?.isActive('codeBlock')}
              title="كود"
            >
              <Code className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor?.chain().focus().setHorizontalRule().run()}
              title="خط فاصل"
            >
              <Minus className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor?.chain().focus().toggleSubscript().run()}
              isActive={editor?.isActive('subscript')}
              title="نص سفلي"
            >
              <SubscriptIcon className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor?.chain().focus().toggleSuperscript().run()}
              isActive={editor?.isActive('superscript')}
              title="نص علوي"
            >
              <SuperscriptIcon className="w-4 h-4" />
            </ToolbarButton>
          </div>

          {/* Media and Links */}
          <div className="flex items-center space-x-1 space-x-reverse">
            <ToolbarButton
              onClick={() => setIsImageModalOpen(true)}
              title="إدراج صورة"
            >
              <ImageIcon className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={handleOpenLinkModal}
              isActive={editor?.isActive('link')}
              title="إدراج رابط"
            >
              <LinkIcon className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => {
                // Color picker functionality (placeholder for now)
                const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']
                const color = colors[Math.floor(Math.random() * colors.length)]
                editor?.chain().focus().toggleHighlight({ color }).run()
              }}
              title="ألوان التمييز"
            >
              <Palette className="w-4 h-4" />
            </ToolbarButton>
          </div>
        </div>
      </div>

      {/* Main Editor */}
      <div className="relative bg-background rounded-xl border border-border/50 shadow-sm overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-muted/10 via-accent/5 to-transparent pointer-events-none" />

        {/* Animated Background Shapes */}
        <motion.div
          animate={{
            scale: [1, 1.1, 1],
            opacity: [0.05, 0.1, 0.05]
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
          }}
          className="absolute top-10 right-10 w-32 h-32 rounded-full bg-primary/10 blur-xl pointer-events-none"
        />

        <div className="relative z-10">
          <TipTapEditorContent
            editor={editor}
            className="min-h-[500px] max-h-[calc(100vh-300px)] overflow-y-auto focus-within:ring-2 focus-within:ring-primary/20 focus-within:border-primary/30 transition-all duration-200"
          />
        </div>

        {/* Floating AI Assistant */}
        {safeOnAIAction && (
          <FloatingAIAssistant onAIAction={safeOnAIAction} />
        )}

        {/* Image Upload Modal */}
        <ImageUploadCard
          isOpen={isImageModalOpen}
          onClose={() => setIsImageModalOpen(false)}
          onImageInsert={handleImageInsert}
        />

        {/* Link Insert Modal */}
        <LinkInsertCard
          isOpen={isLinkModalOpen}
          onClose={() => setIsLinkModalOpen(false)}
          onLinkInsert={handleLinkInsert}
          selectedText={selectedText}
        />
      </div>

      {/* Editor Stats */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.5 }}
        className="flex items-center justify-between mt-4 text-xs text-muted-foreground"
      >
        <div className="flex items-center space-x-4 space-x-reverse">
          <span>الكلمات: {getWordCount(safeContent)}</span>
          <span>الأحرف: {getCharCount(safeContent)}</span>
        </div>
        <div className="flex items-center space-x-2 space-x-reverse">
          <span>اضغط</span>
          <kbd className="px-2 py-1 bg-muted rounded text-xs">Ctrl + S</kbd>
          <span>للحفظ</span>
        </div>
      </motion.div>
    </motion.div>
  )
}
