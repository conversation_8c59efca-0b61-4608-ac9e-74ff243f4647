import { use<PERSON><PERSON><PERSON>, EditorContent as TipTapEditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Placeholder from '@tiptap/extension-placeholder'
import TextAlign from '@tiptap/extension-text-align'
import Highlight from '@tiptap/extension-highlight'
import Underline from '@tiptap/extension-underline'
import Link from '@tiptap/extension-link'
import TaskList from '@tiptap/extension-task-list'
import TaskItem from '@tiptap/extension-task-item'
import Table from '@tiptap/extension-table'
import TableRow from '@tiptap/extension-table-row'
import TableHeader from '@tiptap/extension-table-header'
import TableCell from '@tiptap/extension-table-cell'
import { Color } from '@tiptap/extension-color'
import TextStyle from '@tiptap/extension-text-style'
import CharacterCount from '@tiptap/extension-character-count'
import { motion } from "framer-motion"
import { Button } from "@/components/ui/button"
import { 
  Bold, 
  Italic, 
  Underline as UnderlineIcon, 
  Strikethrough,
  Highlight as HighlightIcon,
  AlignRight,
  AlignCenter,
  AlignLeft,
  List,
  ListOrdered,
  Quote,
  Code,
  Link as LinkIcon,
  Table as TableIcon,
  CheckSquare,
  Heading1,
  Heading2,
  Heading3
} from "lucide-react"

interface EditorContentProps {
  content: string
  onContentChange: (content: string) => void
  isRTL?: boolean
}

export function EditorContent({ content, onContentChange, isRTL = true }: EditorContentProps) {
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3],
        },
      }),
      Placeholder.configure({
        placeholder: 'اكتب ملاحظتك هنا... يمكنك استخدام التنسيق الغني لتنظيم أفكارك بشكل جميل.',
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
        alignments: ['left', 'center', 'right'],
        defaultAlignment: isRTL ? 'right' : 'left',
      }),
      Highlight.configure({
        multicolor: true,
      }),
      Underline,
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-primary hover:text-primary/80 underline cursor-pointer',
        },
      }),
      TaskList,
      TaskItem.configure({
        nested: true,
      }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableHeader,
      TableCell,
      Color,
      TextStyle,
      CharacterCount,
    ],
    content: content || '',
    onUpdate: ({ editor }) => {
      const html = editor.getHTML()
      onContentChange(html)
    },
    editorProps: {
      attributes: {
        class: 'prose prose-lg max-w-none focus:outline-none min-h-[500px] p-6',
        dir: isRTL ? 'rtl' : 'ltr',
        style: 'font-family: "Tajawal", sans-serif;',
      },
    },
    immediatelyRender: false,
  })

  if (!editor) {
    return (
      <div className="flex items-center justify-center min-h-[500px]">
        <div className="text-center space-y-4">
          <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto" />
          <p className="text-muted-foreground">جاري تحميل المحرر...</p>
        </div>
      </div>
    )
  }

  const ToolbarButton = ({ 
    onClick, 
    isActive, 
    children, 
    title 
  }: { 
    onClick: () => void
    isActive?: boolean
    children: React.ReactNode
    title: string
  }) => (
    <Button
      onClick={onClick}
      variant={isActive ? "default" : "ghost"}
      size="sm"
      className={`w-8 h-8 p-0 rounded-lg transition-all duration-200 ${
        isActive 
          ? 'bg-primary text-white shadow-sm' 
          : 'hover:bg-muted/50 text-muted-foreground hover:text-foreground'
      }`}
      title={title}
    >
      {children}
    </Button>
  )

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.2 }}
      className="relative h-full"
    >
      {/* Formatting Toolbar */}
      <div className="mb-4 p-3 bg-muted/30 rounded-xl border border-border/30">
        <div className="flex items-center space-x-2 space-x-reverse flex-wrap gap-2">
          {/* Text Formatting */}
          <div className="flex items-center space-x-1 space-x-reverse">
            <ToolbarButton
              onClick={() => editor?.chain().focus().toggleBold().run()}
              isActive={editor?.isActive('bold')}
              title="غامق"
            >
              <Bold className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor?.chain().focus().toggleItalic().run()}
              isActive={editor?.isActive('italic')}
              title="مائل"
            >
              <Italic className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor?.chain().focus().toggleUnderline().run()}
              isActive={editor?.isActive('underline')}
              title="تحته خط"
            >
              <UnderlineIcon className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor?.chain().focus().toggleHighlight().run()}
              isActive={editor?.isActive('highlight')}
              title="تمييز"
            >
              <HighlightIcon className="w-4 h-4" />
            </ToolbarButton>
          </div>

          {/* Headings */}
          <div className="flex items-center space-x-1 space-x-reverse border-r border-border/30 pr-2">
            <ToolbarButton
              onClick={() => editor?.chain().focus().toggleHeading({ level: 1 }).run()}
              isActive={editor?.isActive('heading', { level: 1 })}
              title="عنوان كبير"
            >
              <Heading1 className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor?.chain().focus().toggleHeading({ level: 2 }).run()}
              isActive={editor?.isActive('heading', { level: 2 })}
              title="عنوان متوسط"
            >
              <Heading2 className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor?.chain().focus().toggleHeading({ level: 3 }).run()}
              isActive={editor?.isActive('heading', { level: 3 })}
              title="عنوان صغير"
            >
              <Heading3 className="w-4 h-4" />
            </ToolbarButton>
          </div>

          {/* Lists */}
          <div className="flex items-center space-x-1 space-x-reverse border-r border-border/30 pr-2">
            <ToolbarButton
              onClick={() => editor?.chain().focus().toggleBulletList().run()}
              isActive={editor?.isActive('bulletList')}
              title="قائمة نقطية"
            >
              <List className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor?.chain().focus().toggleOrderedList().run()}
              isActive={editor?.isActive('orderedList')}
              title="قائمة مرقمة"
            >
              <ListOrdered className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor?.chain().focus().toggleTaskList().run()}
              isActive={editor?.isActive('taskList')}
              title="قائمة مهام"
            >
              <CheckSquare className="w-4 h-4" />
            </ToolbarButton>
          </div>

          {/* Alignment */}
          <div className="flex items-center space-x-1 space-x-reverse">
            <ToolbarButton
              onClick={() => editor?.chain().focus().setTextAlign('right').run()}
              isActive={editor?.isActive({ textAlign: 'right' })}
              title="محاذاة يمين"
            >
              <AlignRight className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor?.chain().focus().setTextAlign('center').run()}
              isActive={editor?.isActive({ textAlign: 'center' })}
              title="محاذاة وسط"
            >
              <AlignCenter className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor?.chain().focus().setTextAlign('left').run()}
              isActive={editor?.isActive({ textAlign: 'left' })}
              title="محاذاة يسار"
            >
              <AlignLeft className="w-4 h-4" />
            </ToolbarButton>
          </div>
        </div>
      </div>

      {/* Main Editor */}
      <div className="relative bg-background rounded-xl border border-border/50 shadow-sm overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-muted/10 via-accent/5 to-transparent pointer-events-none" />
        
        {/* Animated Background Shapes */}
        <motion.div
          animate={{
            scale: [1, 1.1, 1],
            opacity: [0.05, 0.1, 0.05]
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
          }}
          className="absolute top-10 right-10 w-32 h-32 rounded-full bg-primary/10 blur-xl pointer-events-none"
        />

        <div className="relative z-10">
          <TipTapEditorContent 
            editor={editor} 
            className="min-h-[500px] focus-within:ring-2 focus-within:ring-primary/20 focus-within:border-primary/30 transition-all duration-200"
          />
        </div>
      </div>

      {/* Editor Stats */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.5 }}
        className="flex items-center justify-between mt-4 text-xs text-muted-foreground"
      >
        <div className="flex items-center space-x-4 space-x-reverse">
          <span>الكلمات: {editor?.storage?.characterCount?.words?.() || 0}</span>
          <span>الأحرف: {editor?.storage?.characterCount?.characters?.() || 0}</span>
        </div>
        <div className="flex items-center space-x-2 space-x-reverse">
          <span>اضغط</span>
          <kbd className="px-2 py-1 bg-muted rounded text-xs">Ctrl + S</kbd>
          <span>للحفظ</span>
        </div>
      </motion.div>
    </motion.div>
  )
}
