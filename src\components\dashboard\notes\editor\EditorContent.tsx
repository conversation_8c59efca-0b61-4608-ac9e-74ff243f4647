import { useState } from 'react'
import { OutputData } from '@editorjs/editorjs'
import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { FloatingAIAssistant } from "./FloatingAIAssistant"
import { ImageUploadCard } from "./ImageUploadCard"
import { LinkInsertCard } from "./LinkInsertCard"
import { EditorJSContent } from "./EditorJSContent"
import {
  Bold,
  Italic,
  Underline as UnderlineIcon,
  Strikethrough,
  Highlighter as HighlightIcon,
  AlignRight,
  AlignCenter,
  AlignLeft,
  List,
  ListOrdered,
  Quote,
  Code,
  Minus,
  Image as ImageIcon,
  Link as LinkIcon,
  Palette,
  Heading1,
  Heading2,
  Heading3
} from "lucide-react"


interface EditorContentProps {
  content: string | OutputData
  onContentChange: (content: OutputData) => void
  isRTL?: boolean
  onAIAction?: (action: string) => void
}

export function EditorContent({ content, onContentChange, isRTL = true, onAIAction }: EditorContentProps) {
  const [isImageModalOpen, setIsImageModalOpen] = useState(false)
  const [isLinkModalOpen, setIsLinkModalOpen] = useState(false)
  const [selectedText, setSelectedText] = useState('')

  // Safety checks for props to prevent React error #130
  const safeContent = content || ''
  const safeOnContentChange = typeof onContentChange === 'function' ? onContentChange : () => {}
  const safeOnAIAction = typeof onAIAction === 'function' ? onAIAction : undefined

  const handleImageInsert = (src: string, alt?: string) => {
    // Use global method to insert image into Editor.js
    if ((window as any).editorJSInsertImage) {
      (window as any).editorJSInsertImage(src, alt)
    }
    setIsImageModalOpen(false)
  }

  const handleLinkInsert = (href: string, text?: string) => {
    // Use global method to insert link into Editor.js
    if ((window as any).editorJSInsertLink) {
      (window as any).editorJSInsertLink(href, text)
    }
    setIsLinkModalOpen(false)
  }

  // Calculate word and character count manually to avoid React rendering issues
  const getWordCount = (content: OutputData): number => {
    if (!content.blocks) return 0
    const text = content.blocks
      .map(block => {
        if (block.type === 'paragraph' || block.type === 'header') {
          return block.data.text?.replace(/<[^>]*>/g, '') || ''
        }
        return ''
      })
      .join(' ')
      .trim()
    return text ? text.split(/\s+/).length : 0
  }

  const getCharCount = (content: OutputData): number => {
    if (!content.blocks) return 0
    const text = content.blocks
      .map(block => {
        if (block.type === 'paragraph' || block.type === 'header') {
          return block.data.text?.replace(/<[^>]*>/g, '') || ''
        }
        return ''
      })
      .join('')
      .trim()
    return text.length
  }

  // Handle content change from Editor.js
  const handleEditorChange = (outputData: OutputData) => {
    safeOnContentChange(outputData)
  }
  // Convert content for Editor.js if needed
  const editorContent = typeof safeContent === 'string' ? safeContent : safeContent

  // Get current content for word/char count
  const currentContent = typeof editorContent === 'string'
    ? { blocks: [], time: Date.now(), version: "2.28.2" } as OutputData
    : editorContent

  // ToolbarButton component for consistent styling
  const ToolbarButton = ({
    onClick,
    isActive,
    children,
    title
  }: {
    onClick: () => void
    isActive?: boolean
    children: React.ReactNode
    title: string
  }) => (
    <Button
      onClick={onClick}
      variant={isActive ? "default" : "ghost"}
      size="sm"
      className={`w-8 h-8 p-0 rounded-lg transition-all duration-200 ${
        isActive
          ? 'bg-primary text-white shadow-sm'
          : 'hover:bg-muted/50 text-muted-foreground hover:text-foreground'
      }`}
      title={title}
    >
      {children}
    </Button>
  )



  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.2 }}
      className="relative h-full"
    >
      {/* Stable Formatting Toolbar */}
      <div className="sticky top-0 z-20 mb-4 p-3 bg-background/95 backdrop-blur-md rounded-xl border border-border/30 shadow-sm">
        <div className="flex items-center space-x-2 space-x-reverse flex-wrap gap-2">
          {/* Text Formatting */}
          <div className="flex items-center space-x-1 space-x-reverse">
            <ToolbarButton
              onClick={() => {
                // Trigger Editor.js bold formatting
                const editorJS = (window as any).editorJSInstance;
                if (editorJS) {
                  editorJS.inlineToolbar.open();
                  editorJS.inlineToolbar.toggleBlockSettings('bold');
                }
              }}
              title="غامق"
            >
              <Bold className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => {
                // Trigger Editor.js italic formatting
                const editorJS = (window as any).editorJSInstance;
                if (editorJS) {
                  editorJS.inlineToolbar.open();
                  editorJS.inlineToolbar.toggleBlockSettings('italic');
                }
              }}
              title="مائل"
            >
              <Italic className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => {
                // Trigger Editor.js underline formatting
                const editorJS = (window as any).editorJSInstance;
                if (editorJS) {
                  editorJS.inlineToolbar.open();
                  editorJS.inlineToolbar.toggleBlockSettings('underline');
                }
              }}
              title="تحته خط"
            >
              <UnderlineIcon className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => {
                // Trigger Editor.js highlight formatting
                const editorJS = (window as any).editorJSInstance;
                if (editorJS) {
                  editorJS.inlineToolbar.open();
                  editorJS.inlineToolbar.toggleBlockSettings('marker');
                }
              }}
              title="تمييز"
            >
              <HighlightIcon className="w-4 h-4" />
            </ToolbarButton>
          </div>

          {/* Headings */}
          <div className="flex items-center space-x-1 space-x-reverse border-r border-border/30 pr-2">
            <ToolbarButton
              onClick={() => {
                // Trigger Editor.js h1 formatting
                const editorJS = (window as any).editorJSInstance;
                if (editorJS) {
                  editorJS.blocks.insert('header', { level: 1 });
                }
              }}
              title="عنوان كبير"
            >
              <Heading1 className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => {
                // Trigger Editor.js h2 formatting
                const editorJS = (window as any).editorJSInstance;
                if (editorJS) {
                  editorJS.blocks.insert('header', { level: 2 });
                }
              }}
              title="عنوان متوسط"
            >
              <Heading2 className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => {
                // Trigger Editor.js h3 formatting
                const editorJS = (window as any).editorJSInstance;
                if (editorJS) {
                  editorJS.blocks.insert('header', { level: 3 });
                }
              }}
              title="عنوان صغير"
            >
              <Heading3 className="w-4 h-4" />
            </ToolbarButton>
          </div>

          {/* Lists */}
          <div className="flex items-center space-x-1 space-x-reverse border-r border-border/30 pr-2">
            <ToolbarButton
              onClick={() => {
                // Trigger Editor.js bullet list
                const editorJS = (window as any).editorJSInstance;
                if (editorJS) {
                  editorJS.blocks.insert('list', { style: 'unordered' });
                }
              }}
              title="قائمة نقطية"
            >
              <List className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => {
                // Trigger Editor.js ordered list
                const editorJS = (window as any).editorJSInstance;
                if (editorJS) {
                  editorJS.blocks.insert('list', { style: 'ordered' });
                }
              }}
              title="قائمة مرقمة"
            >
              <ListOrdered className="w-4 h-4" />
            </ToolbarButton>
          </div>

          {/* Special Formatting */}
          <div className="flex items-center space-x-1 space-x-reverse border-r border-border/30 pr-2">
            <ToolbarButton
              onClick={() => {
                // Trigger Editor.js quote
                const editorJS = (window as any).editorJSInstance;
                if (editorJS) {
                  editorJS.blocks.insert('quote');
                }
              }}
              title="اقتباس"
            >
              <Quote className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => {
                // Trigger Editor.js code
                const editorJS = (window as any).editorJSInstance;
                if (editorJS) {
                  editorJS.blocks.insert('code');
                }
              }}
              title="كود"
            >
              <Code className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => {
                // Trigger Editor.js delimiter
                const editorJS = (window as any).editorJSInstance;
                if (editorJS) {
                  editorJS.blocks.insert('delimiter');
                }
              }}
              title="خط فاصل"
            >
              <Minus className="w-4 h-4" />
            </ToolbarButton>
          </div>

          {/* Media */}
          <div className="flex items-center space-x-1 space-x-reverse">
            <ToolbarButton
              onClick={() => setIsImageModalOpen(true)}
              title="إدراج صورة"
            >
              <ImageIcon className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => setIsLinkModalOpen(true)}
              title="إدراج رابط"
            >
              <LinkIcon className="w-4 h-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => {
                // Color picker functionality
                const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']
                const color = colors[Math.floor(Math.random() * colors.length)]
                // Apply color to selected text
                const editorJS = (window as any).editorJSInstance;
                if (editorJS) {
                  editorJS.inlineToolbar.open();
                  // Apply color if possible
                }
              }}
              title="ألوان التمييز"
            >
              <Palette className="w-4 h-4" />
            </ToolbarButton>
          </div>
        </div>
      </div>

      {/* Editor.js Content */}
      <EditorJSContent
        content={editorContent}
        onContentChange={handleEditorChange}
        isRTL={isRTL}
        onImageInsert={() => setIsImageModalOpen(true)}
        onLinkInsert={() => setIsLinkModalOpen(true)}
      />

      {/* Stats Bar */}
      <div className="mt-4 flex items-center justify-between text-xs text-muted-foreground bg-muted/30 rounded-lg px-3 py-2">
        <div className="flex items-center space-x-4 space-x-reverse">
          <span>الكلمات: {getWordCount(currentContent)}</span>
          <span>الأحرف: {getCharCount(currentContent)}</span>
        </div>
        <div className="flex items-center space-x-2 space-x-reverse">
          <span>آخر تحديث: {new Date().toLocaleTimeString('ar-SA')}</span>
        </div>
      </div>

      {/* Image Upload Modal */}
      {isImageModalOpen && (
        <ImageUploadCard
          onImageInsert={handleImageInsert}
          onClose={() => setIsImageModalOpen(false)}
        />
      )}

      {/* Link Insert Modal */}
      {isLinkModalOpen && (
        <LinkInsertCard
          selectedText={selectedText}
          onLinkInsert={handleLinkInsert}
          onClose={() => setIsLinkModalOpen(false)}
        />
      )}

      {/* Floating AI Assistant */}
      {safeOnAIAction && (
        <FloatingAIAssistant
          onAIAction={safeOnAIAction}
          selectedText=""
        />
      )}
    </motion.div>
  )
}
