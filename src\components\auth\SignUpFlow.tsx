import { useEffect } from "react"
import { useNavigate } from "react-router-dom"
import { AuthLayout } from "./shared/AuthLayout"
import { ProgressIndicator } from "./shared/ProgressIndicator"
import { BasicInfoStep } from "./steps/BasicInfoStep"
import { UsageIntentStep } from "./steps/UsageIntentStep"
import { SmartFeaturesStep } from "./steps/SmartFeaturesStep"
import { UpgradeStep } from "./steps/UpgradeStep"
import { InitialSetupStep } from "./steps/InitialSetupStep"
import { useAuthStore } from "@/stores/authStore"

const TOTAL_STEPS = 5

const stepTitles = [
  "👋 أهلاً بك في Nots",
  "كيف تخطط لاستخدام Nots؟",
  "هل ترغب بتجربة الميزات الذكية؟",
  "احصل على أقصى استفادة من Nots",
  "لنجهز ملاحظاتك"
]

const stepDescriptions = [
  "لنبدأ رحلتك مع الملاحظات الذكية. أدخل معلوماتك الأساسية.",
  "سيساعدنا هذا في تخصيص تجربتك مع Nots بما يناسب احتياجاتك.",
  "اختر الميزات الذكية التي تريد تفعيلها لتحسين تجربة الملاحظات.",
  "اختر الخطة التي تناسبك للحصول على أفضل تجربة مع Nots.",
  "أنشئ المجلدات الأولية لتنظيم ملاحظاتك بطريقة مثالية."
]

export function SignUpFlow() {
  const navigate = useNavigate()
  const { currentStep, setCurrentStep, isAuthenticated, resetSignUpData } = useAuthStore()

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/')
    }
  }, [isAuthenticated, navigate])

  // Reset signup data when component mounts
  useEffect(() => {
    resetSignUpData()
  }, [resetSignUpData])

  const handleNext = () => {
    if (currentStep < TOTAL_STEPS) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleStepClick = (step: number) => {
    if (step < currentStep) {
      setCurrentStep(step)
    }
  }

  const handleComplete = () => {
    // Navigate to main app after successful signup
    navigate('/')
  }

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return <BasicInfoStep onNext={handleNext} />
      case 2:
        return <UsageIntentStep onNext={handleNext} onBack={handleBack} />
      case 3:
        return <SmartFeaturesStep onNext={handleNext} onBack={handleBack} />
      case 4:
        return <UpgradeStep onNext={handleNext} onBack={handleBack} />
      case 5:
        return <InitialSetupStep onComplete={handleComplete} onBack={handleBack} />
      default:
        return <BasicInfoStep onNext={handleNext} />
    }
  }

  return (
    <AuthLayout
      title={stepTitles[currentStep - 1]}
      description={stepDescriptions[currentStep - 1]}
      showBackButton={currentStep > 1}
      onBack={handleBack}
    >
      <div className="space-y-6">
        {/* Progress Indicator */}
        <ProgressIndicator
          currentStep={currentStep}
          totalSteps={TOTAL_STEPS}
          onStepClick={handleStepClick}
        />

        {/* Current Step Content */}
        {renderCurrentStep()}
      </div>
    </AuthLayout>
  )
}
