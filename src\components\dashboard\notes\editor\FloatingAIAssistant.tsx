import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Button } from "@/components/ui/button"
import { 
  Sparkles,
  Wand2,
  FileText,
  Languages,
  BookOpen,
  X
} from "lucide-react"
import { toast } from "sonner"

interface FloatingAIAssistantProps {
  onAIAction: (action: string) => void
}

export function FloatingAIAssistant({ onAIAction }: FloatingAIAssistantProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  const aiFeatures = [
    {
      id: 'improve',
      label: 'تحسين النص',
      icon: Wand2,
      description: 'تحسين جودة النص وأسلوب الكتابة',
      color: 'from-blue-500 to-blue-600'
    },
    {
      id: 'summarize',
      label: 'تلخيص',
      icon: FileText,
      description: 'إنشاء ملخص مختصر للمحتوى',
      color: 'from-green-500 to-green-600'
    },
    {
      id: 'grammar',
      label: 'تصحيح القواعد',
      icon: BookOpen,
      description: 'تصحيح الأخطاء النحوية والإملائية',
      color: 'from-orange-500 to-orange-600'
    },
    {
      id: 'translate',
      label: 'ترجمة',
      icon: Languages,
      description: 'ترجمة النص إلى لغات أخرى',
      color: 'from-purple-500 to-purple-600'
    }
  ]

  const handleAIFeature = (feature: typeof aiFeatures[0]) => {
    onAIAction(feature.id)
    toast.info(`ميزة ${feature.label} ستكون متاحة قريباً`)
    setIsExpanded(false)
  }

  return (
    <div className="fixed bottom-6 left-6 z-50">
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
            className="absolute bottom-20 left-1/2 transform -translate-x-1/2"
          >
            {aiFeatures.map((feature, index) => {
              // Calculate circular positions
              const angle = (index * 90) - 45 // Spread in a quarter circle
              const radius = 80
              const x = Math.cos((angle * Math.PI) / 180) * radius
              const y = Math.sin((angle * Math.PI) / 180) * radius

              return (
                <motion.div
                  key={feature.id}
                  initial={{ opacity: 0, scale: 0, x: 0, y: 0 }}
                  animate={{ opacity: 1, scale: 1, x, y }}
                  exit={{ opacity: 0, scale: 0, x: 0, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1, ease: "easeOut" }}
                  className="absolute"
                  style={{ transform: `translate(${x}px, ${y}px)` }}
                >
                  <div className="relative group">
                    <Button
                      onClick={() => handleAIFeature(feature)}
                      className={`
                        w-14 h-14 rounded-full bg-gradient-to-r ${feature.color} hover:opacity-90
                        text-white shadow-lg hover:shadow-xl
                        transition-all duration-300 flex items-center justify-center
                        hover:scale-110
                      `}
                    >
                      <feature.icon className="w-6 h-6" />
                    </Button>

                    {/* Tooltip */}
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      whileHover={{ opacity: 1, scale: 1 }}
                      className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 px-3 py-2 bg-background/95 backdrop-blur-md border border-border/50 rounded-lg shadow-lg whitespace-nowrap z-10"
                    >
                      <div className="text-sm font-medium text-foreground">{feature.label}</div>
                      <div className="text-xs text-muted-foreground">{feature.description}</div>

                      {/* Arrow */}
                      <div className="absolute top-full left-1/2 transform -translate-x-1/2">
                        <div className="w-2 h-2 bg-background/95 border-r border-b border-border/50 rotate-45"></div>
                      </div>
                    </motion.div>
                  </div>
                </motion.div>
              )
            })}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main AI Button */}
      <motion.div
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        className="relative z-50"
      >
        <Button
          onClick={() => {
            console.log('AI button clicked, current state:', isExpanded)
            setIsExpanded(!isExpanded)
          }}
          className={`
            w-16 h-16 rounded-full bg-gradient-to-r from-primary to-secondary
            hover:opacity-90 text-white shadow-lg hover:shadow-xl
            transition-all duration-300 flex items-center justify-center
            cursor-pointer relative z-50
            ${isExpanded ? 'rotate-45' : ''}
          `}
        >
          {isExpanded ? (
            <X className="w-6 h-6" />
          ) : (
            <Sparkles className="w-6 h-6" />
          )}
        </Button>

        {/* Pulsing Ring Animation */}
        {!isExpanded && (
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.5, 0, 0.5]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute inset-0 rounded-full bg-gradient-to-r from-primary to-secondary"
          />
        )}
      </motion.div>

      {/* Tooltip */}
      {!isExpanded && (
        <motion.div
          initial={{ opacity: 0, x: 10 }}
          animate={{ opacity: 1, x: 0 }}
          className="absolute right-full top-1/2 transform -translate-y-1/2 mr-3 px-3 py-2 bg-background/95 backdrop-blur-md border border-border/50 rounded-lg shadow-lg whitespace-nowrap"
        >
          <div className="text-sm font-medium text-foreground">مساعد الذكاء الاصطناعي</div>
          <div className="text-xs text-muted-foreground">اضغط للوصول لأدوات الذكاء الاصطناعي</div>
          
          {/* Arrow */}
          <div className="absolute left-0 top-1/2 transform -translate-y-1/2 translate-x-full">
            <div className="w-2 h-2 bg-background/95 border-r border-b border-border/50 rotate-45"></div>
          </div>
        </motion.div>
      )}
    </div>
  )
}
