import { motion } from "framer-motion"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { 
  Brain, 
  Lightbulb, 
  Mic, 
  BarChart3,
  TrendingUp
} from "lucide-react"

interface AIUsageData {
  type: 'summary' | 'expansion' | 'voice-to-text'
  label: string
  count: number
  percentage: number
  color: string
  bgColor: string
  icon: React.ElementType
}

const aiUsageData: AIUsageData[] = [
  {
    type: 'summary',
    label: 'التلخيص التلقائي',
    count: 18,
    percentage: 45,
    color: 'text-purple-600',
    bgColor: 'bg-purple-500',
    icon: Brain
  },
  {
    type: 'expansion',
    label: 'توسيع الأفكار',
    count: 12,
    percentage: 30,
    color: 'text-orange-600',
    bgColor: 'bg-orange-500',
    icon: Lightbulb
  },
  {
    type: 'voice-to-text',
    label: 'تحويل الصوت لنص',
    count: 10,
    percentage: 25,
    color: 'text-green-600',
    bgColor: 'bg-green-500',
    icon: Mic
  }
]

// Mock weekly data for the chart
const weeklyData = [
  { day: 'السبت', summary: 3, expansion: 2, voice: 1 },
  { day: 'الأحد', summary: 5, expansion: 3, voice: 2 },
  { day: 'الاثنين', summary: 4, expansion: 1, voice: 3 },
  { day: 'الثلاثاء', summary: 2, expansion: 4, voice: 1 },
  { day: 'الأربعاء', summary: 3, expansion: 2, voice: 2 },
  { day: 'الخميس', summary: 1, expansion: 0, voice: 1 },
  { day: 'الجمعة', summary: 0, expansion: 0, voice: 0 }
]

export function AIAnalytics() {
  const totalUsage = aiUsageData.reduce((sum, item) => sum + item.count, 0)
  const maxDayUsage = Math.max(...weeklyData.map(day => day.summary + day.expansion + day.voice))

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.5 }}
      className="grid grid-cols-1 lg:grid-cols-2 gap-6"
    >
      {/* AI Usage Distribution */}
      <Card className="border-border/50">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 space-x-reverse">
            <Brain className="w-5 h-5 text-primary" />
            <span>استخدام الذكاء الاصطناعي</span>
          </CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-foreground mb-2">
              {totalUsage}
            </div>
            <p className="text-sm text-muted-foreground">
              إجمالي العمليات هذا الشهر
            </p>
          </div>
          
          <div className="space-y-4">
            {aiUsageData.map((item, index) => {
              const Icon = item.icon
              
              return (
                <motion.div
                  key={item.type}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="space-y-2"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <div className={`w-8 h-8 rounded-lg ${item.bgColor} bg-opacity-20 flex items-center justify-center`}>
                        <Icon className={`w-4 h-4 ${item.color}`} />
                      </div>
                      <span className="text-sm font-medium text-foreground">
                        {item.label}
                      </span>
                    </div>
                    <div className="text-right">
                      <span className="text-sm font-bold text-foreground">
                        {item.count}
                      </span>
                      <span className="text-xs text-muted-foreground mr-1">
                        ({item.percentage}%)
                      </span>
                    </div>
                  </div>
                  
                  <Progress 
                    value={item.percentage} 
                    className="h-2"
                    style={{
                      background: `linear-gradient(to left, ${item.bgColor}20, transparent)`
                    }}
                  />
                </motion.div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Weekly Activity Chart */}
      <Card className="border-border/50">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 space-x-reverse">
            <BarChart3 className="w-5 h-5 text-primary" />
            <span>النشاط الأسبوعي</span>
          </CardTitle>
        </CardHeader>
        
        <CardContent>
          <div className="space-y-4">
            {weeklyData.map((day, index) => {
              const totalDayUsage = day.summary + day.expansion + day.voice
              const dayPercentage = maxDayUsage > 0 ? (totalDayUsage / maxDayUsage) * 100 : 0
              
              return (
                <motion.div
                  key={day.day}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="space-y-2"
                >
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-foreground">
                      {day.day}
                    </span>
                    <div className="flex items-center space-x-1 space-x-reverse text-xs text-muted-foreground">
                      <span>{totalDayUsage}</span>
                      {totalDayUsage > 0 && (
                        <TrendingUp className="w-3 h-3" />
                      )}
                    </div>
                  </div>
                  
                  <div className="relative h-6 bg-muted/30 rounded-full overflow-hidden">
                    {/* Summary bar */}
                    <div
                      className="absolute top-0 right-0 h-full bg-purple-500 transition-all duration-500"
                      style={{
                        width: `${maxDayUsage > 0 ? (day.summary / maxDayUsage) * 100 : 0}%`
                      }}
                    />
                    
                    {/* Expansion bar */}
                    <div
                      className="absolute top-0 h-full bg-orange-500 transition-all duration-500"
                      style={{
                        right: `${maxDayUsage > 0 ? (day.summary / maxDayUsage) * 100 : 0}%`,
                        width: `${maxDayUsage > 0 ? (day.expansion / maxDayUsage) * 100 : 0}%`
                      }}
                    />
                    
                    {/* Voice bar */}
                    <div
                      className="absolute top-0 h-full bg-green-500 transition-all duration-500"
                      style={{
                        right: `${maxDayUsage > 0 ? ((day.summary + day.expansion) / maxDayUsage) * 100 : 0}%`,
                        width: `${maxDayUsage > 0 ? (day.voice / maxDayUsage) * 100 : 0}%`
                      }}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <div className="flex items-center space-x-4 space-x-reverse">
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <div className="w-2 h-2 bg-purple-500 rounded-full" />
                        <span>{day.summary}</span>
                      </div>
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <div className="w-2 h-2 bg-orange-500 rounded-full" />
                        <span>{day.expansion}</span>
                      </div>
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <div className="w-2 h-2 bg-green-500 rounded-full" />
                        <span>{day.voice}</span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )
            })}
          </div>
          
          {/* Legend */}
          <div className="mt-6 pt-4 border-t border-border/30">
            <div className="flex items-center justify-center space-x-6 space-x-reverse text-xs">
              <div className="flex items-center space-x-1 space-x-reverse">
                <div className="w-3 h-3 bg-purple-500 rounded-full" />
                <span className="text-muted-foreground">تلخيص</span>
              </div>
              <div className="flex items-center space-x-1 space-x-reverse">
                <div className="w-3 h-3 bg-orange-500 rounded-full" />
                <span className="text-muted-foreground">توسيع</span>
              </div>
              <div className="flex items-center space-x-1 space-x-reverse">
                <div className="w-3 h-3 bg-green-500 rounded-full" />
                <span className="text-muted-foreground">صوت لنص</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
