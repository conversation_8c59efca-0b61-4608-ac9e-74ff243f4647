import { motion } from "framer-motion"
import { Check, X } from "lucide-react"
import { checkPasswordStrength } from "@/lib/validations/auth"

interface PasswordStrengthMeterProps {
  password: string
  showRequirements?: boolean
}

export function PasswordStrengthMeter({ password, showRequirements = true }: PasswordStrengthMeterProps) {
  const strength = checkPasswordStrength(password)
  
  const requirements = [
    {
      label: "8 أحرف على الأقل",
      test: (pwd: string) => pwd.length >= 8
    },
    {
      label: "حرف كبير واحد على الأقل",
      test: (pwd: string) => /[A-Z]/.test(pwd)
    },
    {
      label: "حرف صغير واحد على الأقل", 
      test: (pwd: string) => /[a-z]/.test(pwd)
    },
    {
      label: "رقم واحد على الأقل",
      test: (pwd: string) => /[0-9]/.test(pwd)
    },
    {
      label: "رمز خاص واحد على الأقل (!@#$%^&*)",
      test: (pwd: string) => /[!@#$%^&*]/.test(pwd)
    }
  ]

  if (!password) return null

  return (
    <div className="mt-3 space-y-3">
      {/* Strength Bar */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">قوة كلمة المرور:</span>
          <span className={`text-sm font-medium ${
            strength.score <= 2 ? 'text-red-500' :
            strength.score <= 3 ? 'text-yellow-500' :
            strength.score <= 4 ? 'text-blue-500' : 'text-green-500'
          }`}>
            {strength.label}
          </span>
        </div>
        
        <div className="w-full bg-muted rounded-full h-2 overflow-hidden">
          <motion.div
            className={`h-full ${strength.color} transition-all duration-300`}
            initial={{ width: 0 }}
            animate={{ width: `${(strength.score / 5) * 100}%` }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          />
        </div>
      </div>

      {/* Requirements List */}
      {showRequirements && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: "auto" }}
          transition={{ duration: 0.3 }}
          className="space-y-2"
        >
          <p className="text-sm text-muted-foreground mb-2">متطلبات كلمة المرور:</p>
          <div className="space-y-1">
            {requirements.map((req, index) => {
              const isValid = req.test(password)
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className={`flex items-center space-x-2 space-x-reverse text-sm ${
                    isValid ? 'text-green-600' : 'text-muted-foreground'
                  }`}
                >
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ duration: 0.2 }}
                  >
                    {isValid ? (
                      <Check className="w-4 h-4 text-green-500" />
                    ) : (
                      <X className="w-4 h-4 text-muted-foreground" />
                    )}
                  </motion.div>
                  <span className={isValid ? 'line-through' : ''}>{req.label}</span>
                </motion.div>
              )
            })}
          </div>
        </motion.div>
      )}
    </div>
  )
}
