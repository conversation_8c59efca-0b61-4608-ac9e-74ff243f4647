'use client'

import { motion } from 'framer-motion'
import { 
  Calendar, 
  Clock, 
  ExternalLink, 
  FileText, 
  MapPin,
  Users,
  CalendarDays,
  ArrowRight
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { CalendarEventItem } from '@/pages/dashboard/Notifications'

interface CalendarEventsProps {
  events: CalendarEventItem[]
  isLoading: boolean
}

interface EventCardProps {
  event: CalendarEventItem
  index: number
}

function EventCard({ event, index }: EventCardProps) {
  const formatDateTime = (date: Date) => {
    return new Intl.DateTimeFormat('ar-SA', {
      weekday: 'short',
      day: 'numeric',
      month: 'short',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date)
  }

  const formatDuration = (start: Date, end: Date) => {
    const diffMs = end.getTime() - start.getTime()
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60))
    
    if (diffHours > 0) {
      return `${diffHours} ساعة${diffMinutes > 0 ? ` و ${diffMinutes} دقيقة` : ''}`
    }
    return `${diffMinutes} دقيقة`
  }

  const getTimeUntilEvent = (date: Date) => {
    const now = new Date()
    const diffMs = date.getTime() - now.getTime()
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60))
    
    if (diffMs < 0) return 'بدأ'
    if (diffHours > 24) {
      const days = Math.floor(diffHours / 24)
      return `خلال ${days} يوم`
    }
    if (diffHours > 0) {
      return `خلال ${diffHours} ساعة`
    }
    return `خلال ${diffMinutes} دقيقة`
  }

  const isUpcoming = event.startDate.getTime() > new Date().getTime()
  const timeUntil = getTimeUntilEvent(event.startDate)

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className="bg-background rounded-xl border border-border/50 shadow-sm hover:shadow-md transition-all duration-300 p-6 group"
    >
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-start justify-between">
          <div className="flex-1 space-y-2">
            <div className="flex items-center gap-3">
              <div 
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: event.color }}
              />
              <h3 className="font-semibold text-foreground group-hover:text-primary transition-colors">
                {event.title}
              </h3>
              {isUpcoming && (
                <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/30 dark:text-blue-400">
                  {timeUntil}
                </Badge>
              )}
            </div>
            
            {event.description && (
              <p className="text-sm text-muted-foreground line-clamp-2">
                {event.description}
              </p>
            )}
          </div>

          <Badge variant="secondary" className="text-xs">
            {event.category}
          </Badge>
        </div>

        {/* Event Details */}
        <div className="space-y-3">
          {/* Date and Time */}
          <div className="flex items-center gap-4 text-sm">
            <div className="flex items-center gap-2 text-muted-foreground">
              <Calendar className="w-4 h-4" />
              <span>{formatDateTime(event.startDate)}</span>
            </div>
            <div className="flex items-center gap-2 text-muted-foreground">
              <Clock className="w-4 h-4" />
              <span>{formatDuration(event.startDate, event.endDate)}</span>
            </div>
          </div>

          {/* Note Link */}
          {event.noteId && (
            <div className="flex items-center gap-2 text-sm">
              <FileText className="w-4 h-4 text-muted-foreground" />
              <span className="text-muted-foreground">مرتبط بملاحظة</span>
              <Button
                variant="link"
                size="sm"
                className="h-auto p-0 text-primary hover:text-primary/80"
              >
                عرض الملاحظة
                <ArrowRight className="w-3 h-3 mr-1" />
              </Button>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-2 pt-2 border-t border-border/30">
          <Button
            size="sm"
            className="flex items-center gap-2"
          >
            <CalendarDays className="w-4 h-4" />
            <span>عرض في التقويم</span>
          </Button>

          {event.noteId && (
            <Button
              size="sm"
              variant="outline"
              className="flex items-center gap-2"
            >
              <FileText className="w-4 h-4" />
              <span>فتح الملاحظة</span>
            </Button>
          )}

          <Button
            size="sm"
            variant="outline"
            className="flex items-center gap-2"
          >
            <ExternalLink className="w-4 h-4" />
            <span>تفاصيل</span>
          </Button>
        </div>
      </div>
    </motion.div>
  )
}

function EmptyState() {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
      className="text-center py-12"
    >
      <div className="w-24 h-24 mx-auto mb-6 bg-muted/30 rounded-full flex items-center justify-center">
        <Calendar className="w-12 h-12 text-muted-foreground" />
      </div>
      <h3 className="text-lg font-semibold text-foreground mb-2">
        لا توجد أحداث قادمة
      </h3>
      <p className="text-muted-foreground max-w-md mx-auto">
        عندما تقوم بجدولة أحداث في التقويم، ستظهر هنا لتبقيك على اطلاع بمواعيدك.
      </p>
      <Button className="mt-4" variant="outline">
        <Calendar className="w-4 h-4 mr-2" />
        إضافة حدث جديد
      </Button>
    </motion.div>
  )
}

function LoadingSkeleton() {
  return (
    <div className="space-y-4">
      {Array.from({ length: 2 }).map((_, i) => (
        <motion.div
          key={i}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: i * 0.1 }}
          className="bg-background rounded-xl border border-border/50 shadow-sm p-6"
        >
          <div className="space-y-4">
            <div className="flex items-start justify-between">
              <div className="flex-1 space-y-2">
                <div className="h-5 bg-muted/50 rounded animate-pulse w-3/4"></div>
                <div className="h-4 bg-muted/50 rounded animate-pulse w-1/2"></div>
              </div>
              <div className="h-6 bg-muted/50 rounded animate-pulse w-16"></div>
            </div>
            <div className="space-y-2">
              <div className="h-4 bg-muted/50 rounded animate-pulse w-2/3"></div>
              <div className="h-4 bg-muted/50 rounded animate-pulse w-1/2"></div>
            </div>
            <div className="flex gap-2">
              {Array.from({ length: 3 }).map((_, j) => (
                <div key={j} className="h-8 bg-muted/50 rounded animate-pulse w-20"></div>
              ))}
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  )
}

export function CalendarEvents({ events, isLoading }: CalendarEventsProps) {
  // Sort events by start date
  const sortedEvents = [...events].sort((a, b) => 
    a.startDate.getTime() - b.startDate.getTime()
  ).slice(0, 5) // Show only next 5 events

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.3 }}
      className="space-y-6"
    >
      {/* Section Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-lg flex items-center justify-center">
            <Calendar className="w-4 h-4 text-white" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-foreground">الأحداث القادمة</h2>
            <p className="text-sm text-muted-foreground">
              أحداثك المجدولة في التقويم
            </p>
          </div>
        </div>

        <Button variant="outline" size="sm" className="flex items-center gap-2">
          <Calendar className="w-4 h-4" />
          <span>عرض التقويم</span>
        </Button>
      </div>

      {/* Content */}
      {isLoading ? (
        <LoadingSkeleton />
      ) : sortedEvents.length === 0 ? (
        <EmptyState />
      ) : (
        <div className="space-y-4">
          {sortedEvents.map((event, index) => (
            <EventCard
              key={event.id}
              event={event}
              index={index}
            />
          ))}
          
          {events.length > 5 && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.5 }}
              className="text-center pt-4"
            >
              <Button variant="outline" className="flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                <span>عرض جميع الأحداث ({events.length})</span>
              </Button>
            </motion.div>
          )}
        </div>
      )}
    </motion.div>
  )
}
