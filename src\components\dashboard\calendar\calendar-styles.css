/* Custom FullCalendar Styles for Arabic RTL Support */

.fc {
  direction: rtl;
  font-family: '<PERSON><PERSON><PERSON>', sans-serif;
}

/* Header styling */
.fc-header-toolbar {
  margin-bottom: 1rem;
}

/* Day headers */
.fc-col-header-cell {
  background: hsl(var(--muted) / 0.3);
  border: 1px solid hsl(var(--border) / 0.3);
  font-weight: 600;
  color: hsl(var(--foreground));
  padding: 0.75rem 0.5rem;
}

.fc-col-header-cell-cushion {
  color: hsl(var(--foreground));
  font-weight: 600;
  text-decoration: none;
}

/* Day cells */
.fc-daygrid-day {
  border: 1px solid hsl(var(--border) / 0.2);
  background: hsl(var(--background));
  transition: background-color 0.2s ease;
}

.fc-daygrid-day:hover {
  background: hsl(var(--muted) / 0.1);
}

.fc-daygrid-day-number {
  color: hsl(var(--foreground));
  font-weight: 500;
  padding: 0.5rem;
  text-decoration: none;
}

.fc-day-today {
  background: hsl(var(--primary) / 0.1) !important;
  border-color: hsl(var(--primary) / 0.3) !important;
}

.fc-day-today .fc-daygrid-day-number {
  color: hsl(var(--primary));
  font-weight: 700;
}

/* Events */
.fc-event {
  border: none !important;
  border-radius: 0.5rem;
  padding: 0.25rem 0.5rem;
  margin: 0.125rem;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.fc-event:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.fc-event-title {
  font-weight: 600;
  font-size: 0.875rem;
}

.fc-event-time {
  font-weight: 500;
  opacity: 0.9;
}

/* Time grid styles */
.fc-timegrid-slot {
  border-color: hsl(var(--border) / 0.2);
}

.fc-timegrid-slot-label {
  color: hsl(var(--muted-foreground));
  font-weight: 500;
}

.fc-timegrid-axis {
  border-color: hsl(var(--border) / 0.3);
}

/* List view styles */
.fc-list-event {
  border-color: hsl(var(--border) / 0.3);
  background: hsl(var(--background));
}

.fc-list-event:hover {
  background: hsl(var(--muted) / 0.1);
}

.fc-list-event-title {
  color: hsl(var(--foreground));
  font-weight: 600;
}

.fc-list-event-time {
  color: hsl(var(--muted-foreground));
  font-weight: 500;
}

/* Scrollbars */
.fc-scroller::-webkit-scrollbar {
  width: 6px;
}

.fc-scroller::-webkit-scrollbar-track {
  background: hsl(var(--muted) / 0.3);
  border-radius: 3px;
}

.fc-scroller::-webkit-scrollbar-thumb {
  background: hsl(var(--primary) / 0.5);
  border-radius: 3px;
}

.fc-scroller::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary) / 0.7);
}

/* More events link */
.fc-more-link {
  color: hsl(var(--primary));
  font-weight: 600;
  text-decoration: none;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  background: hsl(var(--primary) / 0.1);
  transition: all 0.2s ease;
}

.fc-more-link:hover {
  background: hsl(var(--primary) / 0.2);
  transform: scale(1.05);
}

/* Now indicator */
.fc-timegrid-now-indicator-line {
  border-color: hsl(var(--destructive));
  border-width: 2px;
}

.fc-timegrid-now-indicator-arrow {
  border-color: hsl(var(--destructive));
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .fc-event {
    font-size: 0.75rem;
    padding: 0.125rem 0.25rem;
  }
  
  .fc-daygrid-day-number {
    font-size: 0.875rem;
    padding: 0.25rem;
  }
  
  .fc-col-header-cell {
    padding: 0.5rem 0.25rem;
    font-size: 0.875rem;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .fc-event {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  }
  
  .fc-event:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
  }
}

/* Custom event priority indicators */
.fc-event.priority-high {
  border-left: 4px solid #ef4444;
}

.fc-event.priority-medium {
  border-left: 4px solid #f59e0b;
}

.fc-event.priority-low {
  border-left: 4px solid #22c55e;
}

/* Animation for view changes */
.fc-view-harness {
  transition: opacity 0.3s ease;
}

.fc-view-harness.fc-view-harness-active {
  opacity: 1;
}

/* Loading state */
.fc-view-harness.loading {
  opacity: 0.6;
  pointer-events: none;
}

/* Custom button styles */
.fc-button {
  background: hsl(var(--primary));
  border-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  border-radius: 0.5rem;
  font-weight: 600;
  transition: all 0.2s ease;
}

.fc-button:hover {
  background: hsl(var(--primary) / 0.9);
  transform: translateY(-1px);
}

.fc-button:disabled {
  opacity: 0.5;
  transform: none;
}
