import { useState, useRef } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Upload, 
  Link as LinkIcon, 
  X, 
  Image as ImageIcon,
  AlertCircle
} from "lucide-react"
import { toast } from "sonner"

interface ImageUploadCardProps {
  isOpen: boolean
  onClose: () => void
  onImageInsert: (src: string, alt?: string) => void
}

export function ImageUploadCard({ isOpen, onClose, onImageInsert }: ImageUploadCardProps) {
  const [imageUrl, setImageUrl] = useState('')
  const [imageAlt, setImageAlt] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [dragActive, setDragActive] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleUrlSubmit = () => {
    if (!imageUrl.trim()) {
      toast.error('يرجى إدخال رابط الصورة')
      return
    }

    // Basic URL validation
    try {
      new URL(imageUrl)
      onImageInsert(imageUrl, imageAlt || 'صورة')
      handleClose()
    } catch {
      toast.error('رابط الصورة غير صحيح')
    }
  }

  const handleFileUpload = (file: File) => {
    if (!file.type.startsWith('image/')) {
      toast.error('يرجى اختيار ملف صورة صحيح')
      return
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      toast.error('حجم الصورة كبير جداً (الحد الأقصى 5 ميجابايت)')
      return
    }

    setIsLoading(true)
    
    // Convert file to base64 for demo purposes
    // In a real app, you'd upload to a server
    const reader = new FileReader()
    reader.onload = (e) => {
      const result = e.target?.result as string
      onImageInsert(result, imageAlt || file.name)
      setIsLoading(false)
      handleClose()
    }
    reader.onerror = () => {
      toast.error('فشل في قراءة الملف')
      setIsLoading(false)
    }
    reader.readAsDataURL(file)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragActive(false)
    
    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileUpload(files[0])
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setDragActive(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setDragActive(false)
  }

  const handleClose = () => {
    setImageUrl('')
    setImageAlt('')
    setIsLoading(false)
    setDragActive(false)
    onClose()
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={handleClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          transition={{ duration: 0.2 }}
          onClick={(e) => e.stopPropagation()}
          className="w-full max-w-md"
        >
          <Card className="border-border/50 shadow-xl">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg font-semibold flex items-center gap-2">
                  <ImageIcon className="w-5 h-5 text-primary" />
                  إدراج صورة
                </CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClose}
                  className="w-8 h-8 p-0 rounded-full"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </CardHeader>

            <CardContent>
              <Tabs defaultValue="upload" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="upload">رفع ملف</TabsTrigger>
                  <TabsTrigger value="url">رابط</TabsTrigger>
                </TabsList>

                <TabsContent value="upload" className="space-y-4">
                  <div
                    className={`
                      border-2 border-dashed rounded-lg p-8 text-center transition-colors
                      ${dragActive 
                        ? 'border-primary bg-primary/5' 
                        : 'border-border hover:border-primary/50'
                      }
                    `}
                    onDrop={handleDrop}
                    onDragOver={handleDragOver}
                    onDragLeave={handleDragLeave}
                  >
                    <div className="space-y-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
                        <Upload className="w-6 h-6 text-primary" />
                      </div>
                      
                      <div>
                        <p className="text-sm font-medium">اسحب وأفلت الصورة هنا</p>
                        <p className="text-xs text-muted-foreground mt-1">
                          أو اضغط لاختيار ملف
                        </p>
                      </div>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => fileInputRef.current?.click()}
                        disabled={isLoading}
                      >
                        اختيار ملف
                      </Button>

                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        className="hidden"
                        onChange={(e) => {
                          const file = e.target.files?.[0]
                          if (file) handleFileUpload(file)
                        }}
                      />

                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <AlertCircle className="w-3 h-3" />
                        الحد الأقصى: 5 ميجابايت
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="alt-upload">وصف الصورة (اختياري)</Label>
                    <Input
                      id="alt-upload"
                      value={imageAlt}
                      onChange={(e) => setImageAlt(e.target.value)}
                      placeholder="وصف مختصر للصورة..."
                      dir="rtl"
                    />
                  </div>
                </TabsContent>

                <TabsContent value="url" className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="image-url">رابط الصورة</Label>
                    <Input
                      id="image-url"
                      value={imageUrl}
                      onChange={(e) => setImageUrl(e.target.value)}
                      placeholder="https://example.com/image.jpg"
                      dir="ltr"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="alt-url">وصف الصورة (اختياري)</Label>
                    <Input
                      id="alt-url"
                      value={imageAlt}
                      onChange={(e) => setImageAlt(e.target.value)}
                      placeholder="وصف مختصر للصورة..."
                      dir="rtl"
                    />
                  </div>

                  <div className="flex gap-2 pt-2">
                    <Button
                      onClick={handleUrlSubmit}
                      disabled={!imageUrl.trim() || isLoading}
                      className="flex-1"
                    >
                      إدراج الصورة
                    </Button>
                    <Button
                      variant="outline"
                      onClick={handleClose}
                      disabled={isLoading}
                    >
                      إلغاء
                    </Button>
                  </div>
                </TabsContent>
              </Tabs>

              {isLoading && (
                <div className="flex items-center justify-center py-4">
                  <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin" />
                  <span className="mr-2 text-sm text-muted-foreground">جاري الرفع...</span>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}
