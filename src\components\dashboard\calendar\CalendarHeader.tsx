import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { ViewToggle } from './ViewToggle'
import { CalendarSearch } from './CalendarSearch'
import { motion } from 'framer-motion'
import {
  Plus,
  ChevronLeft,
  ChevronRight,
  Calendar as CalendarIcon
} from 'lucide-react'

export interface CalendarHeaderProps {
  currentDate?: Date
  onDateChange?: (date: Date) => void
  currentView?: 'month' | 'week' | 'day' | 'list'
  onViewChange?: (view: 'month' | 'week' | 'day' | 'list') => void
  searchQuery?: string
  onSearchChange?: (query: string) => void
  onFilterChange?: (filters: any) => void
}

export function CalendarHeader({
  currentDate = new Date(),
  onDateChange,
  currentView = 'month',
  onViewChange,
  searchQuery = '',
  onSearchChange,
  onFilterChange
}: CalendarHeaderProps) {
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery)

  const formatCurrentDate = (date: Date) => {
    return new Intl.DateTimeFormat('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(date)
  }

  const handlePrevious = () => {
    const newDate = new Date(currentDate)
    if (currentView === 'month') {
      newDate.setMonth(newDate.getMonth() - 1)
    } else if (currentView === 'week') {
      newDate.setDate(newDate.getDate() - 7)
    } else if (currentView === 'day') {
      newDate.setDate(newDate.getDate() - 1)
    }
    onDateChange?.(newDate)
  }

  const handleNext = () => {
    const newDate = new Date(currentDate)
    if (currentView === 'month') {
      newDate.setMonth(newDate.getMonth() + 1)
    } else if (currentView === 'week') {
      newDate.setDate(newDate.getDate() + 7)
    } else if (currentView === 'day') {
      newDate.setDate(newDate.getDate() + 1)
    }
    onDateChange?.(newDate)
  }

  const handleToday = () => {
    onDateChange?.(new Date())
  }

  const handleAddNote = () => {
    window.location.href = '/dashboard/notes/new'
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-background/95 backdrop-blur-md rounded-xl border border-border/30 shadow-sm p-4"
    >
      <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4">
        {/* Left Section - Title and Navigation */}
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <CalendarIcon className="w-6 h-6 text-primary" />
            <h1 className="text-2xl font-bold text-foreground">التقويم</h1>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handlePrevious}
              className="w-8 h-8 p-0"
            >
              <ChevronRight className="w-4 h-4" />
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleToday}
              className="px-3"
            >
              اليوم
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleNext}
              className="w-8 h-8 p-0"
            >
              <ChevronLeft className="w-4 h-4" />
            </Button>
          </div>
          
          <div className="text-lg font-medium text-muted-foreground">
            {formatCurrentDate(currentDate)}
          </div>
        </div>

        {/* Right Section - Search, View Toggle, and Add Button */}
        <div className="flex items-center gap-3 w-full lg:w-auto">
          <CalendarSearch
            value={searchQuery}
            onChange={onSearchChange || (() => {})}
            onFilter={onFilterChange}
          />

          <ViewToggle
            currentView={currentView}
            onViewChange={onViewChange}
          />

          <Button
            onClick={handleAddNote}
            className="bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-lg font-medium transition-colors"
          >
            <Plus className="w-4 h-4 ml-2" />
            إضافة ملاحظة
          </Button>
        </div>
      </div>
    </motion.div>
  )
}
