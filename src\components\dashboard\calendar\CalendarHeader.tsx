import { Button } from '@/components/ui/button'
import { ViewToggle } from './ViewToggle'
import { CalendarSearch } from './CalendarSearch'
import { motion } from 'framer-motion'
import {
  Plus,
  ChevronLeft,
  ChevronRight,
  Calendar as CalendarIcon
} from 'lucide-react'

export interface CalendarHeaderProps {
  currentDate?: Date
  onDateChange?: (date: Date) => void
  currentView?: 'month' | 'week' | 'day' | 'list'
  onViewChange?: (view: 'month' | 'week' | 'day' | 'list') => void
  searchQuery?: string
  onSearchChange?: (query: string) => void
  onFilterChange?: (filters: any) => void
}

export function CalendarHeader({
  currentDate = new Date(),
  onDateChange,
  currentView = 'month',
  onViewChange,
  searchQuery = '',
  onSearchChange,
  onFilterChange
}: CalendarHeaderProps) {
  const formatCurrentDate = (date: Date) => {
    return new Intl.DateTimeFormat('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(date)
  }

  const handlePrevious = () => {
    const newDate = new Date(currentDate)
    if (currentView === 'month') {
      newDate.setMonth(newDate.getMonth() - 1)
    } else if (currentView === 'week') {
      newDate.setDate(newDate.getDate() - 7)
    } else if (currentView === 'day') {
      newDate.setDate(newDate.getDate() - 1)
    }
    onDateChange?.(newDate)
  }

  const handleNext = () => {
    const newDate = new Date(currentDate)
    if (currentView === 'month') {
      newDate.setMonth(newDate.getMonth() + 1)
    } else if (currentView === 'week') {
      newDate.setDate(newDate.getDate() + 7)
    } else if (currentView === 'day') {
      newDate.setDate(newDate.getDate() + 1)
    }
    onDateChange?.(newDate)
  }

  const handleToday = () => {
    onDateChange?.(new Date())
  }

  const handleAddNote = () => {
    window.location.href = '/dashboard/notes/new'
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-background/95 backdrop-blur-md rounded-xl border border-border/30 shadow-sm p-6"
    >
      {/* Top Row - Title and Main Navigation */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-r from-primary to-secondary rounded-xl flex items-center justify-center shadow-lg">
              <CalendarIcon className="w-5 h-5 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-foreground">التقويم</h1>
              <p className="text-sm text-muted-foreground">إدارة المواعيد والأحداث</p>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Button
            onClick={handleAddNote}
            className="bg-gradient-to-r from-primary to-secondary hover:opacity-90 text-white px-6 py-2 rounded-lg font-medium transition-all shadow-lg hover:shadow-xl"
          >
            <Plus className="w-4 h-4 ml-2" />
            إضافة حدث
          </Button>
        </div>
      </div>

      {/* Bottom Row - Controls */}
      <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4">
        {/* Left Section - Date Navigation */}
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2 bg-muted/30 rounded-lg p-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={handlePrevious}
              className="w-8 h-8 p-0 hover:bg-primary/10"
            >
              <ChevronRight className="w-4 h-4" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={handleToday}
              className="px-4 hover:bg-primary/10 font-medium"
            >
              اليوم
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={handleNext}
              className="w-8 h-8 p-0 hover:bg-primary/10"
            >
              <ChevronLeft className="w-4 h-4" />
            </Button>
          </div>

          <div className="text-lg font-semibold text-foreground bg-muted/20 px-4 py-2 rounded-lg">
            {formatCurrentDate(currentDate)}
          </div>
        </div>

        {/* Right Section - Search and View Toggle */}
        <div className="flex items-center gap-4 w-full lg:w-auto">
          <CalendarSearch
            value={searchQuery}
            onChange={onSearchChange || (() => {})}
            onFilter={onFilterChange}
          />

          <ViewToggle
            currentView={currentView}
            onViewChange={onViewChange}
          />
        </div>
      </div>
    </motion.div>
  )
}
