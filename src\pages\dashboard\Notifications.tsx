'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { DashboardLayout } from "@/components/dashboard/DashboardLayout"
import { NotificationsHeader } from '@/components/dashboard/notifications/NotificationsHeader'
import { NotificationsFilter } from '@/components/dashboard/notifications/NotificationsFilter'
import { UpcomingReminders } from '@/components/dashboard/notifications/UpcomingReminders'
import { CalendarEvents } from '@/components/dashboard/notifications/CalendarEvents'
import { SystemNotifications } from '@/components/dashboard/notifications/SystemNotifications'
import { AINotifications } from '@/components/dashboard/notifications/AINotifications'
import { UpgradeCTA } from '@/components/dashboard/notifications/UpgradeCTA'
import { NotificationsFooter } from '@/components/dashboard/notifications/NotificationsFooter'

// TypeScript interfaces
export interface NotificationItem {
  id: string
  type: 'reminder' | 'system' | 'calendar' | 'ai'
  title: string
  description?: string
  timestamp: Date
  isRead: boolean
  priority: 'low' | 'medium' | 'high'
  category?: string
  color?: string
  icon?: string
  actions?: NotificationAction[]
}

export interface NotificationAction {
  id: string
  label: string
  type: 'primary' | 'secondary' | 'danger'
  onClick: () => void
}

export interface ReminderItem extends NotificationItem {
  type: 'reminder'
  noteId: string
  noteTitle: string
  notePreview: string
  reminderAt: Date
  snoozeOptions: SnoozeOption[]
}

export interface SnoozeOption {
  label: string
  value: number // minutes
}

export interface CalendarEventItem {
  id: string
  title: string
  description?: string
  startDate: Date
  endDate: Date
  noteId?: string
  category: string
  color: string
}

export interface FilterState {
  activeTab: 'all' | 'reminders' | 'system' | 'events' | 'ai'
  searchQuery: string
  sortBy: 'date' | 'type' | 'priority'
  sortOrder: 'asc' | 'desc'
}

export interface NotificationStats {
  total: number
  reminders: number
  system: number
  events: number
  ai: number
  unread: number
}

export function Notifications() {
  const [isLoading, setIsLoading] = useState(true)
  const [notifications, setNotifications] = useState<NotificationItem[]>([])
  const [reminders, setReminders] = useState<ReminderItem[]>([])
  const [calendarEvents, setCalendarEvents] = useState<CalendarEventItem[]>([])
  const [filter, setFilter] = useState<FilterState>({
    activeTab: 'all',
    searchQuery: '',
    sortBy: 'date',
    sortOrder: 'desc'
  })
  const [stats, setStats] = useState<NotificationStats>({
    total: 0,
    reminders: 0,
    system: 0,
    events: 0,
    ai: 0,
    unread: 0
  })

  // Mock data loading
  useEffect(() => {
    const loadMockData = async () => {
      setIsLoading(true)
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Mock reminders data
      const mockReminders: ReminderItem[] = [
        {
          id: '1',
          type: 'reminder',
          title: 'مراجعة مشروع التطبيق',
          description: 'تذكير لمراجعة التقدم في مشروع التطبيق الجديد',
          noteId: 'note-1',
          noteTitle: 'مشروع التطبيق الجديد',
          notePreview: 'اليوم أحرزت تقدماً جيداً في تطوير واجهة المستخدم...',
          timestamp: new Date(),
          reminderAt: new Date(Date.now() + 30 * 60 * 1000), // 30 minutes from now
          isRead: false,
          priority: 'high',
          category: 'عمل',
          color: '#ef4444',
          snoozeOptions: [
            { label: '10 دقائق', value: 10 },
            { label: '30 دقيقة', value: 30 },
            { label: 'ساعة واحدة', value: 60 },
            { label: 'غداً', value: 1440 }
          ]
        },
        {
          id: '2',
          type: 'reminder',
          title: 'موعد الطبيب',
          description: 'لا تنس موعد الفحص الدوري',
          noteId: 'note-2',
          noteTitle: 'المواعيد الطبية',
          notePreview: 'حجزت موعداً للفحص الدوري يوم الثلاثاء...',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
          reminderAt: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
          isRead: true,
          priority: 'medium',
          category: 'صحة',
          color: '#22c55e',
          snoozeOptions: [
            { label: '10 دقائق', value: 10 },
            { label: '30 دقيقة', value: 30 },
            { label: 'ساعة واحدة', value: 60 },
            { label: 'غداً', value: 1440 }
          ]
        }
      ]

      // Mock calendar events
      const mockEvents: CalendarEventItem[] = [
        {
          id: '1',
          title: 'اجتماع فريق العمل',
          description: 'مناقشة خطة العمل للأسبوع القادم',
          startDate: new Date(Date.now() + 4 * 60 * 60 * 1000), // 4 hours from now
          endDate: new Date(Date.now() + 5 * 60 * 60 * 1000), // 5 hours from now
          noteId: 'note-3',
          category: 'عمل',
          color: '#3b82f6'
        },
        {
          id: '2',
          title: 'جلسة تأمل',
          description: 'وقت للاسترخاء والتأمل',
          startDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // tomorrow
          endDate: new Date(Date.now() + 24 * 60 * 60 * 1000 + 30 * 60 * 1000), // tomorrow + 30 min
          category: 'شخصي',
          color: '#8b5cf6'
        }
      ]

      setReminders(mockReminders)
      setCalendarEvents(mockEvents)
      
      // Calculate stats
      const totalReminders = mockReminders.length
      const totalEvents = mockEvents.length
      const unreadCount = mockReminders.filter(r => !r.isRead).length
      
      setStats({
        total: totalReminders + totalEvents + 3 + 2, // +3 system, +2 AI
        reminders: totalReminders,
        system: 3,
        events: totalEvents,
        ai: 2,
        unread: unreadCount + 2 // +2 for system notifications
      })
      
      setIsLoading(false)
    }

    loadMockData()
  }, [])

  // Filter and sort notifications
  const filteredData = {
    reminders: reminders.filter(item => {
      if (filter.activeTab !== 'all' && filter.activeTab !== 'reminders') return false
      if (filter.searchQuery) {
        return item.title.toLowerCase().includes(filter.searchQuery.toLowerCase()) ||
               item.noteTitle.toLowerCase().includes(filter.searchQuery.toLowerCase())
      }
      return true
    }),
    events: calendarEvents.filter(item => {
      if (filter.activeTab !== 'all' && filter.activeTab !== 'events') return false
      if (filter.searchQuery) {
        return item.title.toLowerCase().includes(filter.searchQuery.toLowerCase())
      }
      return true
    })
  }

  const handleFilterChange = (newFilter: Partial<FilterState>) => {
    setFilter(prev => ({ ...prev, ...newFilter }))
  }

  const handleReminderAction = (reminderId: string, action: string) => {
    console.log(`Reminder ${reminderId} action: ${action}`)
    // Handle reminder actions (done, snooze, edit, delete)
  }

  const handleNotificationRead = (notificationId: string) => {
    console.log(`Mark notification ${notificationId} as read`)
    // Handle marking notification as read
  }

  return (
    <DashboardLayout>
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="container mx-auto px-4 py-6 space-y-6"
        >
          {/* Header */}
          <NotificationsHeader 
            stats={stats}
            isLoading={isLoading}
          />

          {/* Filter */}
          <NotificationsFilter
            filter={filter}
            onFilterChange={handleFilterChange}
            isLoading={isLoading}
          />

          {/* Main Content */}
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
            {/* Left Column - Main Notifications */}
            <div className="xl:col-span-2 space-y-6">
              {/* Upcoming Reminders */}
              {(filter.activeTab === 'all' || filter.activeTab === 'reminders') && (
                <UpcomingReminders
                  reminders={filteredData.reminders}
                  onAction={handleReminderAction}
                  isLoading={isLoading}
                />
              )}

              {/* Calendar Events */}
              {(filter.activeTab === 'all' || filter.activeTab === 'events') && (
                <CalendarEvents
                  events={filteredData.events}
                  isLoading={isLoading}
                />
              )}

              {/* System Notifications */}
              {(filter.activeTab === 'all' || filter.activeTab === 'system') && (
                <SystemNotifications
                  onRead={handleNotificationRead}
                  isLoading={isLoading}
                />
              )}

              {/* AI Notifications */}
              {(filter.activeTab === 'all' || filter.activeTab === 'ai') && (
                <AINotifications
                  onRead={handleNotificationRead}
                  isLoading={isLoading}
                />
              )}
            </div>

            {/* Right Column - Sidebar */}
            <div className="space-y-6">
              {/* Upgrade CTA */}
              <UpgradeCTA />
            </div>
          </div>

          {/* Footer */}
          <NotificationsFooter />
        </motion.div>
      </div>
    </DashboardLayout>
  )
}
