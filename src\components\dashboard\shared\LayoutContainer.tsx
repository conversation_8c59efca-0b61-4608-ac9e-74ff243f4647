import { motion, AnimatePresence } from "framer-motion"
import { ViewType } from "./ViewToggle"

interface LayoutContainerProps {
  viewType: ViewType
  children: React.ReactNode
  className?: string
}

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      duration: 0.3,
      staggerChildren: 0.1
    }
  }
}

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.3
    }
  }
}

export function LayoutContainer({ viewType, children, className = "" }: LayoutContainerProps) {
  const getLayoutClasses = () => {
    switch (viewType) {
      case 'grid':
        return "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
      
      case 'list':
        return "flex flex-col space-y-4"
      
      case 'compact':
        return "grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3"
      
      case 'masonry':
        return "columns-1 sm:columns-2 md:columns-3 lg:columns-4 xl:columns-5 gap-4 space-y-4"
      
      default:
        return "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
    }
  }

  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={viewType}
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        exit="hidden"
        className={`${getLayoutClasses()} ${className}`}
      >
        {Array.isArray(children) 
          ? children.map((child, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className={viewType === 'masonry' ? 'break-inside-avoid mb-4' : ''}
              >
                {child}
              </motion.div>
            ))
          : children
        }
      </motion.div>
    </AnimatePresence>
  )
}

// Helper component for individual items
export function LayoutItem({ 
  children, 
  viewType, 
  index = 0 
}: { 
  children: React.ReactNode
  viewType: ViewType
  index?: number 
}) {
  return (
    <motion.div
      variants={itemVariants}
      initial="hidden"
      animate="visible"
      transition={{ delay: index * 0.1 }}
      className={`
        ${viewType === 'masonry' ? 'break-inside-avoid mb-4' : ''}
        ${viewType === 'list' ? 'w-full' : ''}
      `}
    >
      {children}
    </motion.div>
  )
}
