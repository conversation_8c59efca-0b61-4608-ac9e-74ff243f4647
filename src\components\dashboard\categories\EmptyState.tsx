import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { 
  FolderOpen, 
  Plus, 
  Search,
  Sparkles
} from "lucide-react"

interface EmptyStateProps {
  hasCategories: boolean
  searchQuery: string
  onCreateCategory: () => void
}

export function EmptyState({ hasCategories, searchQuery, onCreateCategory }: EmptyStateProps) {
  if (!hasCategories) {
    // No categories at all - first time user
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="flex items-center justify-center min-h-[400px]"
      >
        <Card className="max-w-md w-full border-border/50 bg-gradient-to-br from-background to-muted/20">
          <CardContent className="p-8 text-center">
            {/* Animated Icon */}
            <motion.div
              animate={{ 
                rotate: [0, 5, -5, 0],
                scale: [1, 1.05, 1]
              }}
              transition={{ 
                duration: 3, 
                repeat: Infinity, 
                ease: "easeInOut" 
              }}
              className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-full mb-6"
            >
              <FolderOpen className="w-10 h-10 text-primary" />
            </motion.div>
            
            {/* Title */}
            <h3 className="text-xl font-bold text-foreground mb-3">
              ابدأ بإنشاء فئتك الأولى! 🎯
            </h3>
            
            {/* Description */}
            <p className="text-muted-foreground mb-6 leading-relaxed">
              نظم ملاحظاتك بطريقة ذكية وجميلة. أنشئ فئات مخصصة بألوان وأيقونات مميزة لتسهيل الوصول إلى ملاحظاتك.
            </p>
            
            {/* Features List */}
            <div className="space-y-2 mb-6 text-sm text-muted-foreground">
              <div className="flex items-center space-x-2 space-x-reverse">
                <div className="w-2 h-2 bg-primary rounded-full" />
                <span>ألوان وأيقونات مخصصة</span>
              </div>
              <div className="flex items-center space-x-2 space-x-reverse">
                <div className="w-2 h-2 bg-secondary rounded-full" />
                <span>تنظيم ذكي للملاحظات</span>
              </div>
              <div className="flex items-center space-x-2 space-x-reverse">
                <div className="w-2 h-2 bg-accent rounded-full" />
                <span>سهولة في البحث والوصول</span>
              </div>
            </div>
            
            {/* Create Button */}
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Button
                onClick={onCreateCategory}
                className="bg-gradient-to-r from-primary to-secondary hover:opacity-90 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <Plus className="w-4 h-4 mr-2" />
                إنشاء فئة جديدة
              </Button>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>
    )
  }

  // Has categories but search returned no results
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="flex items-center justify-center min-h-[300px]"
    >
      <Card className="max-w-md w-full border-border/50 bg-gradient-to-br from-background to-muted/20">
        <CardContent className="p-8 text-center">
          {/* Search Icon */}
          <motion.div
            animate={{ 
              scale: [1, 1.1, 1]
            }}
            transition={{ 
              duration: 2, 
              repeat: Infinity, 
              ease: "easeInOut" 
            }}
            className="inline-flex items-center justify-center w-16 h-16 bg-muted/30 rounded-full mb-6"
          >
            <Search className="w-8 h-8 text-muted-foreground" />
          </motion.div>
          
          {/* Title */}
          <h3 className="text-lg font-bold text-foreground mb-3">
            لم نجد فئات مطابقة 🔍
          </h3>
          
          {/* Description */}
          <p className="text-muted-foreground mb-6">
            {searchQuery ? (
              <>
                لا توجد فئات تطابق البحث "<strong>{searchQuery}</strong>". 
                جرب كلمات مختلفة أو أنشئ فئة جديدة.
              </>
            ) : (
              'لا توجد فئات تطابق الفلتر المحدد. جرب فلتر مختلف أو أنشئ فئة جديدة.'
            )}
          </p>
          
          {/* Actions */}
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Button
                onClick={onCreateCategory}
                className="bg-gradient-to-r from-primary to-secondary hover:opacity-90 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <Plus className="w-4 h-4 mr-2" />
                إنشاء فئة جديدة
              </Button>
            </motion.div>
            
            <Button
              variant="outline"
              onClick={() => window.location.reload()}
              className="border-border/50 hover:bg-muted/50 rounded-xl"
            >
              <Sparkles className="w-4 h-4 mr-2" />
              إعادة تحميل
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
