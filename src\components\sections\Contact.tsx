
import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Mail, Phone, MapPin, Send } from "lucide-react"

export function Contact() {
  return (
    <section id="contact" className="py-20 bg-muted/30">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold arabic-heading mb-4">
            تواصل معنا
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto arabic-body">
            نحن هنا للإجابة على جميع استفساراتك ومساعدتك في رحلتك الرقمية
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Card className="border-border/50 bg-card/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-2xl arabic-heading">
                  أرسل لنا رسالة
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">الاسم</label>
                    <Input placeholder="اسمك الكامل" className="text-right" />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">البريد الإلكتروني</label>
                    <Input placeholder="<EMAIL>" className="text-right" dir="ltr" />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">الموضوع</label>
                  <Input placeholder="موضوع الرسالة" className="text-right" />
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">الرسالة</label>
                  <Textarea 
                    placeholder="اكتب رسالتك هنا..." 
                    className="min-h-32 text-right"
                    rows={6}
                  />
                </div>
                
                <Button className="w-full rounded-full group">
                  إرسال الرسالة
                  <Send className="mr-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Button>
              </CardContent>
            </Card>
          </motion.div>

          {/* Contact Info */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            <div>
              <h3 className="text-2xl font-bold arabic-heading mb-6">
                معلومات التواصل
              </h3>
              <p className="text-muted-foreground arabic-body mb-8 leading-relaxed">
                يسعدنا تواصلك معنا في أي وقت. فريقنا جاهز لمساعدتك 
                وتقديم أفضل الحلول لاحتياجاتك.
              </p>
            </div>

            <div className="space-y-6">
              {[
                {
                  icon: <Mail className="h-6 w-6" />,
                  title: "البريد الإلكتروني",
                  content: "<EMAIL>",
                  subtitle: "نرد عليك خلال 24 ساعة"
                },
                {
                  icon: <Phone className="h-6 w-6" />,
                  title: "الهاتف",
                  content: "+966 50 123 4567",
                  subtitle: "من السبت إلى الخميس 9ص-6م"
                },
                {
                  icon: <MapPin className="h-6 w-6" />,
                  title: "العنوان",
                  content: "الرياض، المملكة العربية السعودية",
                  subtitle: "مكتبنا الرئيسي"
                }
              ].map((item, index) => (
                <motion.div
                  key={item.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}
                  viewport={{ once: true }}
                  className="flex items-start gap-4 p-4 rounded-lg border border-border/50 bg-card/30 backdrop-blur-sm hover:bg-card/50 transition-colors"
                >
                  <div className="flex-shrink-0 w-12 h-12 rounded-full bg-primary/10 text-primary flex items-center justify-center">
                    {item.icon}
                  </div>
                  <div>
                    <h4 className="font-semibold arabic-heading mb-1">
                      {item.title}
                    </h4>
                    <p className="text-foreground mb-1">
                      {item.content}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {item.subtitle}
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
