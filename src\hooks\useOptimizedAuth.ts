import { useEffect, useState, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuthStore } from '@/stores/authStore'

interface UseOptimizedAuthOptions {
  redirectTo?: string
  requireAuth?: boolean
}

interface UseOptimizedAuthReturn {
  isAuthenticated: boolean
  user: any
  isLoading: boolean
  error: string | null
}

// Global state to track authentication initialization
let isAuthInitialized = false
let authInitPromise: Promise<void> | null = null
let authInitCallbacks: (() => void)[] = []

export function useOptimizedAuth(options: UseOptimizedAuthOptions = {}): UseOptimizedAuthReturn {
  const {
    redirectTo = '/auth/login',
    requireAuth = true
  } = options

  const navigate = useNavigate()
  const { isAuthenticated, user, error, initializeAuth } = useAuthStore()
  const [isLoading, setIsLoading] = useState(!isAuthInitialized)
  const hasInitialized = useRef(false)

  useEffect(() => {
    const initAuth = async () => {
      // If already initialized, just update loading state
      if (isAuthInitialized) {
        setIsLoading(false)
        return
      }

      // If initialization is in progress, wait for it
      if (authInitPromise) {
        await authInitPromise
        setIsLoading(false)
        return
      }

      // Start initialization
      setIsLoading(true)
      authInitPromise = initializeAuth()

      try {
        await authInitPromise
        isAuthInitialized = true
        
        // Notify all waiting callbacks
        authInitCallbacks.forEach(callback => callback())
        authInitCallbacks = []
      } catch (error) {
        console.error('Auth initialization failed:', error)
      } finally {
        setIsLoading(false)
        authInitPromise = null
      }
    }

    if (!hasInitialized.current) {
      hasInitialized.current = true
      initAuth()
    }
  }, [initializeAuth])

  // Handle authentication redirect
  useEffect(() => {
    if (!isLoading && requireAuth && !isAuthenticated) {
      console.log('User not authenticated, redirecting to:', redirectTo)
      navigate(redirectTo)
    }
  }, [isAuthenticated, isLoading, navigate, redirectTo, requireAuth])

  return {
    isAuthenticated,
    user,
    isLoading,
    error
  }
}

// Reset function for testing or logout
export function resetAuthState() {
  isAuthInitialized = false
  authInitPromise = null
  authInitCallbacks = []
}

// Function to wait for auth initialization
export function waitForAuthInit(): Promise<void> {
  return new Promise((resolve) => {
    if (isAuthInitialized) {
      resolve()
      return
    }

    if (authInitPromise) {
      authInitPromise.then(resolve)
      return
    }

    authInitCallbacks.push(resolve)
  })
}
