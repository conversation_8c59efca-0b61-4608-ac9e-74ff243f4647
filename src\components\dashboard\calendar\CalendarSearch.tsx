import { useState, useRef, useEffect } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Search,
  X,
  Filter,
  Calendar,
  Tag,
  Clock
} from 'lucide-react'

export interface CalendarSearchProps {
  value: string
  onChange: (value: string) => void
  onFilter?: (filters: SearchFilters) => void
}

export interface SearchFilters {
  categories: string[]
  priorities: string[]
  dateRange: {
    start?: Date
    end?: Date
  }
}

export function CalendarSearch({ value, onChange, onFilter }: CalendarSearchProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [showFilters, setShowFilters] = useState(false)
  const [filters, setFilters] = useState<SearchFilters>({
    categories: [],
    priorities: [],
    dateRange: {}
  })
  const inputRef = useRef<HTMLInputElement>(null)

  const handleClear = () => {
    onChange('')
    setIsExpanded(false)
    inputRef.current?.blur()
  }

  const handleFocus = () => {
    setIsExpanded(true)
  }

  const handleBlur = () => {
    if (!value) {
      setIsExpanded(false)
    }
  }

  const categories = ['عمل', 'شخصي', 'دراسة', 'صحة', 'عائلة']
  const priorities = ['عالية', 'متوسطة', 'منخفضة']

  const toggleCategory = (category: string) => {
    const newCategories = filters.categories.includes(category)
      ? filters.categories.filter(c => c !== category)
      : [...filters.categories, category]
    
    const newFilters = { ...filters, categories: newCategories }
    setFilters(newFilters)
    onFilter?.(newFilters)
  }

  const togglePriority = (priority: string) => {
    const newPriorities = filters.priorities.includes(priority)
      ? filters.priorities.filter(p => p !== priority)
      : [...filters.priorities, priority]
    
    const newFilters = { ...filters, priorities: newPriorities }
    setFilters(newFilters)
    onFilter?.(newFilters)
  }

  return (
    <div className="relative">
      <motion.div
        animate={{
          width: isExpanded ? 300 : 200
        }}
        transition={{ duration: 0.2 }}
        className="relative"
      >
        <div className="relative">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          <Input
            ref={inputRef}
            type="text"
            placeholder="البحث في الأحداث..."
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onFocus={handleFocus}
            onBlur={handleBlur}
            className="pr-10 pl-10 bg-background/50 border-border/50 focus:border-primary/50 transition-colors"
          />
          {value && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClear}
              className="absolute left-1 top-1/2 transform -translate-y-1/2 w-6 h-6 p-0 hover:bg-muted/50"
            >
              <X className="w-3 h-3" />
            </Button>
          )}
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowFilters(!showFilters)}
          className={`absolute left-8 top-1/2 transform -translate-y-1/2 w-6 h-6 p-0 transition-colors ${
            showFilters ? 'text-primary' : 'text-muted-foreground hover:text-foreground'
          }`}
        >
          <Filter className="w-3 h-3" />
        </Button>
      </motion.div>

      {/* Filters Dropdown */}
      <AnimatePresence>
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
            className="absolute top-full left-0 right-0 mt-2 bg-background border border-border/50 rounded-lg shadow-lg p-4 z-50"
          >
            <div className="space-y-4">
              {/* Categories */}
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <Tag className="w-4 h-4 text-muted-foreground" />
                  <span className="text-sm font-medium">الفئات</span>
                </div>
                <div className="flex flex-wrap gap-2">
                  {categories.map((category) => (
                    <Button
                      key={category}
                      variant={filters.categories.includes(category) ? "default" : "outline"}
                      size="sm"
                      onClick={() => toggleCategory(category)}
                      className="text-xs"
                    >
                      {category}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Priorities */}
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <Clock className="w-4 h-4 text-muted-foreground" />
                  <span className="text-sm font-medium">الأولوية</span>
                </div>
                <div className="flex flex-wrap gap-2">
                  {priorities.map((priority) => (
                    <Button
                      key={priority}
                      variant={filters.priorities.includes(priority) ? "default" : "outline"}
                      size="sm"
                      onClick={() => togglePriority(priority)}
                      className="text-xs"
                    >
                      {priority}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
