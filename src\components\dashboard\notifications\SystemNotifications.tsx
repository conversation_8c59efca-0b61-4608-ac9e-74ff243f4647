'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  AlertTriangle, 
  UserPlus, 
  Sync, 
  CheckCircle2, 
  X, 
  Eye, 
  Clock,
  Shield,
  Settings,
  Wifi,
  WifiOff
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

interface SystemNotification {
  id: string
  type: 'invite' | 'sync_error' | 'security' | 'update' | 'maintenance'
  title: string
  description: string
  timestamp: Date
  isRead: boolean
  priority: 'low' | 'medium' | 'high'
  actions?: NotificationAction[]
}

interface NotificationAction {
  id: string
  label: string
  type: 'primary' | 'secondary' | 'danger'
  onClick: () => void
}

interface SystemNotificationsProps {
  onRead: (notificationId: string) => void
  isLoading: boolean
}

interface NotificationCardProps {
  notification: SystemNotification
  onRead: (id: string) => void
  index: number
}

function NotificationCard({ notification, onRead, index }: NotificationCardProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  const getIcon = (type: string) => {
    switch (type) {
      case 'invite':
        return <UserPlus className="w-5 h-5" />
      case 'sync_error':
        return <WifiOff className="w-5 h-5" />
      case 'security':
        return <Shield className="w-5 h-5" />
      case 'update':
        return <Settings className="w-5 h-5" />
      case 'maintenance':
        return <AlertTriangle className="w-5 h-5" />
      default:
        return <AlertTriangle className="w-5 h-5" />
    }
  }

  const getIconColor = (type: string) => {
    switch (type) {
      case 'invite':
        return 'text-blue-600 bg-blue-100 dark:bg-blue-900/30'
      case 'sync_error':
        return 'text-red-600 bg-red-100 dark:bg-red-900/30'
      case 'security':
        return 'text-orange-600 bg-orange-100 dark:bg-orange-900/30'
      case 'update':
        return 'text-green-600 bg-green-100 dark:bg-green-900/30'
      case 'maintenance':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30'
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30'
    }
  }

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'invite':
        return 'دعوة للمشاركة'
      case 'sync_error':
        return 'خطأ في المزامنة'
      case 'security':
        return 'تنبيه أمني'
      case 'update':
        return 'تحديث النظام'
      case 'maintenance':
        return 'صيانة مجدولة'
      default:
        return 'تنبيه النظام'
    }
  }

  const formatTimeAgo = (date: Date) => {
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMinutes = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    if (diffMinutes < 1) return 'الآن'
    if (diffMinutes < 60) return `منذ ${diffMinutes} دقيقة`
    if (diffHours < 24) return `منذ ${diffHours} ساعة`
    return `منذ ${diffDays} يوم`
  }

  const handleMarkAsRead = () => {
    if (!notification.isRead) {
      onRead(notification.id)
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className={`
        bg-background rounded-xl border border-border/50 shadow-sm hover:shadow-md transition-all duration-300 p-6 group cursor-pointer
        ${!notification.isRead ? 'ring-2 ring-primary/20 bg-primary/5' : ''}
      `}
      onClick={() => setIsExpanded(!isExpanded)}
    >
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-4 flex-1">
            <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${getIconColor(notification.type)}`}>
              {getIcon(notification.type)}
            </div>
            
            <div className="flex-1 space-y-2">
              <div className="flex items-center gap-3">
                <h3 className="font-semibold text-foreground group-hover:text-primary transition-colors">
                  {notification.title}
                </h3>
                {!notification.isRead && (
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                )}
              </div>
              
              <p className="text-sm text-muted-foreground line-clamp-2">
                {notification.description}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              {getTypeLabel(notification.type)}
            </Badge>
          </div>
        </div>

        {/* Timestamp */}
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2 text-muted-foreground">
            <Clock className="w-4 h-4" />
            <span>{formatTimeAgo(notification.timestamp)}</span>
          </div>
          
          {!notification.isRead && (
            <Button
              size="sm"
              variant="ghost"
              onClick={(e) => {
                e.stopPropagation()
                handleMarkAsRead()
              }}
              className="text-xs"
            >
              تم القراءة
            </Button>
          )}
        </div>

        {/* Actions */}
        {notification.actions && notification.actions.length > 0 && (
          <div className="flex items-center gap-2 pt-2 border-t border-border/30">
            {notification.actions.map((action) => (
              <Button
                key={action.id}
                size="sm"
                variant={action.type === 'primary' ? 'default' : action.type === 'danger' ? 'destructive' : 'outline'}
                onClick={(e) => {
                  e.stopPropagation()
                  action.onClick()
                }}
                className="text-xs"
              >
                {action.label}
              </Button>
            ))}
          </div>
        )}
      </div>
    </motion.div>
  )
}

function EmptyState() {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
      className="text-center py-12"
    >
      <div className="w-24 h-24 mx-auto mb-6 bg-muted/30 rounded-full flex items-center justify-center">
        <CheckCircle2 className="w-12 h-12 text-green-500" />
      </div>
      <h3 className="text-lg font-semibold text-foreground mb-2">
        لا توجد تنبيهات نظام
      </h3>
      <p className="text-muted-foreground max-w-md mx-auto">
        جميع تنبيهات النظام محدثة. سنقوم بإعلامك عند وجود أي تحديثات مهمة.
      </p>
    </motion.div>
  )
}

function LoadingSkeleton() {
  return (
    <div className="space-y-4">
      {Array.from({ length: 3 }).map((_, i) => (
        <motion.div
          key={i}
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: i * 0.1 }}
          className="bg-background rounded-xl border border-border/50 shadow-sm p-6"
        >
          <div className="flex items-start gap-4">
            <div className="w-10 h-10 bg-muted/50 rounded-lg animate-pulse"></div>
            <div className="flex-1 space-y-2">
              <div className="h-5 bg-muted/50 rounded animate-pulse w-3/4"></div>
              <div className="h-4 bg-muted/50 rounded animate-pulse w-1/2"></div>
              <div className="h-4 bg-muted/50 rounded animate-pulse w-1/4"></div>
            </div>
            <div className="h-6 bg-muted/50 rounded animate-pulse w-20"></div>
          </div>
        </motion.div>
      ))}
    </div>
  )
}

export function SystemNotifications({ onRead, isLoading }: SystemNotificationsProps) {
  // Mock system notifications
  const [notifications] = useState<SystemNotification[]>([
    {
      id: '1',
      type: 'invite',
      title: 'دعوة للمشاركة في مساحة العمل',
      description: 'تم دعوتك للانضمام إلى مساحة عمل "فريق التطوير" من قبل أحمد محمد.',
      timestamp: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
      isRead: false,
      priority: 'medium',
      actions: [
        {
          id: 'accept',
          label: 'قبول',
          type: 'primary',
          onClick: () => console.log('Accept invitation')
        },
        {
          id: 'decline',
          label: 'رفض',
          type: 'secondary',
          onClick: () => console.log('Decline invitation')
        }
      ]
    },
    {
      id: '2',
      type: 'sync_error',
      title: 'فشل في مزامنة البيانات',
      description: 'حدث خطأ أثناء مزامنة ملاحظاتك مع السحابة. يرجى المحاولة مرة أخرى.',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      isRead: false,
      priority: 'high',
      actions: [
        {
          id: 'retry',
          label: 'إعادة المحاولة',
          type: 'primary',
          onClick: () => console.log('Retry sync')
        },
        {
          id: 'view_details',
          label: 'عرض التفاصيل',
          type: 'secondary',
          onClick: () => console.log('View sync details')
        }
      ]
    },
    {
      id: '3',
      type: 'security',
      title: 'تسجيل دخول من جهاز جديد',
      description: 'تم تسجيل الدخول إلى حسابك من جهاز جديد في الرياض، السعودية.',
      timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
      isRead: true,
      priority: 'medium',
      actions: [
        {
          id: 'secure_account',
          label: 'تأمين الحساب',
          type: 'primary',
          onClick: () => console.log('Secure account')
        },
        {
          id: 'view_activity',
          label: 'عرض النشاط',
          type: 'secondary',
          onClick: () => console.log('View activity')
        }
      ]
    }
  ])

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.4 }}
      className="space-y-6"
    >
      {/* Section Header */}
      <div className="flex items-center gap-3">
        <div className="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
          <AlertTriangle className="w-4 h-4 text-white" />
        </div>
        <div>
          <h2 className="text-xl font-bold text-foreground">تنبيهات النظام</h2>
          <p className="text-sm text-muted-foreground">
            تحديثات مهمة حول حسابك والنظام
          </p>
        </div>
      </div>

      {/* Content */}
      {isLoading ? (
        <LoadingSkeleton />
      ) : notifications.length === 0 ? (
        <EmptyState />
      ) : (
        <div className="space-y-4">
          {notifications.map((notification, index) => (
            <NotificationCard
              key={notification.id}
              notification={notification}
              onRead={onRead}
              index={index}
            />
          ))}
        </div>
      )}
    </motion.div>
  )
}
