import { useState } from "react"
import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useAuthStore } from "@/stores/authStore"
import { supabase } from "@/lib/supabase"
import { toast } from "sonner"

export function AuthTest() {
  const [testEmail, setTestEmail] = useState('<EMAIL>')
  const [testPassword, setTestPassword] = useState('Test123!')
  const { 
    isAuthenticated, 
    user, 
    login, 
    signInWithGoogle, 
    logout, 
    isLoading,
    error 
  } = useAuthStore()

  const handleTestLogin = async () => {
    const result = await login(testEmail, testPassword)
    if (result.success) {
      toast.success("تم تسجيل الدخول بنجاح!")
    } else {
      toast.error(`فشل تسجيل الدخول: ${result.error}`)
    }
  }

  const handleTestGoogleLogin = async () => {
    const result = await signInWithGoogle()
    if (!result.success) {
      toast.error(`فشل تسجيل الدخول مع Google: ${result.error}`)
    }
  }

  const handleTestLogout = async () => {
    await logout()
    toast.success("تم تسجيل الخروج بنجاح!")
  }

  const testSupabaseConnection = async () => {
    try {
      const { data, error } = await supabase.from('profiles').select('count').limit(1)
      if (error) throw error
      toast.success("اتصال Supabase يعمل بشكل صحيح!")
    } catch (error: any) {
      toast.error(`خطأ في اتصال Supabase: ${error.message}`)
    }
  }

  return (
    <div className="min-h-screen bg-background p-8" dir="rtl">
      <div className="max-w-4xl mx-auto space-y-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <h1 className="text-3xl font-bold text-foreground mb-2">
            اختبار نظام المصادقة - Nots
          </h1>
          <p className="text-muted-foreground">
            اختبار شامل لجميع وظائف المصادقة مع Supabase
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Authentication Status */}
          <Card>
            <CardHeader>
              <CardTitle>حالة المصادقة</CardTitle>
              <CardDescription>معلومات المستخدم الحالي</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2 space-x-reverse">
                <div className={`w-3 h-3 rounded-full ${isAuthenticated ? 'bg-green-500' : 'bg-red-500'}`} />
                <span className="font-medium">
                  {isAuthenticated ? 'مسجل الدخول' : 'غير مسجل'}
                </span>
              </div>
              
              {user && (
                <div className="space-y-2 p-3 bg-muted rounded-lg">
                  <p><strong>الاسم:</strong> {user.fullName}</p>
                  <p><strong>البريد:</strong> {user.email}</p>
                  <p><strong>الخطة:</strong> {user.plan}</p>
                  <p><strong>المعرف:</strong> {user.id}</p>
                </div>
              )}
              
              {error && (
                <div className="p-3 bg-red-100 border border-red-300 rounded-lg text-red-700">
                  <strong>خطأ:</strong> {error}
                </div>
              )}
              
              {isLoading && (
                <div className="flex items-center space-x-2 space-x-reverse text-primary">
                  <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />
                  <span>جاري المعالجة...</span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Test Login */}
          <Card>
            <CardHeader>
              <CardTitle>اختبار تسجيل الدخول</CardTitle>
              <CardDescription>اختبار تسجيل الدخول بالبريد وكلمة المرور</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="testEmail">البريد الإلكتروني</Label>
                <Input
                  id="testEmail"
                  type="email"
                  value={testEmail}
                  onChange={(e) => setTestEmail(e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="testPassword">كلمة المرور</Label>
                <Input
                  id="testPassword"
                  type="password"
                  value={testPassword}
                  onChange={(e) => setTestPassword(e.target.value)}
                  placeholder="Test123!"
                />
              </div>
              
              <div className="space-y-2">
                <Button 
                  onClick={handleTestLogin}
                  disabled={isLoading || isAuthenticated}
                  className="w-full"
                >
                  اختبار تسجيل الدخول
                </Button>
                
                <Button 
                  onClick={handleTestGoogleLogin}
                  disabled={isLoading || isAuthenticated}
                  variant="outline"
                  className="w-full"
                >
                  اختبار Google OAuth
                </Button>
                
                <Button 
                  onClick={handleTestLogout}
                  disabled={isLoading || !isAuthenticated}
                  variant="destructive"
                  className="w-full"
                >
                  تسجيل الخروج
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Supabase Connection Test */}
          <Card>
            <CardHeader>
              <CardTitle>اختبار اتصال Supabase</CardTitle>
              <CardDescription>التحقق من اتصال قاعدة البيانات</CardDescription>
            </CardHeader>
            <CardContent>
              <Button 
                onClick={testSupabaseConnection}
                disabled={isLoading}
                className="w-full"
              >
                اختبار الاتصال
              </Button>
            </CardContent>
          </Card>

          {/* Navigation Links */}
          <Card>
            <CardHeader>
              <CardTitle>روابط المصادقة</CardTitle>
              <CardDescription>اختبار جميع صفحات المصادقة</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button 
                onClick={() => window.location.href = '/auth/signup'}
                variant="outline"
                className="w-full"
              >
                صفحة التسجيل
              </Button>
              
              <Button 
                onClick={() => window.location.href = '/auth/login'}
                variant="outline"
                className="w-full"
              >
                صفحة تسجيل الدخول
              </Button>
              
              <Button 
                onClick={() => window.location.href = '/auth/forgot'}
                variant="outline"
                className="w-full"
              >
                نسيت كلمة المرور
              </Button>
              
              <Button 
                onClick={() => window.location.href = '/'}
                variant="outline"
                className="w-full"
              >
                الصفحة الرئيسية
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
