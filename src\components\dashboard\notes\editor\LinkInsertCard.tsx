import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  Link as LinkIcon, 
  X, 
  ExternalLink,
  Check,
  AlertCircle
} from "lucide-react"
import { toast } from "sonner"

interface LinkInsertCardProps {
  isOpen: boolean
  onClose: () => void
  onLinkInsert: (href: string, text?: string) => void
  selectedText?: string
}

export function LinkInsertCard({ 
  isOpen, 
  onClose, 
  onLinkInsert, 
  selectedText 
}: LinkInsertCardProps) {
  const [linkUrl, setLinkUrl] = useState('')
  const [linkText, setLinkText] = useState('')
  const [isValidUrl, setIsValidUrl] = useState(false)

  useEffect(() => {
    if (selectedText) {
      setLinkText(selectedText)
    }
  }, [selectedText])

  useEffect(() => {
    // Validate URL
    if (!linkUrl.trim()) {
      setIsValidUrl(false)
      return
    }

    try {
      new URL(linkUrl)
      setIsValidUrl(true)
    } catch {
      // Try adding https:// if it's missing
      try {
        new URL(`https://${linkUrl}`)
        setIsValidUrl(true)
      } catch {
        setIsValidUrl(false)
      }
    }
  }, [linkUrl])

  const handleSubmit = () => {
    if (!linkUrl.trim()) {
      toast.error('يرجى إدخال رابط صحيح')
      return
    }

    let finalUrl = linkUrl.trim()
    
    // Add https:// if missing
    if (!finalUrl.startsWith('http://') && !finalUrl.startsWith('https://')) {
      finalUrl = `https://${finalUrl}`
    }

    // Validate final URL
    try {
      new URL(finalUrl)
      onLinkInsert(finalUrl, linkText.trim() || finalUrl)
      handleClose()
    } catch {
      toast.error('رابط غير صحيح')
    }
  }

  const handleClose = () => {
    setLinkUrl('')
    setLinkText('')
    setIsValidUrl(false)
    onClose()
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && isValidUrl) {
      handleSubmit()
    }
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={handleClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          transition={{ duration: 0.2 }}
          onClick={(e) => e.stopPropagation()}
          className="w-full max-w-md"
        >
          <Card className="border-border/50 shadow-xl">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg font-semibold flex items-center gap-2">
                  <LinkIcon className="w-5 h-5 text-primary" />
                  إدراج رابط
                </CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClose}
                  className="w-8 h-8 p-0 rounded-full"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="link-url">الرابط</Label>
                <div className="relative">
                  <Input
                    id="link-url"
                    value={linkUrl}
                    onChange={(e) => setLinkUrl(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="https://example.com أو example.com"
                    dir="ltr"
                    className="pr-10"
                  />
                  <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                    {linkUrl.trim() && (
                      isValidUrl ? (
                        <Check className="w-4 h-4 text-green-500" />
                      ) : (
                        <AlertCircle className="w-4 h-4 text-red-500" />
                      )
                    )}
                  </div>
                </div>
                {linkUrl.trim() && !isValidUrl && (
                  <p className="text-xs text-red-500 flex items-center gap-1">
                    <AlertCircle className="w-3 h-3" />
                    رابط غير صحيح
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="link-text">نص الرابط (اختياري)</Label>
                <Input
                  id="link-text"
                  value={linkText}
                  onChange={(e) => setLinkText(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="النص الذي سيظهر للرابط..."
                  dir="rtl"
                />
                <p className="text-xs text-muted-foreground">
                  إذا تُرك فارغاً، سيتم استخدام الرابط كنص
                </p>
              </div>

              {/* Preview */}
              {linkUrl.trim() && isValidUrl && (
                <div className="p-3 bg-muted/30 rounded-lg border border-border/30">
                  <p className="text-xs text-muted-foreground mb-1">معاينة:</p>
                  <div className="flex items-center gap-2">
                    <ExternalLink className="w-4 h-4 text-primary" />
                    <span className="text-sm text-primary underline">
                      {linkText.trim() || linkUrl}
                    </span>
                  </div>
                </div>
              )}

              <div className="flex gap-2 pt-2">
                <Button
                  onClick={handleSubmit}
                  disabled={!isValidUrl}
                  className="flex-1"
                >
                  إدراج الرابط
                </Button>
                <Button
                  variant="outline"
                  onClick={handleClose}
                >
                  إلغاء
                </Button>
              </div>

              {/* Quick suggestions */}
              <div className="space-y-2">
                <p className="text-xs text-muted-foreground">اقتراحات سريعة:</p>
                <div className="flex flex-wrap gap-2">
                  {[
                    'google.com',
                    'youtube.com',
                    'github.com',
                    'stackoverflow.com'
                  ].map((suggestion) => (
                    <Button
                      key={suggestion}
                      variant="outline"
                      size="sm"
                      onClick={() => setLinkUrl(suggestion)}
                      className="text-xs h-7"
                    >
                      {suggestion}
                    </Button>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}
